amqp==5.1.1
async-timeout==4.0.2
attrs==21.4.0
autopep8==2.0.2
backports.zoneinfo==0.2.1
bcrypt==3.2.0
billiard==4.1.0
bitarray==2.6.2
blinker==1.4
boto3==1.10.37
botocore==1.13.50
cached-property==1.5.2
celery==5.3.1
certifi==2020.12.5
cffi==1.14.5
chardet==3.0.4
charset-normalizer==2.1.1
Cheetah3==3.2.6.post2
click==8.1.3
click-didyoumean==0.3.0
click-plugins==1.1.1
click-repl==0.2.0
cryptography==3.4.7
decorator==5.1.0
Deprecated==1.2.13
dnspython==2.2.1
docopt==0.6.2
docutils==0.15.2
eventlet==0.33.1
fastjsonschema==2.15.1
Flask==1.1.1
Flask-Cors==3.0.10
Flask-DebugToolbar==0.11.0
flask-pytest==0.0.5
Flask-SQLAlchemy==2.4.1
flower==1.0.0
future==0.18.2
gevent==1.4.0
gmssl==3.2.1
greenlet==1.1.2
gssapi==1.7.2
gunicorn==19.9.0
hdfs==2.7.0
humanize==3.14.0
idna==2.8
impala==0.2
importlib-metadata==4.8.3
importlib-resources==5.9.0
impyla==0.18.0
inflect==5.3.0
iniconfig==1.1.1
ipaddress==1.0.23
itsdangerous==2.0.0
Jinja2==2.11.3
jmespath==0.10.0
kms-client-sdk==0.2.0
kms-tls-sdk==0.2.2
kombu==5.3.1
krbcontext==0.10
ldap3==2.9.1
loguru==0.5.3
Mako==1.2.1
MarkupSafe==2.0.0
mmh3==3.0.0
mo-dots==4.22.21108
mo-future==3.147.20327
mo-imports==3.149.20327
mo-kwargs==4.22.21108
mo-logs==4.23.21108
moz-sql-parser==4.40.21126
mtthrift==2.1.2
numpy==1.22.3
octo-mns==0.0.1
octo-rpc==0.2.22
packaging==21.3
pandas==0.25.3
paramiko==2.7.2
pluggy==1.0.0
ply==3.11
prometheus-client==0.14.1
prompt-toolkit==3.0.3
psutil==5.8.0
pure-sasl==0.6.2
py==1.11.0
pyasn1==0.4.8
pycodestyle==2.10.0
pycparser==2.20
pyftpdlib==1.5.6
PyHive==0.6.4
pyhocon==0.3.59
PyMySQL==1.0.2
PyNaCl==1.4.0
pyOpenSSL==20.0.1
pyparsing==2.4.7
pypinyin==0.46.0
pytalos==0.1.6
pytest==7.1.2
pytest-html==3.1.1
pytest-metadata==2.0.2
python-cat==0.0.8
python-crontab==2.5.1
python-dateutil==2.8.2
pytz==2021.1
redis==4.2.2
requests==2.28.1
requests-toolbelt==0.9.1
retrying==1.3.3
ruamel.yaml==0.16.13
ruamel.yaml.clib==0.2.6
s3transfer==0.2.1
sasl==0.3.1
six==1.16.0
snowland-smx==0.3.1
sqlacodegen==2.3.0
SQLAlchemy==1.2.5
sqlparse==0.4.2
thrift==0.16.0
thrift-sasl==0.4.3
thriftpy2==0.4.14
tomli==2.0.1
tornado==6.1
tqdm==4.64.1
typing_extensions==4.1.1
tzdata==2023.3
urllib3==1.25.11
vine==5.0.0
watchdog==2.1.6
wcwidth==0.2.5
Werkzeug==1.0.1
wrapt==1.14.0
xlrd==1.2.0
xlwt==1.3.0
zipp==3.6.0
mysql-connector-python~=9.0.0
markdown==3.4.1
pygments==2.14.0