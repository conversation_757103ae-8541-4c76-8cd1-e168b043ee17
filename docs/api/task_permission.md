# 任务权限管理接口文档

本文档描述了与任务权限管理相关的所有API接口。这些接口用于管理用户对任务的访问权限。

## 接口列表

### 1. 获取任务权限列表
**接口**: `/`  
**方法**: `GET`  
**描述**: 获取指定任务的所有权限配置  
**权限要求**: 需要登录  

**查询参数**:

| 参数名 | 类型 | 必填 | 描述 |
|-------|------|------|------|
| task_id | integer | 是 | 任务ID |

**响应示例**:
```json
{
    "code": 0,
    "msg": "success",
    "data": {
        "task_id": 1,
        "permissions": [
            {
                "id": 1,
                "user_id": "user001",
                "permission": 7,
                "status": "ENABLE",
                "create_time": "2024-01-20 10:00:00",
                "update_time": "2024-01-20 10:00:00"
            }
        ]
    }
}
```

### 2. 添加任务权限
**接口**: `/`  
**方法**: `POST`  
**描述**: 为指定任务添加用户权限  
**权限要求**: 需要登录  

**请求参数**:

| 参数名 | 类型 | 必填 | 描述 |
|-------|------|------|------|
| task_id | integer | 是 | 任务ID |
| permission | object | 是 | 权限配置对象 |

**权限配置对象格式**:
```json
{
    "read": true,
    "write": true,
    "execute": true
}
```

**响应示例**:
```json
{
    "code": 0,
    "msg": "success",
    "data": {
        "id": 1
    }
}
```

### 3. 获取权限详情
**接口**: `/<id>`  
**方法**: `GET`  
**描述**: 获取指定ID的权限配置详情  
**权限要求**: 需要登录  

**路径参数**:

| 参数名 | 类型 | 描述 |
|-------|------|------|
| id | integer | 权限配置ID |

**响应示例**:
```json
{
    "code": 0,
    "msg": "success",
    "data": {
        "id": 1,
        "user_id": "user001",
        "task_id": 1,
        "permission": 7,
        "status": "ENABLE",
        "create_time": "2024-01-20 10:00:00",
        "update_time": "2024-01-20 10:00:00"
    }
}
```

### 4. 修改权限配置
**接口**: `/<id>`  
**方法**: `POST`  
**描述**: 修改指定ID的权限配置  
**权限要求**: 需要登录  

**路径参数**:

| 参数名 | 类型 | 描述 |
|-------|------|------|
| id | integer | 权限配置ID |

**请求参数**:

| 参数名 | 类型 | 必填 | 描述 |
|-------|------|------|------|
| permission | object | 是 | 新的权限配置对象 |

**响应示例**:
```json
{
    "code": 0,
    "msg": "success",
    "data": null
}
```

### 5. 审批权限申请
**接口**: `/approve/<id>`  
**方法**: `POST`  
**描述**: 审批权限申请  
**权限要求**: 需要登录，且必须是项目管理员  

**路径参数**:

| 参数名 | 类型 | 描述 |
|-------|------|------|
| id | integer | 权限配置ID |

**请求参数**:

| 参数名 | 类型 | 必填 | 描述 |
|-------|------|------|------|
| action | integer | 是 | 审批动作(1:通过, 2:拒绝) |

**响应示例**:
```json
{
    "code": 0,
    "msg": "success",
    "data": null
}
```

### 6. 删除权限配置
**接口**: `/<id>`  
**方法**: `DELETE`  
**描述**: 删除指定ID的权限配置  
**权限要求**: 需要登录，且必须是项目管理员  

**路径参数**:

| 参数名 | 类型 | 描述 |
|-------|------|------|
| id | integer | 权限配置ID |

**响应示例**:
```json
{
    "code": 0,
    "msg": "success",
    "data": null
}
```

## 权限状态说明

| 状态 | 描述 |
|------|------|
| APPROVING | 审批中 |
| ENABLE | 已启用 |
| REJECT | 已拒绝 |

## 权限编码说明

权限采用Linux权限模型，使用数字表示：
- 4: 读取权限(read)
- 2: 写入权限(write)
- 1: 执行权限(execute)
- 0: 无权限

权限数字为以上数值的组合，例如：
- 7 = 4 + 2 + 1: 所有权限
- 5 = 4 + 1: 读取和执行权限
- 6 = 4 + 2: 读取和写入权限

## 错误码说明

所有接口可能返回的错误码：

| 错误码 | 描述 |
|-------|------|
| 0 | 成功 |
| 400 | 参数错误 |
| 403 | 无权限 |
| 404 | 资源不存在 |
| 409 | 资源已存在 |
| 500 | 内部服务器错误 |

## 注意事项

1. 所有接口都需要用户登录
2. 权限申请需要项目管理员审批
3. 非项目管理员修改权限配置时会自动进入审批流程
4. 权限变更会触发通知机制
5. 删除权限配置前需要确认不会影响现有业务
6. 建议定期审查权限配置，及时清理无效配置
7. 权限配置的修改会记录在系统日志中
8. 用户不能同时拥有多个针对同一任务的权限配置 