# 任务类型管理接口文档

本文档描述了与任务类型管理相关的所有API接口。这些接口用于管理系统中的任务类型定义。

## 接口列表

### 1. 获取所有任务类型
**接口**: `/`  
**方法**: `GET`  
**描述**: 获取系统中所有任务类型的列表  
**权限要求**: 需要登录  

**响应示例**:
```json
{
    "code": 0,
    "msg": "success",
    "data": [
        {
            "id": 1,
            "name": "数据同步",
            "desc": "用于数据库之间的数据同步",
            "engines": "spark,flink",
            "create_time": "2024-01-20 10:00:00",
            "create_user": "admin",
            "update_time": "2024-01-20 10:00:00",
            "update_user": "admin"
        }
    ]
}
```

### 2. 获取任务类型详情
**接口**: `/<id>`  
**方法**: `GET`  
**描述**: 获取指定ID的任务类型详细信息，包括支持的引擎列表  
**权限要求**: 需要登录  

**路径参数**:
| 参数名 | 类型 | 描述 |
|-------|------|------|
| id | integer | 任务类型ID |

**响应示例**:
```json
{
    "code": 0,
    "msg": "success",
    "data": {
        "id": 1,
        "name": "数据同步",
        "desc": "用于数据库之间的数据同步",
        "engines": [
            {
                "id": 1,
                "name": "spark"
            },
            {
                "id": 2,
                "name": "flink"
            }
        ],
        "create_time": "2024-01-20 10:00:00",
        "create_user": "admin",
        "update_time": "2024-01-20 10:00:00",
        "update_user": "admin"
    }
}
```

### 3. 创建任务类型
**接口**: `/`  
**方法**: `POST`  
**描述**: 创建新的任务类型  
**权限要求**: 需要登录  

**请求参数**:
| 参数名 | 类型 | 必填 | 描述 |
|-------|------|------|------|
| name | string | 是 | 任务类型名称 |
| desc | string | 否 | 任务类型描述 |
| engines | string | 是 | 支持的引擎列表，多个引擎用逗号分隔 |

**响应示例**:
```json
{
    "code": 0,
    "msg": "success",
    "data": {
        "id": 1
    }
}
```

### 4. 更新任务类型
**接口**: `/<id>`  
**方法**: `POST`  
**描述**: 更新指定ID的任务类型信息  
**权限要求**: 需要登录  

**路径参数**:
| 参数名 | 类型 | 描述 |
|-------|------|------|
| id | integer | 任务类型ID |

**请求参数**:
| 参数名 | 类型 | 必填 | 描述 |
|-------|------|------|------|
| name | string | 否 | 任务类型名称 |
| desc | string | 否 | 任务类型描述 |
| engines | string | 否 | 支持的引擎列表，多个引擎用逗号分隔 |

**响应示例**:
```json
{
    "code": 0,
    "msg": "success",
    "data": null
}
```

## 错误码说明

所有接口可能返回的错误码：

| 错误码 | 描述 |
|-------|------|
| 0 | 成功 |
| 400 | 参数错误 |
| 404 | 资源不存在 |
| 500 | 内部服务器错误 |

## 注意事项

1. 所有接口都需要用户登录
2. 任务类型名称在系统中必须唯一
3. 创建和更新操作会自动记录操作人和时间
4. engines字段中指定的引擎必须是系统中已存在的引擎
5. 更新任务类型时需要注意不要影响已经使用该类型的任务
6. 建议在任务类型描述中详细说明该类型任务的使用场景和注意事项
7. 引擎列表的顺序可能会影响任务的调度策略
8. 删除任务类型前需要确保没有任务正在使用该类型 