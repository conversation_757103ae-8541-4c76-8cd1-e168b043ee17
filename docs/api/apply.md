# 申请管理接口文档

本文档描述了与申请管理相关的所有API接口。这些接口用于管理任务申请的创建、审批和进度查询等功能。

## 接口列表

### 1. 创建申请
**接口**: `/`  
**方法**: `POST`  
**描述**: 创建新的任务申请  
**权限要求**: 需要登录  

**请求参数**:

| 参数名 | 类型 | 必填 | 描述 |
|-------|------|------|------|
| information | string | 否 | 申请说明信息 |
| tasks | array | 是 | 任务列表 |

**任务对象格式**:
```json
{
    "name": "任务名称",
    "trans_type": "传输类型",
    "engine_id": 1,
    "alarm_receivers": "告警接收人",
    "params": "任务参数",
    "source": {
        "cid": 1,
        "其他配置": "值"
    },
    "sink": {
        "cid": 2,
        "其他配置": "值"
    },
    "exec_immediately": true
}
```

**响应示例**:
```json
{
    "code": 0,
    "msg": "success",
    "data": {
        "id": 1
    }
}
```

### 2. 获取申请进度
**接口**: `/progress/<id>`  
**方法**: `GET`  
**描述**: 获取指定申请的审批进度  
**权限要求**: 需要登录  

**路径参数**:

| 参数名 | 类型 | 描述 |
|-------|------|------|
| id | integer | 申请ID |

**响应示例**:
```json
{
    "code": 0,
    "msg": "success",
    "data": {
        "status": "APPROVING",
        "steps": [
            {
                "step": 1,
                "approver": "admin",
                "status": "APPROVED",
                "comment": "同意"
            }
        ]
    }
}
```

### 3. 催促申请
**接口**: `/push/<id>`  
**方法**: `GET`  
**描述**: 催促指定申请的审批  
**权限要求**: 需要登录  

**路径参数**:

| 参数名 | 类型 | 描述 |
|-------|------|------|
| id | integer | 申请ID |

**响应示例**:
```json
{
    "code": 0,
    "msg": "success",
    "data": null
}
```

### 4. 获取申请详情
**接口**: `/<id>`  
**方法**: `GET`  
**描述**: 获取指定申请的详细信息  
**权限要求**: 需要登录  

**路径参数**:

| 参数名 | 类型 | 描述 |
|-------|------|------|
| id | integer | 申请ID |

**响应示例**:
```json
{
    "code": 0,
    "msg": "success",
    "data": {
        "id": 1,
        "status": "APPROVING",
        "information": "申请说明",
        "create_time": "2024-01-20 10:00:00",
        "create_user": "user001",
        "update_time": "2024-01-20 10:00:00",
        "update_user": "user001"
    }
}
```

### 5. 同意申请
**接口**: `/agree/<id>`  
**方法**: `GET`  
**描述**: 同意指定的申请  
**权限要求**: 需要登录，且必须是审批人  

**路径参数**:

| 参数名 | 类型 | 描述 |
|-------|------|------|
| id | integer | 申请ID |

**响应示例**:
```json
{
    "code": 0,
    "msg": "success",
    "data": null
}
```

### 6. 拒绝申请
**接口**: `/reject/<id>`  
**方法**: `GET`, `POST`  
**描述**: 拒绝指定的申请  
**权限要求**: 需要登录，且必须是审批人  

**路径参数**:

| 参数名 | 类型 | 描述 |
|-------|------|------|
| id | integer | 申请ID |

**请求参数**:

| 参数名 | 类型 | 必填 | 描述 |
|-------|------|------|------|
| comment | string | 否 | 拒绝原因 |

**响应示例**:
```json
{
    "code": 0,
    "msg": "success",
    "data": null
}
```

## 申请状态说明

| 状态 | 描述 |
|------|------|
| APPROVING | 审批中 |
| APPROVED | 已通过 |
| REJECTED | 已拒绝 |

## 错误码说明

所有接口可能返回的错误码：

| 错误码 | 描述 |
|-------|------|
| 0 | 成功 |
| 400 | 参数错误 |
| 403 | 无权限 |
| 404 | 资源不存在 |
| 500 | 内部服务器错误 |

## 注意事项

1. 所有接口都需要用户登录
2. 创建申请时，任务名称必须符合规范
3. 申请创建后会自动进入审批流程
4. 审批通过后，如果设置了立即执行，任务会自动开始执行
5. 催促功能有频率限制，建议合理使用
6. 拒绝申请时建议填写拒绝原因
7. 申请状态变更会通知相关人员
8. 审批流程按照预设的审批链进行 