# 组件管理接口文档

本文档描述了与组件管理相关的所有API接口。这些接口用于管理系统中的数据处理组件。

## 接口列表

### 1. 获取组件列表
**接口**: `/`  
**方法**: `GET`  
**描述**: 获取系统中的组件列表，支持按操作类型和数据源类型筛选  
**权限要求**: 需要登录  

**查询参数**:

| 参数名 | 类型 | 必填 | 描述 |
|-------|------|------|------|
| operator | string | 否 | 操作类型(source/sink) |
| datasource | string | 否 | 数据源类型 |

**响应示例**:
```json
{
    "code": 0,
    "msg": "success",
    "data": [
        {
            "id": 1,
            "operator": "source",
            "datasource": "mysql",
            "config": {
                "host": "${host}",
                "port": "${port}",
                "database": "${database}"
            },
            "create_time": "2024-01-20 10:00:00",
            "create_user": "admin",
            "update_time": "2024-01-20 10:00:00",
            "update_user": "admin"
        }
    ]
}
```

### 2. 创建组件
**接口**: `/`  
**方法**: `POST`  
**描述**: 创建新的组件  
**权限要求**: 需要登录  

**请求参数**:

| 参数名 | 类型 | 必填 | 描述 |
|-------|------|------|------|
| operator | string | 是 | 操作类型(source/sink) |
| datasource | string | 是 | 数据源类型 |
| config | object | 否 | 组件配置模板 |

**响应示例**:
```json
{
    "code": 0,
    "msg": "success",
    "data": {
        "id": 1
    }
}
```

### 3. 更新组件
**接口**: `/<id>`  
**方法**: `POST`  
**描述**: 更新指定ID的组件信息  
**权限要求**: 需要登录  

**路径参数**:

| 参数名 | 类型 | 描述 |
|-------|------|------|
| id | integer | 组件ID |

**请求参数**:

| 参数名 | 类型 | 必填 | 描述 |
|-------|------|------|------|
| operator | string | 否 | 操作类型 |
| datasource | string | 否 | 数据源类型 |
| config | object | 否 | 组件配置模板 |

**响应示例**:
```json
{
    "code": 0,
    "msg": "success",
    "data": null
}
```

### 4. 匹配源组件
**接口**: `/match/sources`  
**方法**: `GET`  
**描述**: 根据目标组件ID获取匹配的源组件列表  
**权限要求**: 无  

**查询参数**:

| 参数名 | 类型 | 必填 | 描述 |
|-------|------|------|------|
| sink_id | integer | 是 | 目标组件ID |

**响应示例**:
```json
{
    "code": 0,
    "msg": "success",
    "data": [
        {
            "code": "mysql2mysql",
            "name": "MySQL同步",
            "operator": "source",
            "datasource": "mysql",
            "cid": 1,
            "engines": [
                {
                    "id": 1,
                    "name": "spark"
                }
            ]
        }
    ]
}
```

### 5. 匹配目标组件
**接口**: `/match/sinks`  
**方法**: `GET`  
**描述**: 根据源组件ID获取匹配的目标组件列表  
**权限要求**: 无  

**查询参数**:

| 参数名 | 类型 | 必填 | 描述 |
|-------|------|------|------|
| source_id | integer | 是 | 源组件ID |

**响应示例**:
```json
{
    "code": 0,
    "msg": "success",
    "data": [
        {
            "code": "mysql2mysql",
            "name": "MySQL同步",
            "operator": "sink",
            "datasource": "mysql",
            "cid": 2,
            "engines": [
                {
                    "id": 1,
                    "name": "spark"
                }
            ]
        }
    ]
}
```

## 组件类型说明

### 操作类型(operator)

| 类型 | 描述 |
|------|------|
| source | 数据源组件，用于读取数据 |
| sink | 目标组件，用于写入数据 |

### 数据源类型(datasource)

| 类型 | 描述 |
|------|------|
| mysql | MySQL数据库 |
| postgresql | PostgreSQL数据库 |
| oracle | Oracle数据库 |
| kafka | Kafka消息队列 |
| hdfs | HDFS文件系统 |
| ftp | FTP文件服务器 |

## 错误码说明

所有接口可能返回的错误码：

| 错误码 | 描述 |
|-------|------|
| 0 | 成功 |
| 400 | 参数错误 |
| 404 | 资源不存在 |
| 500 | 内部服务器错误 |

## 注意事项

1. 组件配置模板中的变量使用 ${variable} 格式
2. 创建组件时需要确保配置模板的正确性
3. 组件的匹配关系由任务类型定义
4. 更新组件时需要注意不要影响现有任务
5. 组件配置应该包含必要的连接信息
6. 建议在组件描述中说明配置参数的含义
7. 组件的匹配关系会影响任务创建时的选项
8. 删除组件前需要确保没有任务正在使用该组件 