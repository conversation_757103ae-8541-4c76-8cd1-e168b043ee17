# 文档服务接口文档

本文档描述了与文档服务相关的所有API接口。这些接口用于查看系统的API文档和插件配置文档。

## 接口列表

### 1. 查看API文档
**接口**: `/`  
**方法**: `GET`  
**描述**: 显示系统的API文档  
**权限要求**: 无  

**响应格式**: HTML  
**说明**: 返回一个包含完整样式的HTML页面，展示API文档内容。文档内容来源于 `docs/api.md` 文件。

**特性**:
- 支持Markdown语法
- 代码高亮显示
- 响应式布局
- 支持表格展示
- 支持目录生成

### 2. 查看插件文档
**接口**: `/plugins`  
**方法**: `GET`  
**描述**: 显示系统的插件配置文档  
**权限要求**: 无  

**响应格式**: HTML  
**说明**: 返回一个包含完整样式的HTML页面，展示插件配置文档内容。文档内容来源于 `docs/plugins.md` 文件。

**特性**:
- 支持Markdown语法
- 代码高亮显示
- 响应式布局
- 支持表格展示
- 支持目录生成

## 错误码说明

所有接口可能返回的错误码：

| 错误码 | 描述               |
|--------|-------------------|
| 0      | 成功              |
| 404    | 文档文件不存在     |
| 500    | 内部服务器错误     |

## 文档样式说明

文档页面使用以下样式和库：

1. **GitHub Markdown CSS**
   - 版本：5.2.0
   - 用途：提供与GitHub风格一致的Markdown渲染样式

2. **Highlight.js**
   - 版本：11.7.0
   - 用途：提供代码块的语法高亮
   - 主题：GitHub风格

## 注意事项

1. 文档内容必须使用UTF-8编码
2. 文档必须使用Markdown格式编写
3. 代码块需要指定语言以获得正确的语法高亮
4. 页面最大宽度限制为980px
5. 移动设备上会自动调整页面布局
6. 文档更新后会立即反映在接口响应中 