# 组织管理接口文档

本文档描述了与组织管理相关的所有API接口。这些接口用于管理系统中的组织信息。

## 接口列表

### 1. 获取所有组织
**接口**: `/`  
**方法**: `GET`  
**描述**: 获取系统中所有组织的列表  
**权限要求**: 需要登录  

**响应示例**:
```json
{
    "code": 0,
    "msg": "success",
    "data": [
        {
            "id": 1,
            "name": "技术部",
            "desc": "负责系统研发",
            "create_time": "2024-01-20 10:00:00",
            "create_user": "admin",
            "update_time": "2024-01-20 10:00:00",
            "update_user": "admin"
        }
    ]
}
```

### 2. 获取组织详情（通过ID）
**接口**: `/<id>`  
**方法**: `GET`  
**描述**: 获取指定ID的组织详细信息  
**权限要求**: 需要登录  

**路径参数**:

| 参数名 | 类型 | 描述 |
|-------|------|------|
| id | integer | 组织ID |

**响应示例**:
```json
{
    "code": 0,
    "msg": "success",
    "data": {
        "id": 1,
        "name": "技术部",
        "desc": "负责系统研发",
        "create_time": "2024-01-20 10:00:00",
        "create_user": "admin",
        "update_time": "2024-01-20 10:00:00",
        "update_user": "admin"
    }
}
```

### 3. 获取组织详情（通过名称）
**接口**: `/<name>`  
**方法**: `GET`  
**描述**: 获取指定名称的组织详细信息  
**权限要求**: 无  

**路径参数**:

| 参数名 | 类型 | 描述 |
|-------|------|------|
| name | string | 组织名称 |

**响应示例**:
```json
{
    "code": 0,
    "msg": "success",
    "data": {
        "id": 1,
        "name": "技术部",
        "desc": "负责系统研发",
        "create_time": "2024-01-20 10:00:00",
        "create_user": "admin",
        "update_time": "2024-01-20 10:00:00",
        "update_user": "admin"
    }
}
```

### 4. 创建组织
**接口**: `/`  
**方法**: `POST`  
**描述**: 创建新的组织  
**权限要求**: 需要登录  

**请求参数**:

| 参数名 | 类型 | 必填 | 描述 |
|-------|------|------|------|
| name | string | 是 | 组织名称 |
| desc | string | 否 | 组织描述 |

**响应示例**:
```json
{
    "code": 0,
    "msg": "success",
    "data": {
        "id": 1
    }
}
```

### 5. 更新组织
**接口**: `/<id>`  
**方法**: `POST`  
**描述**: 更新指定ID的组织信息  
**权限要求**: 需要登录  

**路径参数**:

| 参数名 | 类型 | 描述 |
|-------|------|------|
| id | integer | 组织ID |

**请求参数**:

| 参数名 | 类型 | 必填 | 描述 |
|-------|------|------|------|
| name | string | 否 | 组织名称 |
| desc | string | 否 | 组织描述 |

**响应示例**:
```json
{
    "code": 0,
    "msg": "success",
    "data": null
}
```

## 错误码说明

所有接口可能返回的错误码：

| 错误码 | 描述 |
|-------|------|
| 0 | 成功 |
| 400 | 参数错误 |
| 404 | 资源不存在 |
| 500 | 内部服务器错误 |

## 注意事项

1. 除了通过名称获取组织信息外，其他接口都需要用户登录
2. 组织名称在系统中必须唯一
3. 创建和更新操作会自动记录操作人和时间
4. 组织名称不应包含特殊字符
5. 建议组织描述不超过200字符
6. 组织信息的变更会影响到与之相关的所有业务数据 