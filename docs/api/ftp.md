# FTP服务接口文档

本文档描述了与FTP服务相关的所有API接口。这些接口用于测试FTP连接和浏览FTP服务器内容。

## 接口列表

### 1. 测试DSN连接（通过名称）
**接口**: `/test/<name>`  
**方法**: `GET`  
**描述**: 测试指定DSN名称的FTP连接是否可用  
**权限要求**: 需要登录  

**路径参数**:

| 参数名 | 类型 | 描述 |
|-------|------|------|
| name | string | DSN名称 |

**响应示例**:
```json
{
    "code": 0,
    "msg": "success",
    "data": {
        "connected": true,
        "error": null
    }
}
```

### 2. 测试DSN连接（通过ID）
**接口**: `/test/id/<id>`  
**方法**: `GET`  
**描述**: 测试指定DSN ID的FTP连接是否可用  
**权限要求**: 需要登录  

**路径参数**:

| 参数名 | 类型 | 描述 |
|-------|------|------|
| id | integer | DSN ID |

**响应示例**:
```json
{
    "code": 0,
    "msg": "success",
    "data": {
        "connected": true,
        "error": null
    }
}
```

### 3. 测试直接连接
**接口**: `/test`  
**方法**: `POST`  
**描述**: 测试直接提供的FTP连接信息  
**权限要求**: 需要登录  

**请求参数**:

| 参数名 | 类型 | 必填 | 描述 |
|-------|------|------|------|
| protocol | string | 是 | FTP协议类型 |
| ip | string | 是 | FTP服务器IP地址 |
| port | integer | 是 | FTP服务器端口 |
| username | string | 是 | 用户名 |
| password | string | 是 | 密码 |

**响应示例**:
```json
{
    "code": 0,
    "msg": "success",
    "data": {
        "connected": true,
        "error": null
    }
}
```

### 4. 列出FTP目录内容（通过DSN ID）
**接口**: `/list/id/<id>`  
**方法**: `GET`  
**描述**: 获取指定DSN ID的FTP服务器上某个路径下的所有内容  
**权限要求**: 需要登录  

**路径参数**:

| 参数名 | 类型 | 描述 |
|-------|------|------|
| id | integer | DSN ID |

**查询参数**:

| 参数名 | 类型 | 必填 | 描述 |
|-------|------|------|------|
| ftp_path | string | 是 | FTP服务器上的路径 |

**响应示例**:
```json
{
    "code": 0,
    "msg": "success",
    "data": {
        "files": [
            {
                "name": "example.txt",
                "type": "file",
                "size": 1024,
                "modify_time": "2024-01-20 10:00:00"
            }
        ]
    }
}
```

### 5. 列出FTP目录内容（通过DSN名称）
**接口**: `/list/<name>`  
**方法**: `GET`  
**描述**: 获取指定DSN名称的FTP服务器上某个路径下的所有内容  
**权限要求**: 需要登录  

**路径参数**:

| 参数名 | 类型 | 描述 |
|-------|------|------|
| name | string | DSN名称 |

**查询参数**:

| 参数名 | 类型 | 必填 | 描述 |
|-------|------|------|------|
| ftp_path | string | 是 | FTP服务器上的路径 |

**响应示例**:
```json
{
    "code": 0,
    "msg": "success",
    "data": {
        "files": [
            {
                "name": "example.txt",
                "type": "file",
                "size": 1024,
                "modify_time": "2024-01-20 10:00:00"
            }
        ]
    }
}
```

### 6. 列出FTP目录内容（直接连接）
**接口**: `/list/direct`  
**方法**: `GET`  
**描述**: 通过直接提供的连接信息获取FTP服务器上某个路径下的所有内容  
**权限要求**: 需要登录  

**查询参数**:

| 参数名 | 类型 | 必填 | 描述 |
|-------|------|------|------|
| protocol | string | 是 | FTP协议类型 |
| ip | string | 是 | FTP服务器IP地址 |
| port | integer | 是 | FTP服务器端口 |
| username | string | 是 | 用户名 |
| password | string | 是 | 密码 |
| ftp_path | string | 是 | FTP服务器上的路径 |

**响应示例**:
```json
{
    "code": 0,
    "msg": "success",
    "data": {
        "files": [
            {
                "name": "example.txt",
                "type": "file",
                "size": 1024,
                "modify_time": "2024-01-20 10:00:00"
            }
        ]
    }
}
```

## 错误码说明

所有接口可能返回的错误码：

| 错误码 | 描述 |
|-------|------|
| 0 | 成功 |
| 400 | 参数错误 |
| 404 | 资源不存在 |
| 500 | 内部服务器错误 |

## 注意事项

1. 所有接口都需要用户登录
2. 端口号必须是有效的整数
3. FTP路径应使用正斜杠(/)作为分隔符
4. 密码在传输时应进行适当的加密处理
5. 建议在生产环境中使用SFTP等安全协议
6. 连接测试可能需要一定时间，请设置合适的超时时间 