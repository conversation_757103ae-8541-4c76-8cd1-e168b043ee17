# FTP服务接口文档

本文档描述了与FTP服务相关的所有API接口。这些接口用于测试FTP连接、浏览FTP服务器内容、获取文件行数统计和读取文件内容。

## 接口列表

### 1. 测试DSN连接（通过名称）
**接口**: `/test/<name>`  
**方法**: `GET`  
**描述**: 测试指定DSN名称的FTP连接是否可用  
**权限要求**: 需要登录  

**路径参数**:

| 参数名 | 类型 | 描述 |
|-------|------|------|
| name | string | DSN名称 |

**响应示例**:
```json
{
    "code": 0,
    "msg": "success",
    "data": {
        "connected": true,
        "error": null
    }
}
```

### 2. 测试DSN连接（通过ID）
**接口**: `/test/id/<id>`  
**方法**: `GET`  
**描述**: 测试指定DSN ID的FTP连接是否可用  
**权限要求**: 需要登录  

**路径参数**:

| 参数名 | 类型 | 描述 |
|-------|------|------|
| id | integer | DSN ID |

**响应示例**:
```json
{
    "code": 0,
    "msg": "success",
    "data": {
        "connected": true,
        "error": null
    }
}
```

### 3. 测试直接连接
**接口**: `/test`  
**方法**: `POST`  
**描述**: 测试直接提供的FTP连接信息  
**权限要求**: 需要登录  

**请求参数**:

| 参数名 | 类型 | 必填 | 描述 |
|-------|------|------|------|
| protocol | string | 是 | FTP协议类型 |
| ip | string | 是 | FTP服务器IP地址 |
| port | integer | 是 | FTP服务器端口 |
| username | string | 是 | 用户名 |
| password | string | 是 | 密码 |

**响应示例**:
```json
{
    "code": 0,
    "msg": "success",
    "data": {
        "connected": true,
        "error": null
    }
}
```

### 4. 列出FTP目录内容（通过DSN ID）
**接口**: `/list/id/<id>`  
**方法**: `GET`  
**描述**: 获取指定DSN ID的FTP服务器上某个路径下的所有内容  
**权限要求**: 需要登录  

**路径参数**:

| 参数名 | 类型 | 描述 |
|-------|------|------|
| id | integer | DSN ID |

**查询参数**:

| 参数名 | 类型 | 必填 | 描述 |
|-------|------|------|------|
| ftp_path | string | 是 | FTP服务器上的路径 |

**响应示例**:
```json
{
    "code": 0,
    "msg": "success",
    "data": {
        "files": [
            {
                "name": "example.txt",
                "type": "file",
                "size": 1024,
                "modify_time": "2024-01-20 10:00:00"
            }
        ]
    }
}
```

### 5. 列出FTP目录内容（通过DSN名称）
**接口**: `/list/<name>`  
**方法**: `GET`  
**描述**: 获取指定DSN名称的FTP服务器上某个路径下的所有内容  
**权限要求**: 需要登录  

**路径参数**:

| 参数名 | 类型 | 描述 |
|-------|------|------|
| name | string | DSN名称 |

**查询参数**:

| 参数名 | 类型 | 必填 | 描述 |
|-------|------|------|------|
| ftp_path | string | 是 | FTP服务器上的路径 |

**响应示例**:
```json
{
    "code": 0,
    "msg": "success",
    "data": {
        "files": [
            {
                "name": "example.txt",
                "type": "file",
                "size": 1024,
                "modify_time": "2024-01-20 10:00:00"
            }
        ]
    }
}
```

### 6. 列出FTP目录内容（直接连接）
**接口**: `/list/direct`  
**方法**: `GET`  
**描述**: 通过直接提供的连接信息获取FTP服务器上某个路径下的所有内容  
**权限要求**: 需要登录  

**查询参数**:

| 参数名 | 类型 | 必填 | 描述 |
|-------|------|------|------|
| protocol | string | 是 | FTP协议类型 |
| ip | string | 是 | FTP服务器IP地址 |
| port | integer | 是 | FTP服务器端口 |
| username | string | 是 | 用户名 |
| password | string | 是 | 密码 |
| ftp_path | string | 是 | FTP服务器上的路径 |

**响应示例**:
```json
{
    "code": 0,
    "msg": "success",
    "data": {
        "files": [
            {
                "name": "example.txt",
                "type": "file",
                "size": 1024,
                "modify_time": "2024-01-20 10:00:00"
            }
        ]
    }
}
```

### 7. 获取文件行数统计（通过DSN ID）
**接口**: `/wc/id/<id>`
**方法**: `GET`
**描述**: 通过DSN ID获取指定文件的总行数
**权限要求**: 需要登录

**路径参数**:

| 参数名 | 类型 | 描述 |
|-------|------|------|
| id | integer | DSN ID |

**查询参数**:

| 参数名 | 类型 | 必填 | 描述 |
|-------|------|------|------|
| file_path | string | 是 | 文件路径 |

**响应示例**:
```json
{
    "code": 200,
    "message": "success",
    "data": {
        "file_path": "/data/logs/app.log",
        "line_count": 15432
    }
}
```

### 8. 获取文件行数统计（通过DSN名称）
**接口**: `/wc/<name>`
**方法**: `GET`
**描述**: 通过DSN名称获取指定文件的总行数
**权限要求**: 需要登录

**路径参数**:

| 参数名 | 类型 | 描述 |
|-------|------|------|
| name | string | DSN名称 |

**查询参数**:

| 参数名 | 类型 | 必填 | 描述 |
|-------|------|------|------|
| file_path | string | 是 | 文件路径 |

**响应示例**:
```json
{
    "code": 200,
    "message": "success",
    "data": {
        "file_path": "/data/logs/app.log",
        "line_count": 15432
    }
}
```

### 9. 获取文件行数统计（直接连接）
**接口**: `/wc/direct`
**方法**: `GET`
**描述**: 通过直接提供的连接信息获取指定文件的总行数
**权限要求**: 需要登录

**查询参数**:

| 参数名 | 类型 | 必填 | 描述 |
|-------|------|------|------|
| protocol | string | 是 | FTP协议类型 (ftp/sftp/wtp) |
| ip | string | 是 | FTP服务器IP地址 |
| port | integer | 是 | FTP服务器端口 |
| username | string | 是 | 用户名 |
| password | string | 是 | 密码 |
| file_path | string | 是 | 文件路径 |

**响应示例**:
```json
{
    "code": 200,
    "message": "success",
    "data": {
        "file_path": "/data/logs/app.log",
        "line_count": 15432
    }
}
```

### 10. 获取文件前n行内容（通过DSN ID）
**接口**: `/headn/id/<id>`
**方法**: `GET`
**描述**: 通过DSN ID获取指定文件的前n行内容
**权限要求**: 需要登录

**路径参数**:

| 参数名 | 类型 | 描述 |
|-------|------|------|
| id | integer | DSN ID |

**查询参数**:

| 参数名 | 类型 | 必填 | 描述 |
|-------|------|------|------|
| file_path | string | 是 | 文件路径 |
| n | integer | 否 | 要获取的行数，默认100 |

**响应示例**:
```json
{
    "code": 200,
    "message": "success",
    "data": {
        "file_path": "/data/logs/app.log",
        "lines_requested": 50,
        "content": "2024-01-01 10:00:01 INFO Application started\n2024-01-01 10:00:02 INFO Loading configuration..."
    }
}
```

### 11. 获取文件前n行内容（通过DSN名称）
**接口**: `/headn/<name>`
**方法**: `GET`
**描述**: 通过DSN名称获取指定文件的前n行内容
**权限要求**: 需要登录

**路径参数**:

| 参数名 | 类型 | 描述 |
|-------|------|------|
| name | string | DSN名称 |

**查询参数**:

| 参数名 | 类型 | 必填 | 描述 |
|-------|------|------|------|
| file_path | string | 是 | 文件路径 |
| n | integer | 否 | 要获取的行数，默认100 |

**响应示例**:
```json
{
    "code": 200,
    "message": "success",
    "data": {
        "file_path": "/data/logs/app.log",
        "lines_requested": 100,
        "content": "2024-01-01 10:00:01 INFO Application started\n2024-01-01 10:00:02 INFO Loading configuration..."
    }
}
```

### 12. 获取文件前n行内容（直接连接）
**接口**: `/headn/direct`
**方法**: `GET`
**描述**: 通过直接提供的连接信息获取指定文件的前n行内容
**权限要求**: 需要登录

**查询参数**:

| 参数名 | 类型 | 必填 | 描述 |
|-------|------|------|------|
| protocol | string | 是 | FTP协议类型 (ftp/sftp/wtp) |
| ip | string | 是 | FTP服务器IP地址 |
| port | integer | 是 | FTP服务器端口 |
| username | string | 是 | 用户名 |
| password | string | 是 | 密码 |
| file_path | string | 是 | 文件路径 |
| n | integer | 否 | 要获取的行数，默认100 |

**响应示例**:
```json
{
    "code": 200,
    "message": "success",
    "data": {
        "file_path": "/data/logs/app.log",
        "lines_requested": 20,
        "content": "2024-01-01 10:00:01 INFO Application started\n2024-01-01 10:00:02 INFO Loading configuration..."
    }
}
```

## 错误码说明

所有接口可能返回的错误码：

| 错误码 | 描述 |
|-------|------|
| 200 | 成功 |
| 400 | 参数错误（如：端口号无效、n参数无效等） |
| 404 | 资源不存在（如：DSN不存在、文件不存在等） |
| 500 | 内部服务器错误（如：连接失败、读取失败等） |

**常见错误响应示例**:

文件不存在：
```json
{
    "code": 404,
    "message": "resource not found",
    "detail": "File /data/logs/nonexistent.log not found"
}
```

参数错误：
```json
{
    "code": 400,
    "message": "illegal params",
    "detail": "Parameter n must be a positive integer"
}
```

## 注意事项

### 通用注意事项
1. 所有接口都需要用户登录
2. 端口号必须是有效的整数
3. FTP路径应使用正斜杠(/)作为分隔符
4. 密码在传输时应进行适当的加密处理
5. 建议在生产环境中使用SFTP等安全协议
6. 连接测试可能需要一定时间，请设置合适的超时时间

### 文件操作接口特殊说明
7. **wc接口**：
   - 支持大文件高效处理，使用流式读取避免内存溢出
   - SFTP协议优先使用服务器端`wc -l`命令，性能最优
   - FTP协议使用网络流式统计，无需下载整个文件

8. **headn接口**：
   - 参数`n`必须是正整数，默认值为100
   - SFTP协议优先使用服务器端`head -n`命令
   - 对于大文件，建议设置较小的`n`值以提高响应速度

9. **性能优化建议**：
   - 推荐使用SFTP协议，具有最佳的性能表现
   - 在网络不稳定环境下，建议适当减少`headn`的行数请求
   - 所有文件操作都会自动检查文件是否存在

10. **安全建议**：
    - 确保提供的用户账号具有文件读取权限
    - 使用HTTPS传输以保护连接信息安全
    - 所有操作都会记录详细的审计日志

## 使用示例

### 获取文件行数
```bash
# 通过DSN ID获取文件行数
curl -X GET "http://localhost:5000/api/v1/ftp/wc/id/123?file_path=/data/logs/app.log" \
     -H "Authorization: Bearer your_token"

# 通过DSN名称获取文件行数
curl -X GET "http://localhost:5000/api/v1/ftp/wc/prod-ftp?file_path=/data/logs/app.log" \
     -H "Authorization: Bearer your_token"
```

### 获取文件前n行
```bash
# 获取前50行
curl -X GET "http://localhost:5000/api/v1/ftp/headn/id/123?file_path=/data/logs/app.log&n=50" \
     -H "Authorization: Bearer your_token"

# 使用默认100行
curl -X GET "http://localhost:5000/api/v1/ftp/headn/prod-ftp?file_path=/data/logs/app.log" \
     -H "Authorization: Bearer your_token"
```