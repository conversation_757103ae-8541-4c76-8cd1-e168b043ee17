# FTP历史记录接口文档

本文档描述了与FTP文件历史记录相关的所有API接口。这些接口用于查询和管理FTP文件的历史记录信息。

## 接口列表

### 1. 获取单个历史文件记录
**接口**: `/file/<file_id>`  
**方法**: `GET`  
**描述**: 获取指定ID的FTP历史文件记录详情  
**权限要求**: 需要登录  

**路径参数**:

| 参数名 | 类型 | 描述 |
|-------|------|------|
| file_id | integer | 文件记录ID |

**响应示例**:
```json
{
    "code": 0,
    "msg": "success",
    "data": {
        "id": 1,
        "filename": "example.txt",
        "execution_id": 100,
        "create_time": "2024-01-20 10:00:00",
        "status": "success"
    }
}
```

### 2. 获取历史文件记录列表
**接口**: `/file`  
**方法**: `GET`  
**描述**: 获取FTP历史文件记录列表，支持分页和文件名搜索  
**权限要求**: 需要登录  

**查询参数**:

| 参数名 | 类型 | 必填 | 默认值 | 描述 |
|-------|------|------|--------|------|
| page | integer | 否 | 1 | 页码 |
| per_page | integer | 否 | 10 | 每页记录数 |
| filename | string | 否 | - | 文件名搜索关键词 |

**响应示例**:
```json
{
    "code": 0,
    "msg": "success",
    "data": {
        "items": [
            {
                "id": 1,
                "filename": "example.txt",
                "execution_id": 100,
                "create_time": "2024-01-20 10:00:00",
                "status": "success"
            }
        ],
        "total": 100,
        "page": 1,
        "per_page": 10
    }
}
```

### 3. 获取执行ID相关的历史文件记录
**接口**: `/file/execution/<execution_id>`  
**方法**: `GET`  
**描述**: 获取指定执行ID关联的所有FTP历史文件记录  
**权限要求**: 需要登录  

**路径参数**:

| 参数名 | 类型 | 描述 |
|-------|------|------|
| execution_id | integer | 执行ID |

**响应示例**:
```json
{
    "code": 0,
    "msg": "success",
    "data": [
        {
            "id": 1,
            "filename": "example1.txt",
            "execution_id": 100,
            "create_time": "2024-01-20 10:00:00",
            "status": "success"
        },
        {
            "id": 2,
            "filename": "example2.txt",
            "execution_id": 100,
            "create_time": "2024-01-20 10:01:00",
            "status": "success"
        }
    ]
}
```

## 错误码说明

所有接口可能返回的错误码：

| 错误码 | 描述 |
|-------|------|
| 0 | 成功 |
| 404 | 资源不存在 |
| 500 | 内部服务器错误 |

## 注意事项

1. 所有接口都需要用户登录
2. 分页参数必须是正整数
3. 文件名搜索支持模糊匹配
4. 返回的时间格式为 ISO 8601 标准格式
5. 建议合理设置分页大小，避免返回过多数据
6. 历史记录按时间倒序排列 