# 组件配置管理接口文档

本文档描述了与组件配置管理相关的所有API接口。这些接口用于管理组件的具体配置信息。

## 接口列表

### 1. 获取组件配置
**接口**: `/<cid>`  
**方法**: `GET`  
**描述**: 获取指定组件ID的所有配置信息  
**权限要求**: 需要登录  

**路径参数**:

| 参数名 | 类型 | 描述 |
|-------|------|------|
| cid | integer | 组件ID |

**响应示例**:
```json
{
    "code": 0,
    "msg": "success",
    "data": [
        {
            "id": 1,
            "cid": 100,
            "name": "host",
            "value": "db.example.com",
            "create_time": "2024-01-20 10:00:00",
            "create_user": "admin",
            "update_time": "2024-01-20 10:00:00",
            "update_user": "admin"
        }
    ]
}
```

### 2. 添加组件配置
**接口**: `/`  
**方法**: `POST`  
**描述**: 为组件添加新的配置项  
**权限要求**: 需要登录  

**请求参数**:

| 参数名 | 类型 | 必填 | 描述 |
|-------|------|------|------|
| cid | integer | 是 | 组件ID |
| name | string | 是 | 配置项名称 |
| value | string | 是 | 配置项值 |

**响应示例**:
```json
{
    "code": 0,
    "msg": "success",
    "data": {
        "id": 1
    }
}
```

### 3. 更新组件配置
**接口**: `/<id>`  
**方法**: `POST`  
**描述**: 更新指定ID的配置项  
**权限要求**: 需要登录  

**路径参数**:

| 参数名 | 类型 | 描述 |
|-------|------|------|
| id | integer | 配置项ID |

**请求参数**:

| 参数名 | 类型 | 必填 | 描述 |
|-------|------|------|------|
| name | string | 否 | 配置项名称 |
| value | string | 否 | 配置项值 |

**响应示例**:
```json
{
    "code": 0,
    "msg": "success",
    "data": null
}
```

### 4. 删除组件配置
**接口**: `/<id>`  
**方法**: `DELETE`  
**描述**: 删除指定ID的配置项  
**权限要求**: 需要登录  

**路径参数**:

| 参数名 | 类型 | 描述 |
|-------|------|------|
| id | integer | 配置项ID |

**响应示例**:
```json
{
    "code": 0,
    "msg": "success",
    "data": null
}
```

## 错误码说明

所有接口可能返回的错误码：

| 错误码 | 描述 |
|-------|------|
| 0 | 成功 |
| 400 | 参数错误 |
| 404 | 资源不存在 |
| 500 | 内部服务器错误 |

## 注意事项

1. 所有接口都需要用户登录
2. 配置项名称在同一组件内必须唯一
3. 创建和更新操作会自动记录操作人和时间
4. 敏感配置信息（如密码）应该进行加密处理
5. 更新配置时需要注意不要影响正在运行的任务
6. 建议为配置项添加适当的描述信息
7. 删除配置前需要确认不会影响现有功能
8. 配置值应该符合组件定义的数据类型和格式要求 