# 数据源管理接口文档

本文档描述了与数据源(DSN)管理相关的所有API接口。这些接口用于管理系统中的数据源配置信息。

## 接口列表

### 1. 获取所有数据源
**接口**: `/`  
**方法**: `GET`  
**描述**: 获取系统中所有数据源的列表  
**权限要求**: 需要登录  

**响应示例**:
```json
{
    "code": 0,
    "msg": "success",
    "data": [
        {
            "id": 1,
            "name": "mysql_prod",
            "type": "mysql",
            "connect": {
                "host": "db.example.com",
                "port": 3306,
                "username": "user",
                "password": "******"
            },
            "create_time": "2024-01-20 10:00:00",
            "create_user": "admin",
            "update_time": "2024-01-20 10:00:00",
            "update_user": "admin"
        }
    ]
}
```

### 2. 获取数据源详情（通过ID）
**接口**: `/<id>`  
**方法**: `GET`  
**描述**: 获取指定ID的数据源详细信息  
**权限要求**: 需要登录  

**路径参数**:

| 参数名 | 类型 | 描述 |
|-------|------|------|
| id | integer | 数据源ID |

**响应示例**:
```json
{
    "code": 0,
    "msg": "success",
    "data": {
        "id": 1,
        "name": "mysql_prod",
        "type": "mysql",
        "connect": {
            "host": "db.example.com",
            "port": 3306,
            "username": "user",
            "password": "******"
        },
        "create_time": "2024-01-20 10:00:00",
        "create_user": "admin",
        "update_time": "2024-01-20 10:00:00",
        "update_user": "admin"
    }
}
```

### 3. 获取数据源详情（通过名称）
**接口**: `/<name>`  
**方法**: `GET`  
**描述**: 获取指定名称的数据源详细信息  
**权限要求**: 无  

**路径参数**:

| 参数名 | 类型 | 描述 |
|-------|------|------|
| name | string | 数据源名称 |

**响应示例**:
```json
{
    "code": 0,
    "msg": "success",
    "data": {
        "id": 1,
        "name": "mysql_prod",
        "type": "mysql",
        "connect": {
            "host": "db.example.com",
            "port": 3306,
            "username": "user",
            "password": "******"
        },
        "create_time": "2024-01-20 10:00:00",
        "create_user": "admin",
        "update_time": "2024-01-20 10:00:00",
        "update_user": "admin"
    }
}
```

### 4. 创建数据源
**接口**: `/`  
**方法**: `POST`  
**描述**: 创建新的数据源  
**权限要求**: 需要登录  

**请求参数**:

| 参数名 | 类型 | 必填 | 描述 |
|-------|------|------|------|
| name | string | 是 | 数据源名称 |
| type | string | 是 | 数据源类型(如: mysql, postgresql, oracle等) |
| connect | object | 是 | 连接信息 |

**连接信息示例**:
```json
{
    "host": "db.example.com",
    "port": 3306,
    "username": "user",
    "password": "password",
    "database": "dbname"
}
```

**响应示例**:
```json
{
    "code": 0,
    "msg": "success",
    "data": {
        "id": 1
    }
}
```

### 5. 更新数据源
**接口**: `/<id>`  
**方法**: `POST`  
**描述**: 更新指定ID的数据源信息  
**权限要求**: 需要登录  

**路径参数**:

| 参数名 | 类型 | 描述 |
|-------|------|------|
| id | integer | 数据源ID |

**请求参数**:

| 参数名 | 类型 | 必填 | 描述 |
|-------|------|------|------|
| name | string | 否 | 数据源名称 |
| type | string | 否 | 数据源类型 |
| connect | object | 否 | 连接信息 |

**响应示例**:
```json
{
    "code": 0,
    "msg": "success",
    "data": null
}
```

## 错误码说明

所有接口可能返回的错误码：

| 错误码 | 描述 |
|-------|------|
| 0 | 成功 |
| 400 | 参数错误 |
| 404 | 资源不存在 |
| 500 | 内部服务器错误 |

## 注意事项

1. 除了通过名称获取数据源信息外，其他接口都需要用户登录
2. 数据源名称在系统中必须唯一
3. 创建和更新操作会自动记录操作人和时间
4. 连接信息中的敏感数据（如密码）在返回时会被脱敏
5. 建议在创建数据源后进行连接测试
6. 更新数据源信息时注意不要影响正在使用该数据源的任务
7. 数据源类型必须是系统支持的类型之一
8. 连接信息的格式因数据源类型而异，请参考具体数据源的文档 