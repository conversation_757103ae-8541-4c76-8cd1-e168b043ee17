# 执行记录接口文档

本文档描述了与任务执行记录相关的所有API接口。这些接口用于管理和监控任务的执行状态。

## 接口列表

### 1. 获取执行记录详情
**接口**: `/<id>`  
**方法**: `GET`  
**描述**: 获取指定ID的执行记录详细信息  
**权限要求**: 无  

**路径参数**:

| 参数名 | 类型 | 描述 |
|-------|------|------|
| id | integer | 执行记录ID |

**响应示例**:
```json
{
    "code": 0,
    "msg": "success",
    "data": {
        "id": 1,
        "execution_name": "task_execution_001",
        "status": "SUCCESS",
        "start_time": "2024-01-20 10:00:00",
        "end_time": "2024-01-20 10:05:00",
        "create_user": "user001"
    }
}
```

### 2. 获取执行日志
**接口**: `/log/<id>`  
**方法**: `GET`  
**描述**: 获取指定执行记录的日志内容  
**权限要求**: 无  

**路径参数**:

| 参数名 | 类型 | 描述 |
|-------|------|------|
| id | integer | 执行记录ID |

**响应格式**: text/plain  
**说明**: 返回原始日志文本内容

### 3. 获取执行状态
**接口**: `/status/<id>`  
**方法**: `GET`  
**描述**: 获取指定执行记录的当前状态  
**权限要求**: 无  

**路径参数**:

| 参数名 | 类型 | 描述 |
|-------|------|------|
| id | integer | 执行记录ID |

**响应示例**:
```json
{
    "code": 0,
    "msg": "success",
    "data": {
        "status": "RUNNING",
        "message": "Task is running",
        "start_time": "2024-01-20 10:00:00",
        "update_time": "2024-01-20 10:01:00",
        "end_time": null
    }
}
```

**状态说明**:

| 状态 | 描述 |
|------|------|
| RUNNING | 执行中 |
| SUCCESS | 执行成功 |
| FAILED | 执行失败 |
| KILLED | 已终止 |

### 4. 终止执行
**接口**: `/kill/<id>`  
**方法**: `GET`  
**描述**: 终止指定的执行记录  
**权限要求**: 需要登录，且必须是项目管理员或任务创建者  

**路径参数**:

| 参数名 | 类型 | 描述 |
|-------|------|------|
| id | integer | 执行记录ID |

**响应示例**:
```json
{
    "code": 0,
    "msg": "success",
    "data": null
}
```

### 5. 重新执行
**接口**: `/exec/<id>`  
**方法**: `GET`  
**描述**: 重新执行指定的执行记录  
**权限要求**: 需要登录  

**路径参数**:

| 参数名 | 类型 | 描述 |
|-------|------|------|
| id | integer | 执行记录ID |

**查询参数**:

| 参数名 | 类型 | 必填 | 默认值 | 描述 |
|-------|------|------|--------|------|
| async | integer | 否 | 1 | 是否异步执行(1:是, 0:否) |

**响应示例**:
```json
{
    "code": 0,
    "msg": "success",
    "data": null
}
```

## 错误码说明

所有接口可能返回的错误码：

| 错误码 | 描述 |
|-------|------|
| 0 | 成功 |
| 403 | 无权限 |
| 404 | 资源不存在 |
| 500 | 内部服务器错误 |
| 503 | 服务不可用 |

## 注意事项

1. 终止执行接口只能由项目管理员或任务创建者调用
2. 已经处于终止状态、成功状态或失败状态的执行记录不能被终止
3. 异步执行时需要通过状态接口查询执行结果
4. 日志文件保存在系统配置的日志目录中
5. 建议定期清理过期的执行记录和日志文件
6. 执行记录的状态变更会实时反映在状态接口中 