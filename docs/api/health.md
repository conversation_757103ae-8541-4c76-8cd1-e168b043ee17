# 健康检查与监控接口文档

本文档描述了与系统健康检查、告警和监控相关的所有API接口。

## 接口列表

### 1. 健康检查
**接口**: `/check`  
**方法**: `GET`  
**描述**: 检查系统是否正常运行  
**权限要求**: 无  

**响应示例**:
```json
{
    "code": 0,
    "msg": "success",
    "data": null
}
```

**说明**: 
- 返回成功响应表示系统正常运行
- 常用于负载均衡健康检查
- 可用于系统监控探测

### 2. 发送告警
**接口**: `/alert`  
**方法**: `GET`, `POST`  
**描述**: 发送告警消息给指定接收者  
**权限要求**: 无  

**请求参数**:
| 参数名 | 类型 | 必填 | 描述 |
|-------|------|------|------|
| msg | string | 是 | 告警消息内容 |
| receivers | string | 是 | 接收者列表，多个接收者用逗号分隔 |

**响应示例**:
```json
{
    "code": 0,
    "msg": "success",
    "data": null
}
```

**使用示例**:
```bash
# 使用GET方法
curl -X GET "http://api.example.com/alert?msg=系统异常&receivers=user1,user2"

# 使用POST方法
curl -X POST "http://api.example.com/alert" \
     -H "Content-Type: application/json" \
     -d '{"msg": "系统异常", "receivers": "user1,user2"}'
```

### 3. 获取Talos表结构
**接口**: `/talos/table/schema`  
**方法**: `GET`, `POST`  
**描述**: 获取指定Talos表的结构信息  
**权限要求**: 无  

**请求参数**:
| 参数名 | 类型 | 必填 | 描述 |
|-------|------|------|------|
| name | string | 是 | Talos表名称 |

**响应示例**:
```json
{
    "code": 0,
    "msg": "success",
    "data": {
        "columns": [
            {
                "name": "id",
                "type": "BIGINT",
                "nullable": false
            },
            {
                "name": "name",
                "type": "VARCHAR",
                "nullable": true
            }
        ],
        "partitions": [
            "dt"
        ]
    }
}
```

## 错误码说明

所有接口可能返回的错误码：

| 错误码 | 描述 |
|-------|------|
| 0 | 成功 |
| 400 | 参数错误 |
| 500 | 内部服务器错误 |

## 注意事项

1. 健康检查接口建议配置较短的超时时间
2. 告警消息不应包含敏感信息
3. 接收者列表应使用规范的用户标识
4. Talos表结构查询可能需要一定的响应时间
5. 建议对告警接口调用进行频率限制
6. 系统异常时健康检查接口可能无法响应 