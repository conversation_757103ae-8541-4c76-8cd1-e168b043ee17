# 告警管理接口文档

本文档描述了与告警管理相关的所有API接口。这些接口用于管理系统告警的配置和控制。

## 接口列表

### 1. 设置告警静默
**接口**: `/keep_quiet/<id>`  
**方法**: `GET`, `POST`  
**描述**: 设置指定告警的静默时间  
**权限要求**: 无  

**路径参数**:

| 参数名 | 类型    | 描述     |
|--------|---------|----------|
| id     | integer | 告警ID   |

**请求参数**:

| 参数名            | 类型    | 必填 | 描述             |
|------------------|---------|------|------------------|
| keep_quiet_n_mins | integer | 是   | 静默时间（分钟） |

**响应示例**:
```json
{
    "code": 0,
    "msg": "success",
    "data": null
}
```

## 错误码说明

所有接口可能返回的错误码：

| 错误码 | 描述               |
|--------|-------------------|
| 0      | 成功              |
| 404    | 资源不存在         |
| 500    | 内部服务器错误     |

## 注意事项

1. 静默时间设置后立即生效
2. 在静默期间，相同类型的告警将不会重复发送
3. 静默时间到期后，告警会恢复正常发送
4. 建议根据告警的重要程度设置合适的静默时间
5. 系统会记录告警的静默操作日志
6. 对于重要的告警，不建议设置过长的静默时间 