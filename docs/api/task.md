# 任务管理接口文档

本文档描述了任务管理相关的所有 API 接口。

## 接口列表

### 1. 文档解析
**接口**: `/doc/parse`  
**方法**: `POST`  
**描述**: 解析上传的文档文件  
**权限要求**: 无  

**请求参数**:

| 参数名   | 类型  | 必填 | 描述                             |
|----------|-------|------|----------------------------------|
| docfile  | File  | 是   | 要解析的文档文件，支持的格式：doc, docx |

**响应示例**:
```json
{
    "code": 0,
    "msg": "success",
    "data": null
}
```

### 2. 获取文档
**接口**: `/doc/download`  
**方法**: `GET`  
**描述**: 下载指定来源和目标的文档  
**权限要求**: 无  

**请求参数**:

| 参数名 | 类型   | 必填 | 描述           |
|--------|--------|------|----------------|
| source | string | 是   | 数据源标识     |
| sink   | string | 是   | 数据目标标识   |

### 3. 根据ID下载文档
**接口**: `/doc/download/<id>`  
**方法**: `GET`  
**描述**: 根据任务ID下载对应的文档  
**权限要求**: 需要登录，需要任务读取权限  

**路径参数**:

| 参数名 | 类型    | 描述     |
|--------|---------|----------|
| id     | integer | 任务ID   |

### 4. 获取任务列表
**接口**: `/`  
**方法**: `GET`  
**描述**: 获取当前用户可访问的所有任务  
**权限要求**: 需要登录  

**响应示例**:
```json
{
    "code": 0,
    "msg": "success",
    "data": [
        {
            "id": 1,
            "name": "task_name",
            "status": "success"
            // ... 其他任务属性
        }
    ]
}
```

### 5. 搜索任务
**接口**: `/search`  
**方法**: `POST`  
**描述**: 根据条件搜索任务  
**权限要求**: 需要登录  

**请求参数**:

| 参数名         | 类型     | 必填 | 描述           |
|----------------|----------|------|----------------|
| create_user    | string   | 否   | 创建用户       |
| name           | string   | 否   | 任务名称       |
| trans_type     | string   | 否   | 传输类型       |
| status         | string   | 否   | 任务状态       |
| apply_id       | integer  | 否   | 申请ID         |
| task_type_id   | integer  | 否   | 任务类型ID     |
| engine_id      | integer  | 否   | 引擎ID         |
| project_id     | integer  | 否   | 项目ID         |
| create_time    | string   | 否   | 创建时间       |
| source_org_id  | integer  | 否   | 源组织ID       |
| sink_org_id    | integer  | 否   | 目标组织ID     |

### 6. 获取任务详情
**接口**: `/<id>`  
**方法**: `GET`  
**描述**: 获取指定ID的任务详情  
**权限要求**: 需要登录，需要任务读取权限  

**路径参数**:

| 参数名 | 类型    | 描述     |
|--------|---------|----------|
| id     | integer | 任务ID   |

### 7. 根据名称获取任务
**接口**: `/<name>`  
**方法**: `GET`  
**描述**: 根据任务名称获取任务详情  
**权限要求**: 无  

**路径参数**:

| 参数名 | 类型   | 描述       |
|--------|--------|------------|
| name   | string | 任务名称   |

### 8. 更新任务
**接口**: `/<id>`  
**方法**: `POST`  
**描述**: 更新指定ID的任务信息  
**权限要求**: 需要登录，需要任务写入权限  

**路径参数**:

| 参数名 | 类型    | 描述     |
|--------|---------|----------|
| id     | integer | 任务ID   |

**请求参数**:

| 参数名         | 类型     | 必填 | 描述           |
|----------------|----------|------|----------------|
| name           | string   | 否   | 任务名称       |
| tran_type      | string   | 否   | 传输类型       |
| alarm_receivers | string   | 否   | 告警接收人     |
| params         | object   | 否   | 任务参数       |
| engine_id      | integer  | 否   | 引擎ID         |
| source_configs  | object   | 否   | 源配置         |
| sink_configs    | object   | 否   | 目标配置       |

### 9. 下线任务
**接口**: `/offline/<id>`  
**方法**: `GET`  
**描述**: 将指定任务下线  
**权限要求**: 需要登录，需要任务写入权限  

**路径参数**:

| 参数名 | 类型    | 描述     |
|--------|---------|----------|
| id     | integer | 任务ID   |

### 10. 上线任务
**接口**: `/online/<id>`  
**方法**: `GET`  
**描述**: 将指定任务上线  
**权限要求**: 需要登录，需要任务写入权限  

**路径参数**:

| 参数名 | 类型    | 描述     |
|--------|---------|----------|
| id     | integer | 任务ID   |

### 11. 获取任务命令
**接口**: `/cmd/<id>`  
**方法**: `GET`  
**描述**: 获取任务的执行命令  
**权限要求**: 无  

**路径参数**:

| 参数名 | 类型    | 描述     |
|--------|---------|----------|
| id     | integer | 任务ID   |

**查询参数**:

| 参数名     | 类型   | 必填 | 描述           |
|------------|--------|------|----------------|
| etl_system | string | 否   | ETL系统类型     |

### 12. 获取任务申请信息
**接口**: `/apply/<id>`  
**方法**: `GET`  
**描述**: 获取任务的申请信息  
**权限要求**: 需要登录，需要任务读取权限  

**路径参数**:

| 参数名 | 类型    | 描述     |
|--------|---------|----------|
| id     | integer | 任务ID   |

### 13. 执行任务
**接口**: `/exec/<name>`  
**方法**: `GET`  
**描述**: 执行指定名称的任务  
**权限要求**: 需要登录，需要任务执行权限  

**路径参数**:

| 参数名 | 类型   | 描述       |
|--------|--------|------------|
| name   | string | 任务名称   |

**查询参数**:

| 参数名   | 类型   | 必填 | 描述           |
|----------|--------|------|----------------|
| params   | string | 否   | 任务参数(HOCON格式) |
| etl_date | string | 否   | ETL日期       |

### 14. 获取执行记录
**接口**: `/execution/<id>`  
**方法**: `GET`  
**描述**: 获取任务的执行记录  
**权限要求**: 需要登录，需要任务读取权限  

**路径参数**:

| 参数名 | 类型    | 描述     |
|--------|---------|----------|
| id     | integer | 任务ID   |

### 15. ETL任务执行
**接口**: `/etl/<name>`  
**方法**: `GET`  
**描述**: 执行ETL任务  
**权限要求**: 无  

**路径参数**:

| 参数名 | 类型   | 描述       |
|--------|--------|------------|
| name   | string | 任务名称   |

**查询参数**:

| 参数名   | 类型   | 必填 | 描述           |
|----------|--------|------|----------------|
| params   | string | 否   | 任务参数(HOCON格式) |
| etl_date | string | 否   | ETL日期       |

### 16. 创建任务
**接口**: `/create`  
**方法**: `POST`  
**描述**: 创建新任务  
**权限要求**: 无  

**请求参数**:

| 参数名         | 类型     | 必填 | 描述           |
|----------------|----------|------|----------------|
| whoami         | string   | 是   | 创建者标识     |
| engine         | string   | 是   | 引擎名称       |
| name           | string   | 是   | 任务名称       |
| type           | string   | 是   | 任务类型       |
| source_configs  | object   | 是   | 源配置         |
| sink_configs    | object   | 是   | 目标配置       |
| project        | string   | 是   | 项目名称       |

### 17. 创建临时任务
**接口**: `/tmp`  
**方法**: `POST`  
**描述**: 创建临时任务  
**权限要求**: 无  

**请求参数**:

| 参数名         | 类型     | 必填 | 描述           |
|----------------|----------|------|----------------|
| whoami         | string   | 是   | 创建者标识     |
| engine         | string   | 是   | 引擎名称       |
| name           | string   | 是   | 任务名称       |
| type           | string   | 是   | 任务类型       |
| source_configs  | object   | 是   | 源配置         |
| sink_configs    | object   | 是   | 目标配置       |

## 错误码说明

所有接口可能返回的错误码：

| 错误码 | 描述               |
|--------|--------------------|
| 0      | 成功               |
| 400    | 参数错误           |
| 404    | 资源不存在         |
| 500    | 内部服务器错误     |

## 注意事项

1. 所有需要登录的接口都需要在请求头中携带有效的认证信息。
2. 部分接口需要特定的任务权限（读取、写入、执行等）。
3. 时间相关的参数格式应为 ISO 8601 标准格式。
4. 文件上传接口仅支持 doc 和 docx 格式的文件。
5. params 参数使用 HOCON 格式的字符串。 