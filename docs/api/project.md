# 项目管理接口文档

本文档描述了与项目管理相关的所有API接口。这些接口用于管理系统中的项目信息和成员关系。

## 接口列表

### 1. 搜索项目
**接口**: `/search`  
**方法**: `POST`  
**描述**: 搜索并分页获取项目列表  
**权限要求**: 需要登录  

**请求参数**:

| 参数名 | 类型 | 必填 | 描述 |
|-------|------|------|------|
| page | object | 否 | 分页参数 |
| query | object | 否 | 查询条件 |

**分页参数格式**:
```json
{
    "page": 1,
    "per_page": 10
}
```

**查询条件格式**:
```json
{
    "name": "项目名称",
    "create_user": "创建者ID"
}
```

**响应示例**:
```json
{
    "code": 0,
    "msg": "success",
    "data": {
        "items": [
            {
                "id": 1,
                "name": "数据同步项目",
                "desc": "用于数据同步的项目",
                "admins": "user001,user002",
                "create_time": "2024-01-20 10:00:00",
                "create_user": "admin",
                "update_time": "2024-01-20 10:00:00",
                "update_user": "admin"
            }
        ],
        "total": 100,
        "page": 1,
        "per_page": 10
    }
}
```

### 2. 创建项目
**接口**: `/add`  
**方法**: `POST`  
**描述**: 创建新项目  
**权限要求**: 需要登录  

**请求参数**:

| 参数名 | 类型 | 必填 | 描述 |
|-------|------|------|------|
| name | string | 是 | 项目名称 |
| desc | string | 否 | 项目描述 |
| admins | string | 否 | 项目管理员列表，多个管理员用逗号分隔 |

**响应示例**:
```json
{
    "code": 0,
    "msg": "success",
    "data": {
        "id": 1,
        "admins": ["user001", "user002"]
    }
}
```

### 3. 更新项目
**接口**: `/<id>`  
**方法**: `POST`  
**描述**: 更新项目信息  
**权限要求**: 需要登录，且必须是项目管理员  

**路径参数**:

| 参数名 | 类型 | 描述 |
|-------|------|------|
| id | integer | 项目ID |

**请求参数**:

| 参数名 | 类型 | 必填 | 描述 |
|-------|------|------|------|
| name | string | 否 | 项目名称 |
| desc | string | 否 | 项目描述 |
| admins | string | 否 | 项目管理员列表 |

**响应示例**:
```json
{
    "code": 0,
    "msg": "success",
    "data": null
}
```

### 4. 删除项目
**接口**: `/<id>`  
**方法**: `DELETE`  
**描述**: 删除项目  
**权限要求**: 需要登录，且必须是项目管理员  

**路径参数**:

| 参数名 | 类型 | 描述 |
|-------|------|------|
| id | integer | 项目ID |

**响应示例**:
```json
{
    "code": 0,
    "msg": "success",
    "data": null
}
```

## 项目角色说明

| 角色 | 描述 | 权限 |
|------|------|------|
| ADMIN | 管理员 | 可以管理项目信息、成员和任务 |
| MEMBER | 普通成员 | 可以查看项目信息和执行授权任务 |

## 错误码说明

所有接口可能返回的错误码：

| 错误码 | 描述 |
|-------|------|
| 0 | 成功 |
| 400 | 参数错误 |
| 403 | 无权限 |
| 404 | 资源不存在 |
| 409 | 资源已存在 |
| 500 | 内部服务器错误 |

## 注意事项

1. 所有接口都需要用户登录
2. 项目名称在系统中必须唯一
3. 创建项目时，创建者会自动成为项目管理员
4. 项目管理员列表中的用户必须是系统中已存在的用户
5. 删除项目前需要确保项目下没有正在运行的任务
6. 更新项目信息时不会影响已有的任务配置
7. 建议在项目描述中详细说明项目的用途和注意事项
8. 管理员变更会影响到项目内所有资源的权限控制 