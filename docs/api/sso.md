# 单点登录接口文档

本文档描述了与单点登录(SSO)相关的所有API接口。这些接口用于处理用户认证和审批回调。

## 接口列表

### 1. 登录
**接口**: `/login`  
**方法**: `GET`, `POST`  
**描述**: 处理用户登录请求  
**权限要求**: 无  

**响应说明**: 根据配置的认证模块返回相应的登录响应，可能会重定向到认证服务器

### 2. 登录回调
**接口**: `/logincallback`  
**方法**: `GET`, `POST`  
**描述**: 处理认证服务器的登录回调  
**权限要求**: 无  

**响应说明**: 处理认证服务器的回调请求，完成用户登录流程

### 3. 登出
**接口**: `/logout`  
**方法**: `GET`, `POST`  
**描述**: 处理用户登出请求  
**权限要求**: 无  

**响应说明**: 清除用户的登录状态，可能会重定向到认证服务器的登出页面

### 4. 审批回调
**接口**: `/auditcallback`  
**方法**: `POST`  
**描述**: 处理审批系统的回调请求  
**权限要求**: 无  

**请求参数**:

| 参数名 | 类型 | 必填 | 描述 |
|-------|------|------|------|
| sponsor | string | 是 | 发起人 |
| status | integer | 是 | 审批状态 |
| values | array | 是 | 审批数据 |

**审批状态说明**:

| 状态码 | 描述 |
|-------|------|
| 0 | 初始状态 |
| 1 | 审批中 |
| 2 | 撤回 |
| 3 | 通过 |
| 4 | 拒绝 |

**响应示例**:
```json
{
    "code": 0,
    "msg": "success",
    "data": {
        "status": 0
    }
}
```

## 错误码说明

所有接口可能返回的错误码：

| 错误码 | 描述 |
|-------|------|
| 0 | 成功 |
| 400 | 参数错误 |
| 500 | 内部服务器错误 |

## 注意事项

1. 认证模块可以通过配置文件进行配置
2. 登录接口会根据配置的认证类型进行相应的处理
3. 登录回调接口需要处理认证服务器的各种回调情况
4. 登出接口会清理用户的会话信息
5. 审批回调接口会根据审批状态自动处理申请
6. 审批拒绝时，用户需要在审批系统中查看具体的拒绝原因
7. 所有接口的调用都会被记录在系统日志中
8. 建议在生产环境中使用HTTPS协议保护接口调用 