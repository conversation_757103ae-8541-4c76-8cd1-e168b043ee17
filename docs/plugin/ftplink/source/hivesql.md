# HiveSQL 源组件配置说明

## 组件说明
HiveSQL源组件，用于执行Hive SQL查询并获取结果数据。支持多种查询方式和数据格式。

## 配置参数

| 参数名 | 中文名称 | 类型 | 必填 | 默认值 | 示例值 | 说明 |
|-------|---------|------|------|--------|--------|------|
| sql | SQL语句 | string | 是 | - | SELECT * FROM test_table WHERE dt='${now.delta(1).datekey}' | Hive SQL查询语句 |
| database | 数据库名 | string | 是 | - | test_db | Hive数据库名称 |
| queue | 队列名称 | string | 否 | default | adhoc | YARN资源队列名称 |
| engine | 执行引擎 | string | 否 | mr | mr,tez,spark | SQL查询执行引擎 |
| priority | 优先级 | int | 否 | 0 | 1 | 查询优先级，值越大优先级越高 |
| timeout | 超时时间 | int | 否 | 3600 | 7200 | 查询超时时间（秒） |
| max_rows | 最大行数 | int | 否 | - | 1000000 | 最大返回行数 |
| batch_size | 批量大小 | int | 否 | 1000 | 5000 | 每批次处理的记录数 |
| retry_times | 重试次数 | int | 否 | 3 | 5 | 查询失败重试次数 |
| retry_interval | 重试间隔 | int | 否 | 60 | 120 | 重试间隔时间（秒） |
| output_format | 输出格式 | string | 否 | csv | csv,json,parquet | 结果数据的输出格式 |
| field_delimiter | 字段分隔符 | string | 否 | , | \t | CSV格式的字段分隔符 |
| null_value | 空值替换 | string | 否 | NULL | - | 空值的替换字符串 |

## 功能特性

1. **查询支持**
   - 支持复杂SQL查询
   - 支持多种执行引擎
   - 支持资源队列配置
   - 支持优先级设置

2. **数据处理**
   - 支持批量处理
   - 支持最大行数限制
   - 支持多种输出格式
   - 支持自定义分隔符

3. **执行控制**
   - 支持超时控制
   - 支持失败重试
   - 支持并发控制
   - 支持资源管理

## 使用示例

### 基本查询配置
```json
{
    "sql": "SELECT * FROM test_table WHERE dt='${now.delta(1).datekey}'",
    "database": "test_db",
    "queue": "adhoc",
    "timeout": 3600,
    "max_rows": 1000000
}
```

### 高级查询配置
```json
{
    "sql": "SELECT t1.*, t2.name FROM test_table1 t1 JOIN test_table2 t2 ON t1.id = t2.id WHERE t1.dt='${now.delta(1).datekey}'",
    "database": "test_db",
    "queue": "adhoc",
    "engine": "tez",
    "priority": 1,
    "timeout": 7200,
    "batch_size": 5000,
    "retry_times": 5,
    "retry_interval": 120,
    "output_format": "json"
}
```

### 自定义输出配置
```json
{
    "sql": "SELECT * FROM test_table WHERE dt='${now.delta(1).datekey}'",
    "database": "test_db",
    "queue": "adhoc",
    "output_format": "csv",
    "field_delimiter": "\t",
    "null_value": "N/A",
    "batch_size": 5000,
    "max_rows": 1000000
}
```

## 注意事项

1. SQL编写：
   - 使用分区过滤提高性能
   - 避免全表扫描
   - 合理使用JOIN操作
   - 注意数据倾斜问题

2. 资源配置：
   - 选择合适的执行引擎
   - 合理设置队列资源
   - 控制查询并发数
   - 避免资源争用

3. 性能优化：
   - 合理设置批量大小
   - 使用合适的压缩格式
   - 优化数据分布
   - 控制结果集大小

4. 错误处理：
   - 实现重试机制
   - 处理超时情况
   - 记录错误日志
   - 设置告警阈值

5. 监控建议：
   - 监控查询执行时间
   - 监控资源使用情况
   - 监控数据质量
   - 监控任务状态 