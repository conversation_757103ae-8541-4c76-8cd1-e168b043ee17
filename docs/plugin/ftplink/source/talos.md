# Talos 源组件配置说明

## 组件说明
Talos消息队列源组件，用于从Talos消息队列中读取数据。支持多种消费模式和数据格式。

## 配置参数

| 参数名 | 中文名称 | 类型 | 必填 | 默认值 | 示例值 | 说明 |
|-------|---------|------|------|--------|--------|------|
| topic | 主题名称 | string | 是 | - | test_topic | Talos主题名称 |
| consumer_group | 消费组 | string | 是 | - | test_group | 消费组名称 |
| partition_id | 分区ID | int | 否 | - | 0 | 指定消费的分区ID，不指定则消费所有分区 |
| start_time | 起始时间 | string | 否 | - | ${now.delta(1).datekey} 00:00:00 | 开始消费的时间点 |
| end_time | 结束时间 | string | 否 | - | ${now.datekey} 00:00:00 | 结束消费的时间点 |
| batch_size | 批量大小 | int | 否 | 100 | 1000 | 每批次读取的消息数 |
| max_fetch_bytes | 最大获取字节数 | int | 否 | 1048576 | 2097152 | 单次拉取的最大字节数 |
| timeout | 超时时间 | int | 否 | 30 | 60 | 单次拉取的超时时间（秒） |
| max_retries | 最大重试次数 | int | 否 | 3 | 5 | 消费失败时的最大重试次数 |
| retry_interval | 重试间隔 | int | 否 | 1 | 2 | 重试间隔时间（秒） |
| message_format | 消息格式 | string | 否 | json | json,text,avro | 消息的序列化格式 |
| schema_registry | Schema注册表 | string | 否 | - | http://schema-registry:8081 | Schema注册表地址（Avro格式需要） |

## 功能特性

1. **消费模式**
   - 支持指定分区消费
   - 支持时间范围消费
   - 支持批量消费
   - 支持消费组模式

2. **数据格式**
   - 支持JSON格式
   - 支持文本格式
   - 支持Avro格式
   - 支持自定义序列化

3. **容错机制**
   - 自动重试机制
   - 超时控制
   - 错误处理
   - 消费位点管理

## 使用示例

### 基本配置
```json
{
    "topic": "test_topic",
    "consumer_group": "test_group",
    "batch_size": 1000,
    "message_format": "json",
    "timeout": 60
}
```

### 指定分区和时间范围的配置
```json
{
    "topic": "test_topic",
    "consumer_group": "test_group",
    "partition_id": 0,
    "start_time": "${now.delta(1).datekey} 00:00:00",
    "end_time": "${now.datekey} 00:00:00",
    "batch_size": 1000,
    "message_format": "json",
    "timeout": 60,
    "max_retries": 5
}
```

### Avro格式配置
```json
{
    "topic": "test_topic",
    "consumer_group": "test_group",
    "batch_size": 1000,
    "message_format": "avro",
    "schema_registry": "http://schema-registry:8081",
    "timeout": 60,
    "max_fetch_bytes": 2097152
}
```

## 注意事项

1. 消费配置：
   - 合理设置batch_size，避免单批次数据过大
   - 根据数据量设置合适的max_fetch_bytes
   - 设置适当的超时时间和重试策略

2. 性能优化：
   - 使用批量消费提高吞吐量
   - 合理设置消费分区数
   - 避免频繁的消费者重平衡

3. 数据一致性：
   - 使用固定的消费组名称
   - 正确处理消费位点
   - 合理设置消费超时时间

4. 错误处理：
   - 实现合适的重试策略
   - 记录消费失败的消息
   - 监控消费延迟情况

5. 监控建议：
   - 监控消费延迟
   - 监控消费速率
   - 监控消费者状态
   - 设置适当的告警阈值 