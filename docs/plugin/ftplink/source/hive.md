# Hive 源组件配置说明

## 组件说明
Hive源组件，用于从Hive数据仓库中读取数据。支持多种数据格式和读取模式。

## 配置参数

| 参数名 | 中文名称 | 类型 | 必填 | 默认值 | 示例值 | 说明 |
|-------|---------|------|------|--------|--------|------|
| database | 数据库名 | string | 是 | - | test_db | Hive数据库名称 |
| table | 表名 | string | 是 | - | test_table | Hive表名称 |
| partition_spec | 分区条件 | string | 否 | - | dt='${now.delta(1).datekey}' | 分区筛选条件 |
| columns | 查询列 | string | 否 | * | id,name,age | 需要查询的列，逗号分隔 |
| where | 过滤条件 | string | 否 | - | age > 18 | WHERE子句条件 |
| queue | 队列名称 | string | 否 | default | adhoc | YARN资源队列名称 |
| engine | 执行引擎 | string | 否 | mr | mr,tez,spark | 查询执行引擎 |
| priority | 优先级 | int | 否 | 0 | 1 | 查询优先级，值越大优先级越高 |
| timeout | 超时时间 | int | 否 | 3600 | 7200 | 查询超时时间（秒） |
| max_rows | 最大行数 | int | 否 | - | 1000000 | 最大返回行数 |
| batch_size | 批量大小 | int | 否 | 1000 | 5000 | 每批次处理的记录数 |
| retry_times | 重试次数 | int | 否 | 3 | 5 | 查询失败重试次数 |
| retry_interval | 重试间隔 | int | 否 | 60 | 120 | 重试间隔时间（秒） |
| output_format | 输出格式 | string | 否 | csv | csv,json,parquet | 结果数据的输出格式 |
| field_delimiter | 字段分隔符 | string | 否 | , | \t | CSV格式的字段分隔符 |
| null_value | 空值替换 | string | 否 | NULL | - | 空值的替换字符串 |

## 功能特性

1. **数据访问**
   - 支持分区表
   - 支持列裁剪
   - 支持条件过滤
   - 支持多种存储格式

2. **执行控制**
   - 支持多种执行引擎
   - 支持资源队列配置
   - 支持优先级设置
   - 支持超时控制

3. **数据处理**
   - 支持批量读取
   - 支持最大行数限制
   - 支持多种输出格式
   - 支持空值处理

## 使用示例

### 基本配置
```json
{
    "database": "test_db",
    "table": "test_table",
    "partition_spec": "dt='${now.delta(1).datekey}'",
    "columns": "id,name,age,gender",
    "queue": "adhoc",
    "timeout": 3600
}
```

### 高级查询配置
```json
{
    "database": "test_db",
    "table": "test_table",
    "partition_spec": "dt='${now.delta(1).datekey}' AND hour='${now.hourkey}'",
    "columns": "id,name,age,gender,score",
    "where": "age >= 18 AND score > 60",
    "queue": "adhoc",
    "engine": "tez",
    "priority": 1,
    "timeout": 7200,
    "batch_size": 5000,
    "retry_times": 5,
    "retry_interval": 120,
    "output_format": "json"
}
```

### 自定义输出配置
```json
{
    "database": "test_db",
    "table": "test_table",
    "partition_spec": "dt='${now.delta(1).datekey}'",
    "columns": "id,name,age,gender",
    "output_format": "csv",
    "field_delimiter": "\t",
    "null_value": "N/A",
    "batch_size": 5000,
    "max_rows": 1000000
}
```

## 注意事项

1. 查询优化：
   - 使用分区过滤提高性能
   - 只查询必要的列
   - 添加合适的过滤条件
   - 控制结果集大小

2. 资源管理：
   - 选择合适的执行引擎
   - 合理设置队列资源
   - 控制查询并发数
   - 避免资源争用

3. 性能调优：
   - 优化数据存储格式
   - 合理设置批量大小
   - 使用适当的压缩
   - 控制数据倾斜

4. 错误处理：
   - 实现重试机制
   - 处理超时情况
   - 记录错误日志
   - 设置告警阈值

5. 监控建议：
   - 监控查询执行时间
   - 监控资源使用情况
   - 监控数据质量
   - 监控任务状态

6. 数据质量：
   - 验证数据完整性
   - 检查数据一致性
   - 处理异常数据
   - 记录数据统计信息 