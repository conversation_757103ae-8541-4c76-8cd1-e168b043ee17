# FTP 源组件配置说明

## 组件说明
FTP/SFTP远程文件服务源组件，用于从远程FTP服务器读取文件数据。

## 配置参数

| 参数名 | 中文名称 | 类型 | 必填 | 默认值 | 示例值 | 说明 |
|-------|---------|------|------|--------|--------|------|
| file_name | 文件名规则 | string | 是 | - | meituan_firpos_custlist_${now.delta(1).datekey}.txt | 目标文件名规则，支持匹配符，支持列表(逗号分隔) |
| file_path | 文件路径 | string | 是 | - | card/gd/${now.delta(1).datekey} | 目标文件存放路径，相对路径从/one-sftp-xy-bank/queqiao后开始 |
| ok_file | 就绪文件规则 | string | 否 | - | .ok | 就绪文件名，解析规则见: https://km.sankuai.com/page/********** |
| target_sep | 列分隔符 | string | 是 | , | , | 文件中的列分隔符，支持多位字符，不可见字符（\x01）请使用变量$sep01 |
| row_sep_to | 替换后的行分隔符 | string | 否 | - | , | row_sep_to替换成row_sep_from行分隔符 |
| row_sep_from | 原行分隔符 | string | 否 | - | , | row_sep_from替换成row_sep_to行分隔符 |
| has_header | 文件头 | boolean | 是 | 0 | - | 文件是否存在文件头，入库时将去除文件头 |
| table_cols | 字段列表 | string | 否 | - | col1,col2,col3 | 文件数据对应的字段列表，逗号隔开，与文件头配置的关系见: https://km.sankuai.com/page/********** |
| compress_type | 压缩类型 | set | 是 | none | none,zip,gzip,tar.gz | 目标文件的压缩类型，none为普通文本文件 |
| compress_passwd | 压缩密码 | string | 否 | - | - | 若压缩包有加密则填入解压密码 |
| compress_with_ok_file | 和标识文件一起打包 | boolean | 否 | - | - | 是否和标识文件一起打包 |
| compress_merge_suffix | 文件合并后缀 | string | 否 | - | - | 文件合并的后缀 |
| lastest_ready_time | 最晚就绪时间 | string | 是 | 9999 | 0530 | 超出此时间将触发延迟告警，小于0表示不告警 |
| ftp_polling_sec | 轮询间隔时间 | int | 是 | 600 | - | 间隔性检测ftp文件是否就绪的时间，若无就绪文件且文件较大，建议使用长轮询时间避免读到中间态文件 |
| max_waiting_hours | 最大等待时长 | int | 是 | 12 | 12 | 轮询等待远程文件的最长时间（单位小时） |
| file_encode | 目标文件编码 | string | 否 | utf-8 | utf-8,gbk | 目标文件的编码格式，默认以utf8编码解析 |
| conflict_sep | 处理分隔符冲突 | boolean | 否 | 0 | - | 处理源文件中的分隔符冲突问题，重写为不可见字符 |

## 功能特性

1. **文件名匹配**
   - 支持通配符匹配
   - 支持多文件列表（逗号分隔）
   - 支持日期变量替换

2. **文件就绪检测**
   - 支持OK文件检测
   - 支持文件元数据校验（MD5、大小、行数、列数）
   - 支持最晚就绪时间告警

3. **文件格式处理**
   - 支持多种压缩格式（zip、gzip、tar.gz）
   - 支持压缩包密码
   - 支持多种文件编码
   - 支持文件头处理
   - 支持分隔符冲突处理

4. **数据质量保证**
   - 文件完整性校验
   - 文件格式校验
   - 数据结构校验

## 使用示例

### 基本配置
```json
{
    "file_name": "data_${now.delta(1).datekey}.txt",
    "file_path": "data/daily",
    "target_sep": ",",
    "has_header": "1",
    "table_cols": "id,name,age,gender",
    "compress_type": "none",
    "lastest_ready_time": "0800",
    "ftp_polling_sec": "300",
    "max_waiting_hours": "4"
}
```

### 带压缩和就绪文件的配置
```json
{
    "file_name": "data_${now.delta(1).datekey}.txt",
    "file_path": "data/daily",
    "ok_file": ".ok",
    "target_sep": ",",
    "has_header": "1",
    "compress_type": "zip",
    "compress_passwd": "password123",
    "lastest_ready_time": "0800",
    "ftp_polling_sec": "300",
    "max_waiting_hours": "4",
    "file_encode": "gbk"
}
```

## 注意事项

1. 文件名支持变量替换，常用变量：
   - ${now.datekey}: 当前日期（YYYYMMDD）
   - ${now.delta(n).datekey}: n天前的日期
   - ${now.hourkey}: 当前小时（HH）

2. 文件就绪检测：
   - 建议使用OK文件机制确保数据完整性
   - 合理设置轮询间隔和最大等待时间
   - 对于大文件，建议使用较长的轮询间隔

3. 编码和分隔符：
   - 确保正确设置文件编码
   - 如果数据中包含分隔符，建议启用conflict_sep

4. 性能优化：
   - 合理设置轮询间隔，避免频繁访问FTP服务器
   - 对于大文件，建议使用压缩传输
   - 使用OK文件机制避免读取未完成的文件 