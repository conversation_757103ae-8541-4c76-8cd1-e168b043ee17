# Fate 源组件配置说明

## 组件说明
Fate源组件，用于从FATE（Federated AI Technology Enabler）系统中读取数据。支持多种数据类型和访问模式。

## 配置参数

| 参数名 | 中文名称 | 类型 | 必填 | 默认值 | 示例值 | 说明 |
|-------|---------|------|------|--------|--------|------|
| party_id | 参与方ID | string | 是 | - | 10000 | FATE参与方的唯一标识符 |
| role | 角色 | string | 是 | - | guest | 参与方在联邦学习中的角色(guest/host/arbiter) |
| namespace | 命名空间 | string | 是 | - | experiment_001 | 数据表的命名空间 |
| name | 数据表名 | string | 是 | - | breast_hetero_guest | 数据表的名称 |
| data_type | 数据类型 | string | 否 | table | table,model,metric | 要获取的数据类型 |
| storage_engine | 存储引擎 | string | 否 | eggroll | eggroll,hdfs,mysql | 数据存储引擎 |
| partition_num | 分区数 | int | 否 | 1 | 4 | 数据分区数量 |
| batch_size | 批量大小 | int | 否 | 1000 | 5000 | 每批次读取的记录数 |
| timeout | 超时时间 | int | 否 | 3600 | 7200 | 读取超时时间（秒） |
| retry_times | 重试次数 | int | 否 | 3 | 5 | 读取失败重试次数 |
| retry_interval | 重试间隔 | int | 否 | 60 | 120 | 重试间隔时间（秒） |
| output_format | 输出格式 | string | 否 | csv | csv,json,parquet | 数据输出格式 |
| field_delimiter | 字段分隔符 | string | 否 | , | \t | CSV格式的字段分隔符 |
| with_label | 包含标签 | boolean | 否 | false | true | 是否包含标签列 |
| label_name | 标签列名 | string | 否 | y | label | 标签列的名称 |
| selected_cols | 选择列 | string | 否 | - | id,x1,x2,x3 | 需要选择的列，逗号分隔 |

## 功能特性

1. **数据访问**
   - 支持表格数据
   - 支持模型数据
   - 支持指标数据
   - 支持多种存储引擎

2. **数据处理**
   - 支持批量读取
   - 支持列选择
   - 支持标签处理
   - 支持多种输出格式

3. **安全特性**
   - 支持数据加密
   - 支持访问控制
   - 支持数据隔离
   - 支持隐私保护

## 使用示例

### 基本配置
```json
{
    "party_id": "10000",
    "role": "guest",
    "namespace": "experiment_001",
    "name": "breast_hetero_guest",
    "data_type": "table",
    "batch_size": 5000,
    "output_format": "csv"
}
```

### 完整配置
```json
{
    "party_id": "10000",
    "role": "guest",
    "namespace": "experiment_001",
    "name": "breast_hetero_guest",
    "data_type": "table",
    "storage_engine": "hdfs",
    "partition_num": 4,
    "batch_size": 5000,
    "timeout": 7200,
    "retry_times": 5,
    "retry_interval": 120,
    "output_format": "json",
    "with_label": true,
    "label_name": "y",
    "selected_cols": "id,x1,x2,x3,y"
}
```

### 模型数据配置
```json
{
    "party_id": "10000",
    "role": "guest",
    "namespace": "experiment_001",
    "name": "hetero_lr_model",
    "data_type": "model",
    "storage_engine": "eggroll",
    "timeout": 3600,
    "output_format": "json"
}
```

## 注意事项

1. 数据访问：
   - 确保有足够的访问权限
   - 正确设置参与方信息
   - 选择合适的存储引擎
   - 注意数据隐私保护

2. 性能优化：
   - 合理设置分区数
   - 优化批量大小
   - 选择适当的存储引擎
   - 控制数据读取量

3. 安全考虑：
   - 保护敏感数据
   - 控制数据访问范围
   - 遵守隐私保护规则
   - 加密重要信息

4. 错误处理：
   - 实现重试机制
   - 处理超时情况
   - 记录错误日志
   - 设置告警阈值

5. 监控建议：
   - 监控数据读取进度
   - 监控资源使用情况
   - 监控数据质量
   - 监控安全事件

6. 数据质量：
   - 验证数据完整性
   - 检查数据一致性
   - 处理异常数据
   - 记录数据统计信息 