# HTTP 源组件配置说明

## 组件说明
HTTP源组件，用于通过HTTP/HTTPS协议从远程服务器获取数据。支持多种请求方式和数据格式。

## 配置参数

| 参数名 | 中文名称 | 类型 | 必填 | 默认值 | 示例值 | 说明 |
|-------|---------|------|------|--------|--------|------|
| url | 请求地址 | string | 是 | - | https://api.example.com/data | HTTP/HTTPS请求的完整URL |
| method | 请求方法 | string | 否 | GET | GET,POST,PUT,DELETE | HTTP请求方法 |
| headers | 请求头 | map | 否 | - | {"Content-Type": "application/json"} | HTTP请求头信息 |
| params | 查询参数 | map | 否 | - | {"date": "${now.datekey}"} | URL查询参数 |
| body | 请求体 | string | 否 | - | {"key": "value"} | POST/PUT请求的数据体 |
| auth_type | 认证类型 | string | 否 | none | none,basic,bearer,digest | 认证方式 |
| username | 用户名 | string | 否 | - | user | Basic认证的用户名 |
| password | 密码 | string | 否 | - | pass | Basic认证的密码 |
| token | 令牌 | string | 否 | - | xyz123 | Bearer认证的token |
| timeout | 超时时间 | int | 否 | 30 | 60 | 请求超时时间（秒） |
| retry_times | 重试次数 | int | 否 | 3 | 5 | 请求失败重试次数 |
| retry_interval | 重试间隔 | int | 否 | 1 | 2 | 重试间隔时间（秒） |
| verify_ssl | 验证SSL | boolean | 否 | true | false | 是否验证SSL证书 |
| response_type | 响应类型 | string | 否 | json | json,text,binary | 期望的响应数据类型 |
| success_codes | 成功状态码 | list | 否 | [200] | [200,201,202] | 视为成功的HTTP状态码列表 |

## 功能特性

1. **请求方式**
   - 支持GET/POST/PUT/DELETE
   - 支持查询参数
   - 支持请求体
   - 支持自定义请求头

2. **认证方式**
   - 支持Basic认证
   - 支持Bearer Token
   - 支持Digest认证
   - 支持自定义认证

3. **数据格式**
   - 支持JSON格式
   - 支持文本格式
   - 支持二进制格式
   - 支持自定义格式

## 使用示例

### 基本GET请求配置
```json
{
    "url": "https://api.example.com/data",
    "method": "GET",
    "headers": {
        "Content-Type": "application/json",
        "Accept": "application/json"
    },
    "params": {
        "date": "${now.delta(1).datekey}",
        "type": "daily"
    },
    "timeout": 60
}
```

### POST请求配置
```json
{
    "url": "https://api.example.com/data",
    "method": "POST",
    "headers": {
        "Content-Type": "application/json",
        "Authorization": "Bearer xyz123"
    },
    "body": {
        "start_date": "${now.delta(1).datekey}",
        "end_date": "${now.datekey}",
        "format": "json"
    },
    "timeout": 60,
    "retry_times": 5,
    "response_type": "json"
}
```

### 带认证的请求配置
```json
{
    "url": "https://api.example.com/data",
    "method": "GET",
    "auth_type": "basic",
    "username": "user",
    "password": "pass",
    "verify_ssl": false,
    "timeout": 60,
    "success_codes": [200, 201]
}
```

## 注意事项

1. 请求配置：
   - URL必须包含协议（http/https）
   - 注意请求方法的大小写
   - 合理设置超时时间
   - 谨慎处理SSL验证

2. 认证安全：
   - 避免明文存储密码
   - 定期更新认证信息
   - 使用HTTPS保护数据传输
   - 最小化权限授权

3. 错误处理：
   - 设置合适的重试策略
   - 处理各种HTTP状态码
   - 记录详细的错误信息
   - 实现优雅的失败处理

4. 性能优化：
   - 避免过大的请求数据
   - 合理设置并发请求数
   - 使用连接池
   - 启用压缩传输

5. 监控建议：
   - 监控请求响应时间
   - 监控错误率
   - 监控数据完整性
   - 设置适当的告警阈值 