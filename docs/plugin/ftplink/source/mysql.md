# MySQL 源组件配置说明

## 组件说明
MySQL数据库源组件，用于从MySQL数据库中读取数据。支持多种查询方式和数据导出格式。

## 配置参数

| 参数名 | 中文名称 | 类型 | 必填 | 默认值 | 示例值 | 说明 |
|-------|---------|------|------|--------|--------|------|
| host | 主机地址 | string | 是 | - | mysql.example.com | MySQL服务器地址 |
| port | 端口号 | int | 是 | 3306 | 3306 | MySQL服务器端口 |
| username | 用户名 | string | 是 | - | root | 数据库用户名 |
| password | 密码 | string | 是 | - | password123 | 数据库密码 |
| database | 数据库名 | string | 是 | - | test_db | 要连接的数据库名 |
| table | 表名 | string | 是 | - | user_info | 要查询的表名 |
| query | SQL查询 | string | 否 | - | SELECT * FROM user_info WHERE status = 1 | 自定义SQL查询语句 |
| columns | 查询列 | string | 否 | * | id,name,age | 要查询的列名，逗号分隔 |
| where | 查询条件 | string | 否 | - | age > 18 | WHERE子句条件 |
| partition | 分区信息 | string | 否 | - | p_date=${now.delta(1).datekey} | 分区条件 |
| batch_size | 批量大小 | int | 否 | 1000 | 5000 | 每批次读取的记录数 |
| max_rows | 最大行数 | int | 否 | - | 1000000 | 最大读取的记录数 |
| timeout | 超时时间 | int | 否 | 3600 | 7200 | 查询超时时间（秒） |
| charset | 字符集 | string | 否 | utf8mb4 | utf8mb4 | 数据库连接字符集 |

## 功能特性

1. **查询方式**
   - 支持表名直接查询
   - 支持自定义SQL查询
   - 支持分区表查询
   - 支持条件过滤

2. **数据读取**
   - 支持批量读取
   - 支持最大行数限制
   - 支持超时控制
   - 支持字符集设置

3. **性能优化**
   - 批量读取机制
   - 连接池管理
   - 超时控制

## 使用示例

### 基本配置
```json
{
    "host": "mysql.example.com",
    "port": 3306,
    "username": "reader",
    "password": "password123",
    "database": "test_db",
    "table": "user_info",
    "columns": "id,name,age,gender",
    "batch_size": 5000
}
```

### 带条件的查询配置
```json
{
    "host": "mysql.example.com",
    "port": 3306,
    "username": "reader",
    "password": "password123",
    "database": "test_db",
    "table": "order_info",
    "columns": "order_id,user_id,amount,create_time",
    "where": "create_time >= '${now.delta(1).datekey} 00:00:00' AND create_time < '${now.datekey} 00:00:00'",
    "batch_size": 5000,
    "max_rows": 1000000,
    "timeout": 7200
}
```

### 自定义SQL查询配置
```json
{
    "host": "mysql.example.com",
    "port": 3306,
    "username": "reader",
    "password": "password123",
    "database": "test_db",
    "query": "SELECT o.order_id, u.user_name, o.amount FROM order_info o JOIN user_info u ON o.user_id = u.user_id WHERE o.create_time >= '${now.delta(1).datekey} 00:00:00'",
    "batch_size": 5000,
    "timeout": 7200
}
```

## 注意事项

1. 数据库连接：
   - 确保数据库用户有足够的读取权限
   - 建议使用只读账号
   - 注意设置合适的连接超时时间

2. 查询优化：
   - 对于大表查询，建议使用WHERE条件限制数据范围
   - 合理设置batch_size，过大或过小都会影响性能
   - 必要时使用max_rows限制总记录数

3. 性能考虑：
   - 避免使用`SELECT *`，只查询需要的列
   - 对于分区表，建议指定分区条件
   - 复杂查询建议使用自定义SQL，并确保SQL经过优化

4. 安全性：
   - 密码不要明文存储，建议使用加密方式
   - 定期更新数据库密码
   - 限制查询超时时间，避免长时间占用连接 