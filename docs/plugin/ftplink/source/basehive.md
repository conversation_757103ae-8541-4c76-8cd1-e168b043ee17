# BaseHive 源组件配置说明

## 组件说明
BaseHive源组件，是Hive源组件的基础实现，提供了基本的Hive数据读取功能。支持简单的数据查询和读取操作。

## 配置参数

| 参数名 | 中文名称 | 类型 | 必填 | 默认值 | 示例值 | 说明 |
|-------|---------|------|------|--------|--------|------|
| database | 数据库名 | string | 是 | - | test_db | Hive数据库名称 |
| table | 表名 | string | 是 | - | test_table | Hive表名称 |
| partition_spec | 分区条件 | string | 否 | - | dt='${now.delta(1).datekey}' | 分区筛选条件 |
| columns | 查询列 | string | 否 | * | id,name,age | 需要查询的列，逗号分隔 |
| where | 过滤条件 | string | 否 | - | age > 18 | WHERE子句条件 |
| timeout | 超时时间 | int | 否 | 3600 | 7200 | 查询超时时间（秒） |
| max_rows | 最大行数 | int | 否 | - | 1000000 | 最大返回行数 |
| batch_size | 批量大小 | int | 否 | 1000 | 5000 | 每批次处理的记录数 |
| retry_times | 重试次数 | int | 否 | 3 | 5 | 查询失败重试次数 |
| retry_interval | 重试间隔 | int | 否 | 60 | 120 | 重试间隔时间（秒） |
| output_format | 输出格式 | string | 否 | csv | csv,json | 结果数据的输出格式 |
| field_delimiter | 字段分隔符 | string | 否 | , | \t | CSV格式的字段分隔符 |
| null_value | 空值替换 | string | 否 | NULL | - | 空值的替换字符串 |

## 功能特性

1. **数据访问**
   - 支持分区表
   - 支持列裁剪
   - 支持条件过滤
   - 支持基本数据格式

2. **数据处理**
   - 支持批量读取
   - 支持最大行数限制
   - 支持基本输出格式
   - 支持空值处理

3. **基础功能**
   - 简单查询执行
   - 基本错误处理
   - 重试机制
   - 超时控制

## 使用示例

### 基本配置
```json
{
    "database": "test_db",
    "table": "test_table",
    "partition_spec": "dt='${now.delta(1).datekey}'",
    "columns": "id,name,age,gender",
    "timeout": 3600
}
```

### 带条件的查询配置
```json
{
    "database": "test_db",
    "table": "test_table",
    "partition_spec": "dt='${now.delta(1).datekey}'",
    "columns": "id,name,age,gender,score",
    "where": "age >= 18 AND score > 60",
    "batch_size": 5000,
    "max_rows": 1000000,
    "retry_times": 5,
    "retry_interval": 120,
    "output_format": "json"
}
```

### 自定义输出配置
```json
{
    "database": "test_db",
    "table": "test_table",
    "partition_spec": "dt='${now.delta(1).datekey}'",
    "columns": "id,name,age,gender",
    "output_format": "csv",
    "field_delimiter": "\t",
    "null_value": "N/A",
    "batch_size": 5000
}
```

## 注意事项

1. 查询优化：
   - 使用分区过滤提高性能
   - 只查询必要的列
   - 添加合适的过滤条件
   - 控制结果集大小

2. 性能考虑：
   - 合理设置批量大小
   - 控制查询并发数
   - 避免大结果集查询
   - 注意内存使用

3. 错误处理：
   - 实现重试机制
   - 处理超时情况
   - 记录错误日志
   - 设置告警阈值

4. 数据质量：
   - 验证数据完整性
   - 检查数据一致性
   - 处理异常数据
   - 记录数据统计信息

5. 使用限制：
   - 仅支持基本查询功能
   - 不支持复杂的优化
   - 功能相对简单
   - 适用于简单场景 