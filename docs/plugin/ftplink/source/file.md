# File 源组件配置说明

## 组件说明
本地文件源组件，用于读取本地文件系统中的数据文件。支持多种文件格式和读取模式。

## 配置参数

| 参数名 | 中文名称 | 类型 | 必填 | 默认值 | 示例值 | 说明 |
|-------|---------|------|------|--------|--------|------|
| file_name | 文件名规则 | string | 是 | - | data_${now.delta(1).datekey}.txt | 目标文件名规则，支持匹配符，支持列表(逗号分隔) |
| file_path | 文件路径 | string | 是 | - | /data/daily | 目标文件存放路径，支持绝对路径和相对路径 |
| ok_file | 就绪文件规则 | string | 否 | - | .ok | 就绪文件名，用于标识数据文件是否就绪 |
| target_sep | 列分隔符 | string | 是 | , | , | 文件中的列分隔符，支持多位字符 |
| row_sep_to | 替换后的行分隔符 | string | 否 | - | , | row_sep_to替换成row_sep_from行分隔符 |
| row_sep_from | 原行分隔符 | string | 否 | - | , | row_sep_from替换成row_sep_to行分隔符 |
| has_header | 文件头 | boolean | 是 | 0 | - | 文件是否存在文件头，入库时将去除文件头 |
| table_cols | 字段列表 | string | 否 | - | col1,col2,col3 | 文件数据对应的字段列表，逗号隔开 |
| compress_type | 压缩类型 | set | 是 | none | none,zip,gzip,tar.gz | 目标文件的压缩类型，none为普通文本文件 |
| compress_passwd | 压缩密码 | string | 否 | - | - | 若压缩包有加密则填入解压密码 |
| file_encode | 目标文件编码 | string | 否 | utf-8 | utf-8,gbk | 目标文件的编码格式，默认以utf8编码解析 |
| conflict_sep | 处理分隔符冲突 | boolean | 否 | 0 | - | 处理源文件中的分隔符冲突问题，重写为不可见字符 |
| delete_after_read | 读取后删除 | boolean | 否 | false | true | 是否在成功读取后删除源文件 |
| check_interval | 检查间隔 | int | 否 | 60 | 300 | 文件就绪检查的间隔时间（秒） |
| max_wait_time | 最大等待时间 | int | 否 | 3600 | 7200 | 等待文件就绪的最大时间（秒） |

## 功能特性

1. **文件处理**
   - 支持多种文件格式
   - 支持压缩文件
   - 支持文件编码转换
   - 支持文件头处理

2. **读取模式**
   - 支持批量读取
   - 支持增量读取
   - 支持文件就绪检测
   - 支持读取后删除

3. **数据格式**
   - 支持CSV格式
   - 支持自定义分隔符
   - 支持行列转换
   - 支持分隔符冲突处理

## 使用示例

### 基本配置
```json
{
    "file_name": "data_${now.delta(1).datekey}.txt",
    "file_path": "/data/daily",
    "target_sep": ",",
    "has_header": "1",
    "table_cols": "id,name,age,gender",
    "compress_type": "none",
    "file_encode": "utf-8"
}
```

### 压缩文件配置
```json
{
    "file_name": "data_${now.delta(1).datekey}.txt",
    "file_path": "/data/daily",
    "ok_file": ".ok",
    "target_sep": ",",
    "has_header": "1",
    "compress_type": "zip",
    "compress_passwd": "password123",
    "file_encode": "gbk",
    "delete_after_read": true
}
```

### 自定义分隔符配置
```json
{
    "file_name": "data_${now.delta(1).datekey}.txt",
    "file_path": "/data/daily",
    "target_sep": "|",
    "row_sep_from": "\\r\\n",
    "row_sep_to": "\\n",
    "has_header": "1",
    "table_cols": "id,name,age,gender",
    "conflict_sep": true,
    "check_interval": 300,
    "max_wait_time": 7200
}
```

## 注意事项

1. 文件命名：
   - 支持日期变量替换
   - 支持通配符匹配
   - 注意文件名唯一性

2. 文件读取：
   - 确保有足够的文件读取权限
   - 合理设置文件编码
   - 注意大文件的内存使用

3. 数据格式：
   - 确保分隔符设置正确
   - 处理特殊字符转义
   - 注意行尾符的兼容性

4. 性能优化：
   - 合理设置检查间隔
   - 及时清理已处理文件
   - 避免频繁的文件操作

5. 安全考虑：
   - 谨慎使用删除功能
   - 保护压缩文件密码
   - 控制文件访问权限 