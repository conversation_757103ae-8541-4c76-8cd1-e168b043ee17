# TalosCTAS 源组件配置说明

## 组件说明
TalosCTAS（Create Table As Select）源组件，用于从Talos消息队列中读取数据并创建Hive表。支持多种数据格式和分区策略。

## 配置参数

| 参数名 | 中文名称 | 类型 | 必填 | 默认值 | 示例值 | 说明 |
|-------|---------|------|------|--------|--------|------|
| topic | 主题名称 | string | 是 | - | test_topic | Talos主题名称 |
| consumer_group | 消费组 | string | 是 | - | test_group | 消费组名称 |
| database | 数据库名 | string | 是 | - | test_db | Hive数据库名称 |
| table | 表名 | string | 是 | - | test_table | Hive表名称 |
| partition_cols | 分区列 | string | 否 | - | dt,hour | 分区列名称，逗号分隔 |
| partition_values | 分区值 | string | 否 | - | ${now.delta(1).datekey},${now.hourkey} | 分区值，与分区列一一对应 |
| columns | 字段列表 | string | 是 | - | id:string,name:string,age:int | 字段名和类型列表，格式为name:type |
| start_time | 起始时间 | string | 否 | - | ${now.delta(1).datekey} 00:00:00 | 开始消费的时间点 |
| end_time | 结束时间 | string | 否 | - | ${now.datekey} 00:00:00 | 结束消费的时间点 |
| batch_size | 批量大小 | int | 否 | 1000 | 5000 | 每批次处理的消息数 |
| message_format | 消息格式 | string | 否 | json | json,avro | 消息的序列化格式 |
| schema_registry | Schema注册表 | string | 否 | - | http://schema-registry:8081 | Schema注册表地址（Avro格式需要） |
| table_format | 表格式 | string | 否 | orc | orc,parquet | Hive表存储格式 |
| compression | 压缩格式 | string | 否 | snappy | snappy,gzip,none | 数据压缩格式 |
| overwrite | 覆盖模式 | boolean | 否 | false | true | 是否覆盖已存在的分区数据 |

## 功能特性

1. **数据同步**
   - 支持实时数据同步
   - 支持历史数据回填
   - 支持增量同步
   - 支持全量覆盖

2. **表管理**
   - 自动创建表
   - 自动创建分区
   - 支持分区覆盖
   - 支持多种存储格式

3. **数据格式**
   - 支持JSON格式
   - 支持Avro格式
   - 支持Schema演化
   - 支持数据类型映射

## 使用示例

### 基本配置
```json
{
    "topic": "test_topic",
    "consumer_group": "test_group",
    "database": "test_db",
    "table": "test_table",
    "columns": "id:string,name:string,age:int,create_time:string",
    "partition_cols": "dt",
    "partition_values": "${now.delta(1).datekey}",
    "batch_size": 5000,
    "message_format": "json",
    "table_format": "orc"
}
```

### 多分区配置
```json
{
    "topic": "test_topic",
    "consumer_group": "test_group",
    "database": "test_db",
    "table": "test_table",
    "columns": "id:string,name:string,age:int,create_time:string",
    "partition_cols": "dt,hour",
    "partition_values": "${now.delta(1).datekey},${now.hourkey}",
    "start_time": "${now.delta(1).datekey} 00:00:00",
    "end_time": "${now.datekey} 00:00:00",
    "batch_size": 5000,
    "message_format": "json",
    "table_format": "orc",
    "compression": "snappy",
    "overwrite": true
}
```

### Avro格式配置
```json
{
    "topic": "test_topic",
    "consumer_group": "test_group",
    "database": "test_db",
    "table": "test_table",
    "columns": "id:string,name:string,age:int,create_time:string",
    "partition_cols": "dt",
    "partition_values": "${now.delta(1).datekey}",
    "message_format": "avro",
    "schema_registry": "http://schema-registry:8081",
    "table_format": "parquet",
    "compression": "snappy"
}
```

## 注意事项

1. 数据同步：
   - 确保消息格式与Schema定义匹配
   - 合理设置批量大小和消费超时
   - 注意处理数据类型转换

2. 分区管理：
   - 谨慎使用覆盖模式
   - 确保分区列和分区值一一对应
   - 注意分区值的动态替换

3. 性能优化：
   - 选择合适的存储格式
   - 使用合适的压缩算法
   - 合理设置批量大小

4. 监控建议：
   - 监控数据同步延迟
   - 监控数据质量
   - 监控存储空间使用
   - 监控任务执行状态

5. 容错处理：
   - 实现幂等性写入
   - 处理数据重复和丢失
   - 保证数据一致性 