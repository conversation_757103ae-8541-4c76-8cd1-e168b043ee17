# FateTask 源组件配置说明

## 组件说明
FateTask源组件，用于从FATE（Federated AI Technology Enabler）任务系统中读取数据。支持多种任务类型和数据格式。

## 配置参数

| 参数名 | 中文名称 | 类型 | 必填 | 默认值 | 示例值 | 说明 |
|-------|---------|------|------|--------|--------|------|
| task_id | 任务ID | string | 是 | - | task_20240101 | FATE任务的唯一标识符 |
| party_id | 参与方ID | string | 是 | - | 10000 | 参与方的唯一标识符 |
| role | 角色 | string | 是 | - | guest | 参与方在联邦学习中的角色(guest/host/arbiter) |
| component_name | 组件名称 | string | 是 | - | intersection | FATE流程中的组件名称 |
| output_key | 输出键名 | string | 否 | - | data | 组件输出数据的键名 |
| output_type | 输出类型 | string | 否 | data | data,model,metric | 要获取的输出类型 |
| data_type | 数据类型 | string | 否 | table | table,model | 数据的存储类型 |
| namespace | 命名空间 | string | 否 | - | experiment_001 | 数据表的命名空间 |
| name | 数据表名 | string | 否 | - | sample_data | 数据表的名称 |
| timeout | 超时时间 | int | 否 | 3600 | 7200 | 任务执行超时时间（秒） |
| retry_times | 重试次数 | int | 否 | 3 | 5 | 任务失败重试次数 |
| retry_interval | 重试间隔 | int | 否 | 60 | 120 | 重试间隔时间（秒） |
| check_interval | 检查间隔 | int | 否 | 10 | 30 | 任务状态检查间隔（秒） |

## 功能特性

1. **任务管理**
   - 支持多种任务类型
   - 支持任务状态查询
   - 支持任务超时控制
   - 支持失败重试

2. **数据访问**
   - 支持表格数据
   - 支持模型数据
   - 支持指标数据
   - 支持多种输出格式

3. **安全特性**
   - 支持多方安全计算
   - 支持数据加密传输
   - 支持访问权限控制
   - 支持数据隐私保护

## 使用示例

### 基本配置
```json
{
    "task_id": "task_20240101",
    "party_id": "10000",
    "role": "guest",
    "component_name": "intersection",
    "output_key": "data",
    "output_type": "data",
    "data_type": "table",
    "timeout": 3600
}
```

### 完整配置
```json
{
    "task_id": "task_20240101",
    "party_id": "10000",
    "role": "guest",
    "component_name": "intersection",
    "output_key": "data",
    "output_type": "data",
    "data_type": "table",
    "namespace": "experiment_001",
    "name": "sample_data",
    "timeout": 7200,
    "retry_times": 5,
    "retry_interval": 120,
    "check_interval": 30
}
```

### 模型输出配置
```json
{
    "task_id": "task_20240101",
    "party_id": "10000",
    "role": "guest",
    "component_name": "hetero_lr",
    "output_key": "model",
    "output_type": "model",
    "data_type": "model",
    "timeout": 3600,
    "retry_times": 3
}
```

## 注意事项

1. 任务配置：
   - 确保任务ID的唯一性
   - 正确设置参与方角色
   - 选择合适的组件名称
   - 指定正确的输出类型

2. 数据访问：
   - 确保有足够的访问权限
   - 注意数据表的命名规范
   - 合理设置数据类型

3. 性能优化：
   - 合理设置超时时间
   - 适当配置重试策略
   - 优化检查间隔

4. 安全考虑：
   - 保护任务相关的敏感信息
   - 遵守数据隐私保护规则
   - 控制数据访问范围

5. 错误处理：
   - 实现完善的重试机制
   - 记录详细的错误日志
   - 设置合理的告警阈值

6. 监控建议：
   - 监控任务执行状态
   - 监控数据处理进度
   - 监控系统资源使用
   - 监控任务执行时间 