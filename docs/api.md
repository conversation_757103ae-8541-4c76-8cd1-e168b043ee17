# Queqiao API 文档

## 目录
- [任务管理接口](#任务管理接口)
- [权限管理接口](#权限管理接口)
- [组件配置接口](#组件配置接口)
- [组件接口](#组件接口)
- [SSO认证接口](#SSO认证接口)
- [FTP连接接口](#FTP连接接口)
- [FTP历史文件接口](#FTP历史文件接口)
- [告警接口](#告警接口)
- [申请单接口](#申请单接口)
- [DSN接口](#DSN接口)
- [执行实例接口](#执行实例接口)
- [健康检查接口](#健康检查接口)
- [组织机构接口](#组织机构接口)
- [项目接口](#项目接口)
- [任务类型接口](#任务类型接口)
- [用户接口](#用户接口)
- [任务执行接口](#任务执行接口)

## 任务管理接口

### 获取任务列表
- **接口**: GET /api/v1/tasks
- **描述**: 获取当前用户可访问的所有任务列表
- **权限要求**: 登录用户
- **返回数据**: 任务列表
- **调用示例**:
```bash
curl -X GET "http://localhost:5000/api/v1/tasks" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

### 获取任务详情
- **接口**: GET /api/v1/task/{id}
- **描述**: 根据任务ID获取任务详细信息
- **权限要求**: 任务读取权限
- **返回数据**: 任务详情(包含组件配置和权限信息)
- **调用示例**:
```bash
curl -X GET "http://localhost:5000/api/v1/task/123" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

### 根据名称获取任务
- **接口**: GET /api/v1/task/{name}
- **描述**: 根据任务名称获取任务信息
- **权限要求**: 无
- **返回数据**: 任务详情(包含组件配置和权限信息)
- **调用示例**:
```bash
curl -X GET "http://localhost:5000/api/v1/task/my_task_name" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

### 创建任务
- **接口**: POST /api/v1/task
- **描述**: 创建新任务
- **权限要求**: 登录用户
- **请求参数**:
  - engine: 引擎类型
  - name: 任务名称
  - type: 任务类型
  - source_configs: 源配置
  - sink_configs: 目标配置
  - project: 项目信息
- **返回数据**: 创建的任务信息
- **调用示例**:
```bash
curl -X POST "http://localhost:5000/api/v1/task" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "engine": "spark",
    "name": "数据同步任务",
    "type": "data_sync",
    "source_configs": {
      "type": "mysql",
      "host": "localhost",
      "port": 3306,
      "database": "test_db",
      "table": "source_table"
    },
    "sink_configs": {
      "type": "hive",
      "database": "target_db",
      "table": "target_table"
    },
    "project": {
      "id": 1,
      "name": "数据平台项目"
    }
  }'
```

### 更新任务
- **接口**: PUT /api/v1/task/{id}
- **描述**: 更新任务信息
- **权限要求**: 任务编辑权限
- **请求参数**: 任务更新字段
- **返回数据**: 更新后的任务信息
- **调用示例**:
```bash
curl -X PUT "http://localhost:5000/api/v1/task/123" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "更新后的任务名称",
    "description": "更新后的任务描述"
  }'
```

### 执行任务
- **接口**: GET /api/v1/task/exec/{name}
- **描述**: 执行指定名称的任务
- **权限要求**: 任务执行权限
- **请求参数**:
  - params: 执行参数(可选)
  - etl_date: ETL日期(可选)
  - async: 是否异步执行(可选)
- **返回数据**: 执行ID
- **调用示例**:
```bash
curl -X GET "http://localhost:5000/api/v1/task/exec/my_task_name?params=custom_param&etl_date=20250120&async=true" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

### 任务上线
- **接口**: PUT /api/v1/task/{id}/online
- **描述**: 将任务状态设置为上线
- **权限要求**: 任务编辑权限
- **返回数据**: 操作结果
- **调用示例**:
```bash
curl -X PUT "http://localhost:5000/api/v1/task/123/online" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

### 任务下线
- **接口**: PUT /api/v1/task/{id}/offline
- **描述**: 将任务状态设置为下线
- **权限要求**: 任务编辑权限
- **返回数据**: 操作结果
- **调用示例**:
```bash
curl -X PUT "http://localhost:5000/api/v1/task/123/offline" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

## 权限管理接口

### 获取任务权限列表
- **接口**: GET /api/v1/task_permission/{task_id}
- **描述**: 获取指定任务的权限列表
- **权限要求**: 登录用户
- **返回数据**: 权限列表
- **调用示例**:
```bash
curl -X GET "http://localhost:5000/api/v1/task_permission/123" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

### 添加任务权限
- **接口**: POST /api/v1/task_permission
- **描述**: 为任务添加新的权限配置
- **权限要求**: 任务管理权限
- **请求参数**:
  - task_id: 任务ID
  - permission: 权限配置信息
- **返回数据**: 添加的权限信息
- **调用示例**:
```bash
curl -X POST "http://localhost:5000/api/v1/task_permission" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "task_id": 123,
    "permission": {
      "user_id": "user123",
      "permission_type": "read",
      "start_time": "2025-01-20T00:00:00Z",
      "end_time": "2025-12-31T23:59:59Z"
    }
  }'
```

### 修改任务权限
- **接口**: PUT /api/v1/task_permission/{id}
- **描述**: 修改已有的权限配置
- **权限要求**: 任务管理权限
- **请求参数**: 权限更新信息
- **返回数据**: 更新后的权限信息
- **调用示例**:
```bash
curl -X PUT "http://localhost:5000/api/v1/task_permission/456" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "permission_type": "write",
    "end_time": "2026-12-31T23:59:59Z"
  }'
```

### 删除任务权限
- **接口**: DELETE /api/v1/task_permission/{id}
- **描述**: 删除指定的权限配置
- **权限要求**: 任务管理权限
- **返回数据**: 操作结果
- **调用示例**:
```bash
curl -X DELETE "http://localhost:5000/api/v1/task_permission/456" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

## 组件接口

### 获取组件列表
- **接口**: GET /api/v1/component
- **描述**: 获取组件列表,支持按操作类型和数据源筛选
- **权限要求**: 登录用户
- **查询参数**:
  - operator: 操作类型(可选)
  - datasource: 数据源(可选)
- **返回数据**: 组件列表
- **调用示例**:
```bash
curl -X GET "http://localhost:5000/api/v1/component?operator=sync&datasource=mysql" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

### 添加组件
- **接口**: POST /api/v1/component
- **描述**: 添加新组件
- **权限要求**: 登录用户
- **请求参数**:
  - operator: 操作类型
  - datasource: 数据源
  - name: 组件名称
  - description: 组件描述
- **返回数据**: 创建的组件信息
- **调用示例**:
```bash
curl -X POST "http://localhost:5000/api/v1/component" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "operator": "sync",
    "datasource": "mysql",
    "name": "MySQL同步组件",
    "description": "用于MySQL数据同步的组件"
  }'
```

### 更新组件
- **接口**: PUT /api/v1/component/{id}
- **描述**: 更新指定ID的组件信息
- **权限要求**: 登录用户
- **URL参数**:
  - id: 组件ID
- **请求参数**: 需要更新的字段
- **返回数据**: 更新后的组件信息
- **调用示例**:
```bash
curl -X PUT "http://localhost:5000/api/v1/component/123" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "更新后的MySQL同步组件",
    "description": "更新后的组件描述"
  }'
```

### 匹配源组件
- **接口**: GET /api/v1/component/match/sources
- **描述**: 根据目标组件ID匹配可用的源组件
- **请求参数**:
  - sink_id: 目标组件ID
- **返回数据**: 匹配的源组件列表
- **调用示例**:
```bash
curl -X GET "http://localhost:5000/api/v1/component/match/sources?sink_id=123" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

### 匹配目标组件
- **接口**: GET /api/v1/component/match/sinks
- **描述**: 根据源组件ID匹配可用的目标组件
- **请求参数**:
  - source_id: 源组件ID
- **返回数据**: 匹配的目标组件列表
- **调用示例**:
```bash
curl -X GET "http://localhost:5000/api/v1/component/match/sinks?source_id=123" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

## 组件配置接口

### 获取组件配置
- **接口**: GET /api/v1/component_config/{cid}
- **描述**: 获取指定组件ID的配置列表
- **权限要求**: 登录用户
- **URL参数**:
  - cid: 组件ID
- **返回数据**: 组件配置列表
- **调用示例**:
```bash
curl -X GET "http://localhost:5000/api/v1/component_config/123" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

### 添加组件配置
- **接口**: POST /api/v1/component_config
- **描述**: 添加新的组件配置
- **权限要求**: 登录用户
- **请求参数**:
  - name: 配置名称
  - description: 配置描述
  - config: 配置信息
- **返回数据**: 创建的配置信息
- **调用示例**:
```bash
curl -X POST "http://localhost:5000/api/v1/component_config" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "MySQL配置",
    "description": "测试环境MySQL配置",
    "config": {
      "host": "localhost",
      "port": 3306,
      "username": "test_user",
      "password": "test_password",
      "database": "test_db"
    }
  }'
```

### 更新组件配置
- **接口**: PUT /api/v1/component_config/{id}
- **描述**: 更新指定ID的组件配置
- **权限要求**: 登录用户
- **URL参数**:
  - id: 配置ID
- **请求参数**: 需要更新的配置字段
- **返回数据**: 更新后的配置信息
- **调用示例**:
```bash
curl -X PUT "http://localhost:5000/api/v1/component_config/123" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "更新后的MySQL配置",
    "description": "更新后的配置描述",
    "config": {
      "host": "new-host",
      "port": 3307
    }
  }'
```

### 删除组件配置
- **接口**: DELETE /api/v1/component_config/{id}
- **描述**: 删除指定ID的组件配置
- **权限要求**: 登录用户
- **URL参数**:
  - id: 配置ID
- **返回数据**: 删除结果
- **调用示例**:
```bash
curl -X DELETE "http://localhost:5000/api/v1/component_config/123" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

## SSO认证接口

### 登录
- **接口**: GET /api/v1/sso/login
- **描述**: SSO登录接口
- **权限要求**: 无
- **返回数据**: 登录结果或重定向到SSO登录页
- **调用示例**:
```bash
curl -X GET "http://localhost:5000/api/v1/sso/login" \
  -H "Accept: application/json"
```

### 登出
- **接口**: GET /api/v1/sso/logout
- **描述**: 退出登录
- **权限要求**: 登录用户
- **返回数据**: 登出结果
- **调用示例**:
```bash
curl -X GET "http://localhost:5000/api/v1/sso/logout" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

### 登录回调
- **接口**: GET /api/v1/sso/callback
- **描述**: SSO登录回调接口
- **权限要求**: 无
- **查询参数**:
  - code: 授权码
  - state: 状态值
- **返回数据**: 登录结果
- **调用示例**:
```bash
curl -X GET "http://localhost:5000/api/v1/sso/callback?code=auth_code_123&state=random_state" \
  -H "Accept: application/json"
```

## FTP连接接口

### 测试DSN名称的FTP连接
- **接口**: GET /api/v1/ftp/test/{name}
- **描述**: 测试指定DSN名称的FTP连接是否可用
- **权限要求**: 登录用户
- **URL参数**:
  - name: DSN名称(string)
- **返回数据**: 
  ```json
  {
    "data": {
      "connected": true/false,  // 连接是否成功
      "error": "错误信息"      // 如果连接失败,返回错误原因
    }
  }
  ```
- **调用示例**:
```bash
curl -X GET "http://localhost:5000/api/v1/ftp/test/my_dsn_name" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

### 测试FTP直接连接
- **接口**: POST /api/v1/ftp/test
- **描述**: 使用直接提供的连接信息测试FTP连接
- **权限要求**: 登录用户
- **请求参数**:
  ```json
  {
    "protocol": "ftp/sftp",    // 协议类型
    "ip": "127.0.0.1",        // FTP服务器IP
    "port": 21,               // 端口号
    "username": "user",       // 用户名
    "password": "pass"        // 密码
  }
  ```
- **返回数据**: 
  ```json
  {
    "data": {
      "connected": true/false,  // 连接是否成功
      "error": "错误信息"      // 如果连接失败,返回错误原因
    }
  }
  ```
- **调用示例**:
```bash
curl -X POST "http://localhost:5000/api/v1/ftp/test" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "protocol": "sftp",
    "ip": "127.0.0.1",
    "port": 22,
    "username": "test_user",
    "password": "test_password"
  }'
```

### 通过DSN ID获取FTP目录列表
- **接口**: GET /api/v1/ftp/list/id/{id}
- **描述**: 获取指定DSN ID的FTP服务器上的目录内容
- **权限要求**: 登录用户
- **URL参数**:
  - id: DSN ID(integer)
- **查询参数**:
  - ftp_path: FTP服务器上的目录路径(string)
- **返回数据**: 
  ```json
  {
    "data": {
      "files": [
        {
          "name": "example.txt",  // 文件名
          "type": "file"         // 类型(file或dir)
        }
      ]
    }
  }
  ```
- **调用示例**:
```bash
curl -X GET "http://localhost:5000/api/v1/ftp/list/id/123?ftp_path=/path/to/dir" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

### 通过DSN名称获取FTP目录列表
- **接口**: GET /api/v1/ftp/list/{name}
- **描述**: 获取指定DSN名称的FTP服务器上的目录内容
- **权限要求**: 登录用户
- **URL参数**:
  - name: DSN名称(string)
- **查询参数**:
  - ftp_path: FTP服务器上的目录路径(string)
- **返回数据**: 
  ```json
  {
    "data": {
      "files": [
        {
          "name": "example.txt",  // 文件名
          "type": "file"         // 类型(file或dir)
        }
      ]
    }
  }
  ```
- **调用示例**:
```bash
curl -X GET "http://localhost:5000/api/v1/ftp/list/my_dsn_name?ftp_path=/path/to/dir" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

### 直接获取FTP目录列表
- **接口**: GET /api/v1/ftp/list/direct
- **描述**: 使用直接提供的连接信息获取FTP服务器上的目录内容
- **权限要求**: 登录用户
- **查询参数**:
  - protocol: 协议类型(ftp/sftp)
  - ip: FTP服务器IP
  - port: 端口号
  - username: 用户名
  - password: 密码
  - ftp_path: FTP服务器上的目录路径
- **返回数据**: 
  ```json
  {
    "data": {
      "files": [
        {
          "name": "example.txt",  // 文件名
          "type": "file"         // 类型(file或dir)
        }
      ]
    }
  }
  ```
- **调用示例**:
```bash
curl -X GET "http://localhost:5000/api/v1/ftp/list/direct?protocol=sftp&ip=127.0.0.1&port=22&username=test_user&password=test_password&ftp_path=/path/to/dir" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

## FTP历史文件接口

### 获取单个FTP历史文件记录
- **接口**: GET /api/v1/history/file/{file_id}
- **描述**: 获取单个FTP历史文件记录的详细信息
- **权限要求**: 登录用户
- **参数说明**:
  - file_id: 文件ID (路径参数)
- **返回数据**: 
  ```json
  {
    "data": {
      "id": 123,
      "filename": "example.csv",
      "file_size": 1024,
      "file_path": "/path/to/file",
      "status": "success",
      "created_at": "2025-01-20T10:00:00Z",
      "updated_at": "2025-01-20T10:05:00Z",
      "execution_id": "exec_123",
      "task_id": 456,
      "error_message": null
    }
  }
  ```
- **调用示例**:
```bash
curl -X GET "http://localhost:5000/history/api/v1/file/123" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

### 获取FTP历史文件列表
- **接口**: GET /api/v1/history/file
- **描述**: 获取FTP历史文件记录列表，支持分页和文件名搜索
- **权限要求**: 登录用户
- **查询参数**:
  - page: 页码 (默认: 1)
  - per_page: 每页记录数 (默认: 10)
  - filename: 文件名搜索关键字 (可选)
- **返回数据**: 
  ```json
  {
    "data": {
      "items": [
        {
          "id": 123,
          "filename": "example1.csv",
          "file_size": 1024,
          "file_path": "/path/to/file1",
          "status": "success",
          "created_at": "2025-01-20T10:00:00Z",
          "updated_at": "2025-01-20T10:05:00Z",
          "execution_id": "exec_123",
          "task_id": 456,
          "error_message": null
        },
        {
          "id": 124,
          "filename": "example2.csv",
          "file_size": 2048,
          "file_path": "/path/to/file2",
          "status": "failed",
          "created_at": "2025-01-20T11:00:00Z",
          "updated_at": "2025-01-20T11:01:00Z",
          "execution_id": "exec_124",
          "task_id": 456,
          "error_message": "文件传输失败"
        }
      ],
      "total": 100,
      "page": 1,
      "per_page": 10
    }
  }
  ```
- **调用示例**:
```bash
curl -X GET "http://localhost:5000/api/v1/history/file?page=1&per_page=10&filename=test" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

### 获取指定执行ID的历史文件列表
- **接口**: GET /api/v1/history/file/execution/{execution_id}
- **描述**: 获取与指定execution_id关联的所有FTP历史文件记录
- **权限要求**: 登录用户
- **参数说明**:
  - execution_id: 执行ID (路径参数)
- **返回数据**: 
  ```json
  {
    "data": [
        {
          "id": 123,
          "filename": "example1.csv",
          "file_size": 1024,
          "file_path": "/path/to/file1",
          "status": "success",
          "created_at": "2025-01-20T10:00:00Z",
          "updated_at": "2025-01-20T10:05:00Z",
          "execution_id": "exec_123",
          "task_id": 456,
          "error_message": null
        }
      ]
  }
  ```
- **调用示例**:
```bash
curl -X GET "http://localhost:5000/api/v1/history/file/execution/456" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

## 告警接口

### 设置告警静默
- **接口**: POST /api/v1/alarm/keep_quiet/{id}
- **描述**: 设置指定告警ID的静默时间
- **权限要求**: 登录用户
- **请求参数**:
  - keep_quiet_n_mins: 静默时间(分钟)
- **返回数据**: 操作结果
- **调用示例**:
```bash
curl -X POST "http://localhost:5000/api/v1/alarm/keep_quiet/123" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "keep_quiet_n_mins": 60
  }'
```

## 申请单接口

### 创建申请单
- **接口**: POST /api/v1/apply
- **描述**: 创建新的申请单及相关任务
- **权限要求**: 登录用户
- **请求参数**:
  - information: 申请说明
  - tasks: 任务列表
- **返回数据**: 创建的申请单信息
- **调用示例**:
```bash
curl -X POST "http://localhost:5000/api/v1/apply" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "information": "数据同步申请",
    "tasks": [
      {
        "name": "每日数据同步",
        "trans_type": "mysql_to_hive",
        "engine_id": "spark",
        "alarm_receivers": "<EMAIL>,<EMAIL>",
        "params": {
          "batch_size": 1000,
          "retry_times": 3
        },
        "exec_immediately": true,
        "source": {
          "cid": "mysql_source_1",
          "database": "prod_db",
          "table": "user_data"
        },
        "sink": {
          "cid": "hive_sink_1",
          "database": "dw",
          "table": "user_data_daily"
        }
      }
    ]
  }'
```

### 获取申请单进度
- **接口**: GET /api/v1/apply/progress/{id}
- **描述**: 获取指定申请单的处理进度
- **权限要求**: 登录用户
- **URL参数**:
  - id: 申请单ID
- **返回数据**: 申请单进度信息
- **调用示例**:
```bash
curl -X GET "http://localhost:5000/api/v1/apply/progress/123" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

### 推送申请单
- **接口**: POST /api/v1/apply/push/{id}
- **描述**: 推送指定申请单进入审批流程
- **权限要求**: 登录用户
- **URL参数**:
  - id: 申请单ID
- **返回数据**: 推送结果
- **调用示例**:
```bash
curl -X POST "http://localhost:5000/api/v1/apply/push/123" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

### 获取申请单详情
- **接口**: GET /api/v1/apply/{id}
- **描述**: 获取指定申请单的详细信息
- **权限要求**: 登录用户
- **URL参数**:
  - id: 申请单ID
- **返回数据**: 申请单详细信息
- **调用示例**:
```bash
curl -X GET "http://localhost:5000/api/v1/apply/123" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

### 同意申请单
- **接口**: POST /api/v1/apply/agree/{id}
- **描述**: 同意指定申请单
- **权限要求**: 审批人
- **URL参数**:
  - id: 申请单ID
- **返回数据**: 操作结果
- **调用示例**:
```bash
curl -X POST "http://localhost:5000/api/v1/apply/agree/123" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

### 拒绝申请单
- **接口**: POST /api/v1/apply/reject/{id}
- **描述**: 拒绝指定申请单
- **权限要求**: 审批人
- **URL参数**:
  - id: 申请单ID
- **请求参数**:
  - comment: 拒绝原因(可选)
- **返回数据**: 操作结果
- **调用示例**:
```bash
curl -X POST "http://localhost:5000/api/v1/apply/reject/123" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "comment": "申请信息不完整，请补充数据同步的频率说明"
  }'
```

## DSN接口

### 获取所有DSN
- **接口**: GET /api/v1/dsn
- **描述**: 获取所有DSN配置
- **权限要求**: 登录用户
- **返回数据**: DSN配置列表
- **调用示例**:
```bash
curl -X GET "http://localhost:5000/api/v1/dsn" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

### 获取DSN详情(通过ID)
- **接口**: GET /api/v1/dsn/{id}
- **描述**: 获取指定ID的DSN配置详情
- **权限要求**: 登录用户
- **URL参数**:
  - id: DSN ID
- **返回数据**: DSN配置详情
- **调用示例**:
```bash
curl -X GET "http://localhost:5000/api/v1/dsn/123" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

### 获取DSN详情(通过名称)
- **接口**: GET /api/v1/dsn/{name}
- **描述**: 获取指定名称的DSN配置详情
- **权限要求**: 无
- **URL参数**:
  - name: DSN名称
- **返回数据**: DSN配置详情
- **调用示例**:
```bash
curl -X GET "http://localhost:5000/api/v1/dsn/prod_ftp_server"
```

### 添加DSN
- **接口**: POST /api/v1/dsn
- **描述**: 添加新的DSN配置
- **权限要求**: 登录用户
- **请求参数**:
  - name: DSN名称(必填，字符串，最大长度50，需唯一)
  - dsn_type: 数据源类型(必填，字符串，最大长度50)
  - connect: 连接参数(JSON格式，包含连接所需的所有参数)
  - org_id: 所属机构ID(必填，整数)
  - comment: 备注说明(可选，字符串，最大长度256)
- **返回数据**: 创建的DSN信息
- **调用示例**:
```bash
curl -X POST "http://localhost:5000/api/v1/dsn" -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "prod_ftp_server",
    "dsn_type": "ftp",
    "connect": {
      "protocol": "sftp",
      "host": "*************",
      "port": 22,
      "username": "prod_user",
      "password": "secure_password"
    },
    "org_id": 1,
    "comment": "生产环境FTP服务器"
  }'
```

### 更新DSN
- **接口**: PUT /api/v1/dsn/{id}
- **描述**: 更新指定DSN的配置信息
- **权限要求**: 登录用户
- **URL参数**:
  - id: DSN ID
- **请求参数**:
  - name: DSN名称(可选，字符串，最大长度50，需唯一)
  - dsn_type: 数据源类型(可选，字符串，最大长度50)
  - connect: 连接参数(可选，JSON格式，包含连接所需的所有参数)
  - org_id: 所属机构ID(可选，整数)
  - comment: 备注说明(可选，字符串，最大长度256)
- **返回数据**: 更新后的DSN信息
- **调用示例**:
```bash
curl -X PUT "http://localhost:5000/api/v1/dsn/123" -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "prod_ftp_server",
    "dsn_type": "sftp",
    "connect": {
      "protocol": "sftp",
      "host": "*************",
      "port": 22,
      "username": "prod_user",
      "password": "new_secure_password"
    },
    "org_id": 1,
    "comment": "更新后的生产环境FTP服务器"
  }'
```

## 执行实例接口

### 获取实例列表
- **接口**: GET /api/v1/instance
- **描述**: 获取执行实例列表
- **权限要求**: 登录用户
- **查询参数**:
  - task_id: 任务ID(可选)
  - status: 实例状态(可选)
  - page: 页码(可选，默认1)
  - size: 每页数量(可选，默认20)
- **返回数据**: 实例列表及分页信息
- **调用示例**:
```bash
curl -X GET "http://localhost:5000/api/v1/instance?task_id=123&status=running&page=1&size=20" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

### 获取实例详情
- **接口**: GET /api/v1/instance/{id}
- **描述**: 获取指定实例的详细信息
- **权限要求**: 登录用户
- **URL参数**:
  - id: 实例ID
- **返回数据**: 实例详细信息
- **调用示例**:
```bash
curl -X GET "http://localhost:5000/api/v1/instance/123" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

### 获取实例日志
- **接口**: GET /api/v1/instance/{id}/log
- **描述**: 获取指定实例的执行日志
- **权限要求**: 登录用户
- **URL参数**:
  - id: 实例ID
- **查询参数**:
  - offset: 日志偏移量(可选)
  - limit: 返回日志行数(可选)
- **返回数据**: 实例日志内容
- **调用示例**:
```bash
curl -X GET "http://localhost:5000/api/v1/instance/123/log?offset=0&limit=100" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

### 停止实例
- **接口**: POST /api/v1/instance/{id}/stop
- **描述**: 停止指定的执行实例
- **权限要求**: 登录用户
- **URL参数**:
  - id: 实例ID
- **返回数据**: 操作结果
- **调用示例**:
```bash
curl -X POST "http://localhost:5000/api/v1/instance/123/stop" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

### 重试实例
- **接口**: POST /api/v1/instance/{id}/retry
- **描述**: 重试执行失败的实例
- **权限要求**: 登录用户
- **URL参数**:
  - id: 实例ID
- **返回数据**: 操作结果
- **调用示例**:
```bash
curl -X POST "http://localhost:5000/api/v1/instance/123/retry" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

### 跳过实例
- **接口**: POST /api/v1/instance/{id}/skip
- **描述**: 跳过执行失败的实例
- **权限要求**: 登录用户
- **URL参数**:
  - id: 实例ID
- **返回数据**: 操作结果
- **调用示例**:
```bash
curl -X POST "http://localhost:5000/api/v1/instance/123/skip" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

## 健康检查接口

### 服务健康检查
- **接口**: GET /api/v1/health
- **描述**: 检查服务是否正常运行
- **权限要求**: 无
- **返回数据**: 服务状态信息
- **调用示例**:
```bash
curl -X GET "http://localhost:5000/api/v1/health"
```

### 数据库健康检查
- **接口**: GET /api/v1/health/db
- **描述**: 检查数据库连接状态
- **权限要求**: 无
- **返回数据**: 数据库连接状态
- **调用示例**:
```bash
curl -X GET "http://localhost:5000/api/v1/health/db"
```

### 缓存健康检查
- **接口**: GET /api/v1/health/cache
- **描述**: 检查缓存服务状态
- **权限要求**: 无
- **返回数据**: 缓存服务状态
- **调用示例**:
```bash
curl -X GET "http://localhost:5000/api/v1/health/cache"
```

### 依赖服务健康检查
- **接口**: GET /api/v1/health/dependencies
- **描述**: 检查所有依赖服务的状态
- **权限要求**: 无
- **返回数据**: 依赖服务状态列表
- **调用示例**:
```bash
curl -X GET "http://localhost:5000/api/v1/health/dependencies"
```

### 健康状态检查
- **接口**: GET /api/v1/check
- **描述**: 检查服务是否正常运行
- **权限要求**: 无
- **返回数据**: 服务状态信息
- **调用示例**:
```bash
curl -X GET "http://localhost:5000/api/v1/check"
```

### 发送告警消息
- **接口**: POST /api/v1/alert
- **描述**: 发送告警消息给指定接收者
- **权限要求**: 无
- **请求参数**:
  - msg: 告警消息内容
  - receivers: 接收者列表
- **返回数据**: 发送结果
- **调用示例**:
```bash
curl -X POST "http://localhost:5000/api/v1/alert" \
  -H "Content-Type: application/json" \
  -d '{
    "msg": "服务异常告警",
    "receivers": ["user1", "user2"]
  }'
```

### 查询Talos表结构
- **接口**: POST /api/v1/talos/table/schema
- **描述**: 获取Talos表的结构信息
- **权限要求**: 无
- **请求参数**:
  - name: 表名
- **返回数据**: 表结构信息
- **调用示例**:
```bash
curl -X POST "http://localhost:5000/api/v1/talos/table/schema" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "table_name"
  }'
```

### 查看API文档
- **接口**: GET /api/v1/doc
- **描述**: 在浏览器中查看API文档
- **权限要求**: 无
- **返回数据**: HTML格式的API文档
- **调用示例**:
```bash
curl -X GET "http://localhost:5000/api/v1/doc"
```

## 组织机构接口

### 根据id获取组织信息
- **接口**: GET /api/v1/org/{id}
- **描述**: 获取指定组织的详细信息
- **权限要求**: 登录用户
- **URL参数**:
  - id: 组织ID
- **返回数据**: 组织详细信息，包含以下字段：
```json
  {
    "id": 123,
    "name": "数据平台部",
    "params": "{}",
    "ad_users": "user1,user2",
    "comment": "备注说明",
    "create_user": "creator",
    "update_user": "updater",
    "create_time": "2023-01-01T00:00:00Z",
    "update_time": "2023-01-02T00:00:00Z"
  }
```
- **调用示例**:
```bash
curl -X GET "http://localhost:5000/api/v1/org/123" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

### 根据name获取组织信息
- **接口**: GET /api/v1/org/{name}
- **描述**: 获取指定组织的详细信息
- **权限要求**: 登录用户
- **URL参数**:
  - name: 组织名称
- **返回数据**: 组织详细信息，包含以下字段：
```json
  {
    "id": 123,
    "name": "数据平台部",
    "params": "{}",
    "ad_users": "user1,user2",
    "comment": "备注说明",
    "create_user": "creator",
    "update_user": "updater",
    "create_time": "2023-01-01T00:00:00Z",
    "update_time": "2023-01-02T00:00:00Z"
  }
```
- **调用示例**:
```bash
curl -X GET "http://localhost:5000/api/v1/org/icbc" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

### 创建组织
- **接口**: POST /api/v1/org
- **描述**: 创建新的组织机构
- **权限要求**: 管理员
- **请求参数**:
  - name: 组织名称(必填，字符串，最大长度50，需唯一)
  - params: 机构参数(可选，JSON格式)
  - ad_users: 机构负责人列表(可选，字符串，最大长度256)
  - comment: 备注说明(可选，字符串，最大长度256)
- **返回数据**: 创建的组织信息
- **调用示例**:
```bash
curl -X POST "http://localhost:5000/api/v1/org" -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "数据平台部",
    "params": {
      "department_code": "DP001",
      "level": 1
    },
    "ad_users": "zhangsan,lisi",
    "comment": "负责公司数据平台建设和运维"
  }'
```

### 更新组织
- **接口**: POST /api/v1/org/{id}
- **描述**: 更新指定组织的信息
- **权限要求**: 管理员
- **URL参数**:
  - id: 组织ID
- **请求参数**:
  - name: 组织名称(必填，字符串，最大长度50，需唯一)
  - params: 机构参数(可选，JSON格式)
  - ad_users: 机构负责人列表(可选，字符串，最大长度256)
  - comment: 备注说明(可选，字符串，最大长度256)
- **返回数据**: 更新后的组织信息
- **调用示例**:
```bash
curl -X PUT "http://localhost:5000/api/v1/org/123" -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "数据平台研发部",
    "params": {
      "department_code": "DP002",
      "level": 2
    },
    "ad_users": "zhangsan,wangwu",
    "comment": "负责数据平台核心系统研发"
  }'
```

## 项目接口

### 获取项目列表
- **接口**: GET /api/v1/project
- **描述**: 获取项目列表
- **权限要求**: 登录用户
- **查询参数**:
  - page: 页码(可选，默认1)
  - size: 每页数量(可选，默认20)
  - org_id: 组织ID(可选)
- **返回数据**: 项目列表及分页信息
- **调用示例**:
```bash
curl -X GET "http://localhost:5000/api/v1/project?page=1&size=20&org_id=123" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

### 获取项目详情
- **接口**: GET /api/v1/project/{id}
- **描述**: 获取指定项目的详细信息
- **权限要求**: 登录用户
- **URL参数**:
  - id: 项目ID
- **返回数据**: 项目详细信息
- **调用示例**:
```bash
curl -X GET "http://localhost:5000/api/v1/project/123" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

### 创建项目
- **接口**: POST /api/v1/project
- **描述**: 创建新项目
- **权限要求**: 管理员
- **请求参数**:
  - name: 项目名称
  - org_id: 所属组织ID
  - description: 项目描述(可选)
  - owner_id: 项目负责人ID
  - members: 项目成员列表(可选)
- **返回数据**: 创建的项目信息
- **调用示例**:
```bash
curl -X POST "http://localhost:5000/api/v1/project" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "数据同步平台",
    "org_id": 123,
    "description": "用于管理和监控数据同步任务的平台",
    "owner_id": 1001,
    "members": [
      {
        "user_id": 1002,
        "role": "developer"
      },
      {
        "user_id": 1003,
        "role": "viewer"
      }
    ]
  }'
```

### 更新项目
- **接口**: PUT /api/v1/project/{id}
- **描述**: 更新项目信息
- **权限要求**: 项目管理员
- **URL参数**:
  - id: 项目ID
- **请求参数**:
  - name: 项目名称(可选)
  - description: 项目描述(可选)
  - owner_id: 项目负责人ID(可选)
- **返回数据**: 更新后的项目信息
- **调用示例**:
```bash
curl -X PUT "http://localhost:5000/api/v1/project/123" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "新一代数据同步平台",
    "description": "升级后的数据同步管理平台",
    "owner_id": 1004
  }'
```

### 删除项目
- **接口**: DELETE /api/v1/project/{id}
- **描述**: 删除指定项目
- **权限要求**: 项目管理员
- **URL参数**:
  - id: 项目ID
- **返回数据**: 删除结果
- **调用示例**:
```bash
curl -X DELETE "http://localhost:5000/api/v1/project/123" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

### 获取项目成员
- **接口**: GET /api/v1/project/{id}/members
- **描述**: 获取指定项目的所有成员
- **权限要求**: 项目成员
- **URL参数**:
  - id: 项目ID
- **返回数据**: 成员列表
- **调用示例**:
```bash
curl -X GET "http://localhost:5000/api/v1/project/123/members" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

### 添加项目成员
- **接口**: POST /api/v1/project/{id}/members
- **描述**: 向指定项目添加成员
- **权限要求**: 项目管理员
- **URL参数**:
  - id: 项目ID
- **请求参数**:
  - members: 成员列表
- **返回数据**: 操作结果
- **调用示例**:
```bash
curl -X POST "http://localhost:5000/api/v1/project/123/members" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "members": [
      {
        "user_id": 1005,
        "role": "developer"
      },
      {
        "user_id": 1006,
        "role": "viewer"
      }
    ]
  }'
```

### 更新项目成员
- **接口**: PUT /api/v1/project/{id}/members/{user_id}
- **描述**: 更新项目成员的角色
- **权限要求**: 项目管理员
- **URL参数**:
  - id: 项目ID
  - user_id: 用户ID
- **请求参数**:
  - role: 新的角色
- **返回数据**: 操作结果
- **调用示例**:
```bash
curl -X PUT "http://localhost:5000/api/v1/project/123/members/1005" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "role": "admin"
  }'
```

### 移除项目成员
- **接口**: DELETE /api/v1/project/{id}/members/{user_id}
- **描述**: 从指定项目移除成员
- **权限要求**: 项目管理员
- **URL参数**:
  - id: 项目ID
  - user_id: 用户ID
- **返回数据**: 操作结果
- **调用示例**:
```bash
curl -X DELETE "http://localhost:5000/api/v1/project/123/members/1005" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

## 任务类型接口

### 获取任务类型列表
- **接口**: GET /api/v1/task_type
- **描述**: 获取所有任务类型列表
- **权限要求**: 登录用户
- **返回数据**: 任务类型列表
- **调用示例**:
```bash
curl -X GET "http://localhost:5000/api/v1/task_type" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

### 获取任务类型详情
- **接口**: GET /api/v1/task_type/{id}
- **描述**: 获取指定任务类型的详细信息
- **权限要求**: 登录用户
- **URL参数**:
  - id: 任务类型ID
- **返回数据**: 任务类型详情
- **调用示例**:
```bash
curl -X GET "http://localhost:5000/api/v1/task_type/123" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

### 添加任务类型
- **接口**: POST /api/v1/task_type
- **描述**: 创建新的任务类型
- **权限要求**: 管理员
- **请求参数**:
  - name: 任务类型名称
  - description: 类型描述
  - params: 参数配置
- **返回数据**: 创建的任务类型信息
- **调用示例**:
```bash
curl -X POST "http://localhost:5000/api/v1/task_type" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "数据同步",
    "description": "用于数据同步的任务类型",
    "params": {
      "source": {
        "type": "string",
        "required": true,
        "description": "数据源"
      },
      "target": {
        "type": "string",
        "required": true,
        "description": "目标位置"
      }
    }
  }'
```

### 更新任务类型
- **接口**: PUT /api/v1/task_type/{id}
- **描述**: 更新任务类型信息
- **权限要求**: 管理员
- **URL参数**:
  - id: 任务类型ID
- **请求参数**: 需要更新的字段
- **返回数据**: 更新后的任务类型信息
- **调用示例**:
```bash
curl -X PUT "http://localhost:5000/api/v1/task_type/123" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "更新后的数据同步",
    "description": "更新后的任务类型描述"
  }'
```

### 删除任务类型
- **接口**: DELETE /api/v1/task_type/{id}
- **描述**: 删除指定的任务类型
- **权限要求**: 管理员
- **URL参数**:
  - id: 任务类型ID
- **返回数据**: 删除结果
- **调用示例**:
```bash
curl -X DELETE "http://localhost:5000/api/v1/task_type/123" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

## 用户接口

### 获取用户信息
- **接口**: GET /api/v1/user/info
- **描述**: 获取当前登录用户的信息
- **权限要求**: 登录用户
- **返回数据**: 用户详细信息
- **调用示例**:
```bash
curl -X GET "http://localhost:5000/api/v1/user/info" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

### 获取用户列表
- **接口**: GET /api/v1/user/list
- **描述**: 获取用户列表
- **权限要求**: 管理员
- **查询参数**:
  - page: 页码(可选，默认1)
  - size: 每页数量(可选，默认20)
  - org_id: 组织ID(可选)
- **返回数据**: 用户列表及分页信息
- **调用示例**:
```bash
curl -X GET "http://localhost:5000/api/v1/user/list?page=1&size=20&org_id=123" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

### 获取用户详情
- **接口**: GET /api/v1/user/{id}
- **描述**: 获取指定用户的详细信息
- **权限要求**: 管理员
- **URL参数**:
  - id: 用户ID
- **返回数据**: 用户详细信息
- **调用示例**:
```bash
curl -X GET "http://localhost:5000/api/v1/user/123" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

### 更新用户信息
- **接口**: PUT /api/v1/user/{id}
- **描述**: 更新指定用户的信息
- **权限要求**: 管理员或本人
- **URL参数**:
  - id: 用户ID
- **请求参数**:
  - name: 用户名称(可选)
  - email: 邮箱地址(可选)
  - phone: 电话号码(可选)
  - role: 角色(可选，仅管理员可修改)
- **返回数据**: 更新后的用户信息
- **调用示例**:
```bash
curl -X PUT "http://localhost:5000/api/v1/user/123" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "张三",
    "email": "<EMAIL>",
    "phone": "13800138000",
    "role": "developer"
  }'
```

### 删除用户
- **接口**: DELETE /api/v1/user/{id}
- **描述**: 删除指定用户
- **权限要求**: 管理员
- **URL参数**:
  - id: 用户ID
- **返回数据**: 删除结果
- **调用示例**:
```bash
curl -X DELETE "http://localhost:5000/api/v1/user/123" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

### 修改密码
- **接口**: POST /api/v1/user/password
- **描述**: 修改当前用户的密码
- **权限要求**: 登录用户
- **请求参数**:
  - old_password: 原密码
  - new_password: 新密码
- **返回数据**: 操作结果
- **调用示例**:
```bash
curl -X POST "http://localhost:5000/api/v1/user/password" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "old_password": "old_password_123",
    "new_password": "new_password_456"
  }'
```

### 重置密码
- **接口**: POST /api/v1/user/{id}/reset_password
- **描述**: 重置指定用户的密码
- **权限要求**: 管理员
- **URL参数**:
  - id: 用户ID
- **请求参数**:
  - new_password: 新密码
- **返回数据**: 操作结果
- **调用示例**:
```bash
curl -X POST "http://localhost:5000/api/v1/user/123/reset_password" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "new_password": "reset_password_789"
  }'
```

## 任务执行接口

### 获取执行实例列表
- **接口**: GET /api/v1/execution
- **描述**: 获取任务执行实例列表
- **权限要求**: 登录用户
- **查询参数**:
  - page: 页码(可选，默认1)
  - size: 每页数量(可选，默认20)
  - task_id: 任务ID(可选)
  - status: 执行状态(可选)
  - start_time: 开始时间(可选)
  - end_time: 结束时间(可选)
- **返回数据**: 执行实例列表及分页信息
- **调用示例**:
```bash
curl -X GET "http://localhost:5000/api/v1/execution?page=1&size=20&task_id=123&status=running" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

### 获取执行实例详情
- **接口**: GET /api/v1/execution/{id}
- **描述**: 获取指定执行实例的详细信息
- **权限要求**: 登录用户
- **URL参数**:
  - id: 执行实例ID
- **返回数据**: 执行实例详细信息
- **调用示例**:
```bash
curl -X GET "http://localhost:5000/api/v1/execution/123" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

### 获取执行实例日志
- **接口**: GET /api/v1/execution/{id}/logs
- **描述**: 获取指定执行实例的运行日志
- **权限要求**: 登录用户
- **URL参数**:
  - id: 执行实例ID
- **查询参数**:
  - offset: 日志偏移量(可选，默认0)
  - limit: 返回日志行数(可选，默认1000)
- **返回数据**: 执行实例日志内容
- **调用示例**:
```bash
curl -X GET "http://localhost:5000/api/v1/execution/123/logs?offset=0&limit=100" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

### 停止执行实例
- **接口**: POST /api/v1/execution/{id}/stop
- **描述**: 停止指定的执行实例
- **权限要求**: 任务所有者或管理员
- **URL参数**:
  - id: 执行实例ID
- **返回数据**: 操作结果
- **调用示例**:
```bash
curl -X POST "http://localhost:5000/api/v1/execution/123/stop" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

### 重试执行实例
- **接口**: POST /api/v1/execution/{id}/retry
- **描述**: 重试失败的执行实例
- **权限要求**: 任务所有者或管理员
- **URL参数**:
  - id: 执行实例ID
- **返回数据**: 新的执行实例信息
- **调用示例**:
```bash
curl -X POST "http://localhost:5000/api/v1/execution/123/retry" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

### 跳过执行实例
- **接口**: POST /api/v1/execution/{id}/skip
- **描述**: 跳过当前执行实例
- **权限要求**: 任务所有者或管理员
- **URL参数**:
  - id: 执行实例ID
- **返回数据**: 操作结果
- **调用示例**:
```bash
curl -X POST "http://localhost:5000/api/v1/execution/123/skip" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

### 获取执行实例状态
- **接口**: GET /api/v1/execution/{id}/status
- **描述**: 获取指定执行实例的当前状态
- **权限要求**: 登录用户
- **URL参数**:
  - id: 执行实例ID
- **返回数据**: 执行实例状态信息
- **调用示例**:
```bash
curl -X GET "http://localhost:5000/api/v1/execution/123/status" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

### 获取执行实例结果
- **接口**: GET /api/v1/execution/{id}/result
- **描述**: 获取指定执行实例的执行结果
- **权限要求**: 登录用户
- **URL参数**:
  - id: 执行实例ID
- **返回数据**: 执行实例结果数据
- **调用示例**:
```bash
curl -X GET "http://localhost:5000/api/v1/execution/123/result" \
  -H "Authorization: Bearer YOUR_TOKEN"

# Queqiao API Documentation

## Task Management API

### Get All Tasks
`GET /api/v1/task/`

**Authentication:** Required  
**Permissions:** None  
**Description:** Get all tasks for the current user  
**Response:**
```json
{
    "code": 0,
    "data": [
        {
            "id": 1,
            "name": "task_name",
            "status": "SUCCESS",
            // other task fields
        }
    ]
}
```

### Search Tasks
`POST /api/v1/task/search`

**Authentication:** Required  
**Parameters:**  
- create_user (optional): Task creator  
- name (optional): Task name  
- trans_type (optional): Transfer type  
- status (optional): Task status  
- apply_id (optional): Apply ID  
- task_type_id (optional): Task type ID  
- engine_id (optional): Engine ID  
- project_id (optional): Project ID  
- create_time (optional): Create time  
- source_org_id (optional): Source organization ID  
- sink_org_id (optional): Sink organization ID  

**Response:**
```json
{
    "code": 0,
    "data": [
        {
            "id": 1,
            "name": "task_name",
            // other task fields
        }
    ]
}
```

### Get Task by ID
`GET /api/v1/task/<int:id>`

**Authentication:** Required  
**Permissions:** Read permission on task  
**Response:**
```json
{
    "code": 0,
    "data": {
        "id": 1,
        "name": "task_name",
        // other task fields with component configs and permissions
    }
}
```

### Get Task by Name
`GET /api/v1/task/<string:name>`

**Authentication:** None  
**Response:**
```json
{
    "code": 0,
    "data": {
        "id": 1,
        "name": "task_name",
        // other task fields with component configs and permissions
    }
}
```

### Update Task
`POST /api/v1/task/<int:id>`

**Authentication:** Required  
**Permissions:** Write permission on task  
**Parameters:**  
- name (optional): Task name  
- tran_type (optional): Transfer type  
- alarm_receivers (optional): Alarm receivers  
- params (optional): Task parameters  
- engine_id (optional): Engine ID  
- source_configs (optional): Source configurations  
- sink_configs (optional): Sink configurations  

**Response:**
```json
{
    "code": 0,
    "data": {
        "id": 1
    }
}
```

### Task Offline
`GET /api/v1/task/offline/<int:id>`

**Authentication:** Required  
**Permissions:** Write permission on task  
**Response:**
```json
{
    "code": 0
}
```

### Task Online
`GET /api/v1/task/online/<int:id>`

**Authentication:** Required  
**Permissions:** Write permission on task  
**Response:**
```json
{
    "code": 0
}
```

### Get Task Command
`GET /api/v1/task/cmd/<int:id>`

**Parameters:**  
- etl_system (optional): ETL system name  

**Response:**
```json
{
    "code": 0,
    "data": "generated_command"
}
```

### Get Task Apply
`GET /api/v1/task/apply/<int:id>`

**Authentication:** Required  
**Permissions:** Read permission on task  
**Response:**
```json
{
    "code": 0,
    "data": {
        "id": 1,
        // apply fields
    }
}
```

### Execute Task
`GET /api/v1/task/exec/<string:name>`

**Authentication:** Required  
**Permissions:** Execute permission on task  
**Parameters:**  
- params (optional): Task parameters in HOCON format  
- async (optional): 1 for async execution (default), 0 for sync  
- etl_date (optional): ETL date  

**Response:**
```json
{
    "code": 0,
    "data": {
        "execution_id": 1
    }
}
```

### Get Task Executions
`GET /api/v1/task/execution/<int:id>`

**Authentication:** Required  
**Permissions:** Read permission on task  
**Response:**
```json
{
    "code": 0,
    "data": [
        {
            "id": 1,
            // execution fields
        }
    ]
}
```

### Execute ETL Task
`GET /api/v1/task/etl/<string:name>`

**Parameters:**  
- params (optional): Task parameters in HOCON format  
- etl_date (optional): ETL date  

**Response:**
```json
{
    "code": 0,
    "data": {
        "execution_id": 1
    }
}
```

### Create Task
`POST /api/v1/task/create`

**Parameters:**  
- whoami: Creator identifier  
- engine: Engine name  
- name: Task name  
- type: Task type  
- source_configs: Source configurations (must include 'dsn')  
- sink_configs: Sink configurations (must include 'dsn')  
- project: Project name  

**Response:**
```json
{
    "code": 0,
    "data": {
        "id": 1,
        // task fields
    }
}
```

### Create Temporary Task
`POST /api/v1/task/tmp`

**Parameters:**  
- whoami: Creator identifier  
- engine: Engine name  
- name: Task name  
- type: Task type  
- source_configs: Source configurations (must include 'dsn')  
- sink_configs: Sink configurations (must include 'dsn')  

**Response:**
```json
{
    "code": 0,
    "data": {
        "id": 1,
        // task fields
    }
}
```