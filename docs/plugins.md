# Queqiao 插件配置文档

本文档描述了Queqiao中所有插件的配置信息。

## 目录
- [数据源插件(Source)](#数据源插件source)
  - [FTPLink引擎](#ftplink引擎-source)
  - [DataX引擎](#datax引擎-source)
  - [Flink引擎](#flink引擎-source)
- [数据目标插件(Sink)](#数据目标插件sink)
  - [FTPLink引擎](#ftplink引擎-sink)
  - [DataX引擎](#datax引擎-sink)
  - [Flink引擎](#flink引擎-sink)

## 数据源插件(Source)

### FTPLink引擎 (Source)

#### MySQL数据源
- **插件名称**: mysql
- **插件说明**: MySQL数据库读取插件
- **配置参数**:
  - `sql`: SQL查询语句
    - 类型: text
    - 是否必填: 是
    - 默认值: 无
    - 示例: "select * from mart_fspinno_queqiao.firpos_custlist where partition_date='${now.delta(1).date}'"
    - 说明: 可执行的sql语句，请务必确认测试通过可执行
  - `ip`: MySQL主机地址
    - 类型: string
    - 是否必填: 否
    - 默认值: 无
    - 说明: 指定将覆盖默认的dsn配置
  - `port`: MySQL主机端口
    - 类型: int
    - 是否必填: 否
    - 默认值: 无
    - 说明: 指定将覆盖默认的dsn配置
  - `username`: MySQL用户名
    - 类型: string
    - 是否必填: 否
    - 默认值: 无
    - 说明: 指定将覆盖默认的dsn配置
  - `password`: MySQL密码
    - 类型: sstring
    - 是否必填: 否
    - 默认值: 无
    - 说明: 个人账号对应的密码
  - `db`: 目标库
    - 类型: string
    - 是否必填: 否
    - 默认值: 无
    - 示例: onesql/hive/spark
    - 说明: 连接的目标数据库

#### Hive数据源
- **插件名称**: hive
- **插件说明**: Hive集群配置式读取插件
- **配置参数**:
  - `table_name`: 目标表名
    - 类型: string
    - 是否必填: 是
    - 默认值: 无
    - 示例: mart_fspinno_queqiao.firpos_custlist
    - 说明: 数据导出的目标表
  - `table_cols`: 读取字段
    - 类型: string
    - 是否必填: 否
    - 默认值: 无
    - 示例: col1,col2,col3
    - 说明: 默认读取全部字段
  - `data_range`: 同步范围
    - 类型: set
    - 是否必填: 是
    - 默认值: full
    - 示例: full,partition
    - 说明: full：全量数据、partition：分区数据
  - `partition_key`: 分区键
    - 类型: string
    - 是否必填: 否
    - 默认值: partition_date
    - 示例: partition_date
    - 说明: 指定分区键，默认使用partition_date
  - `partition_value`: 分区值
    - 类型: date
    - 是否必填: 否
    - 默认值: 无
    - 说明: 选择分区数据范围（partition模式必选）

#### Talos数据源
- **插件名称**: talos
- **插件说明**: 美团Talos集群读取插件
- **配置参数**:
  - `sql`: SQL查询语句
    - 类型: text
    - 是否必填: 是
    - 默认值: 无
    - 示例: "select * from mart_fspinno_queqiao.firpos_custlist where partition_date='${now.delta(1).date}'"
    - 说明: 可执行的sql语句，请务必确认测试通过可执行
  - `uname`: Talos用户名
    - 类型: string
    - 是否必填: 否
    - 默认值: 无
    - 说明: 没有个人账号则使用默认系统账号talos_algo_ftplink
  - `passwd`: Talos密码
    - 类型: sstring
    - 是否必填: 否
    - 默认值: 无
    - 说明: 个人账号对应的密码
  - `engine`: Talos执行引擎
    - 类型: string
    - 是否必填: 否
    - 默认值: 无
    - 示例: onesql/hive/spark
    - 说明: talos执行引擎，默认为onesql

#### TalosCTAS数据源
- **插件名称**: talosctas
- **插件说明**: Talos查询建表插件
- **配置参数**:
  - `sql`: SQL查询语句
    - 类型: text
    - 是否必填: 是
    - 默认值: 无
    - 示例: select * from mart_fspinno_queqiao.firpos_custlist where partition_date='${now.delta(1).date}'
    - 说明: 可执行的sql语句，请务必确认测试通过可执行
  - `table`: 表名
    - 类型: text
    - 是否必填: 是
    - 默认值: 无
    - 示例: tianwen_tmp_72dbfcfcea8d42a6860fefc586f7414c
    - 说明: 写入表名，库名固定为mart_fsp_security_safetmp金服临时库

#### FTP/SFTP数据源
- **插件名称**: ftp
- **插件说明**: FTP/SFTP远程文件服务读取插件
- **配置参数**:
  - `file_name`: 文件名规则
    - 类型: string
    - 是否必填: 是
    - 默认值: 无
    - 示例: meituan_firpos_custlist_${now.delta(1).datekey}.txt
    - 说明: 目标文件名规则，支持匹配符，支持列表(逗号分隔)
  - `file_path`: 文件路径
    - 类型: string
    - 是否必填: 是
    - 默认值: 无
    - 示例: card/gd/${now.delta(1).datekey}
    - 说明: 目标文件存放路径，相对路径从/one-sftp-xy-bank/queqiao后开始
  - `ok_file`: 就绪文件规则
    - 类型: string
    - 是否必填: 否
    - 默认值: 无
    - 示例: .ok
    - 说明: 就绪文件名，解析规则见文档
  - `target_sep`: 列分隔符
    - 类型: string
    - 是否必填: 是
    - 默认值: ,
    - 示例: ,
    - 说明: 文件中的列分隔符，支持多位字符，不可见字符（\x01）请使用变量$sep01
  - `row_sep_to`: 替换后的行分隔符
    - 类型: string
    - 是否必填: 否
    - 默认值: 无
    - 说明: row_sep_to替换成row_sep_from行分隔符
  - `row_sep_from`: 原行分隔符
    - 类型: string
    - 是否必填: 否
    - 默认值: 无
    - 说明: row_sep_from替换成row_sep_to行分隔符
  - `has_header`: 文件头
    - 类型: boolean
    - 是否必填: 是
    - 默认值: 0
    - 说明: 文件是否存在文件头，入库时将去除文件头
  - `table_cols`: 字段列表
    - 类型: string
    - 是否必填: 否
    - 默认值: 无
    - 示例: col1,col2,col3
    - 说明: 文件数据对应的字段列表

#### 本地文件数据源
- **插件名称**: file
- **插件说明**: 本地文件上传插件
- **配置参数**:
  - `file`: 上传本地文件
    - 类型: file
    - 是否必填: 是
    - 默认值: 无
    - 说明: 本地的txt/csv/excel等文件
  - `target_sep`: 列分隔符
    - 类型: string
    - 是否必填: 是
    - 默认值: special
    - 示例: ,
    - 说明: 文件中的列分隔符，支持多位字符，不可见字符（\x01）请使用变量special
  - `has_header`: 文件头
    - 类型: boolean
    - 是否必填: 否
    - 默认值: 0
    - 说明: 文件是否存在文件头，如有则去除
  - `table_cols`: 字段列表
    - 类型: string
    - 是否必填: 否
    - 默认值: 无
    - 示例: col1,col2,col3
    - 说明: 文件数据对应的字段列表，逗号隔开，用于标识元数据，与文件头同时存在时取当前值

#### HTTP数据源
- **插件名称**: http
- **插件说明**: HTTP远程文件读取插件
- **配置参数**:
  - `url`: 请求地址
    - 类型: string
    - 是否必填: 是
    - 默认值: 无
    - 示例: http://xxx.com/xxx.csv
    - 说明: 访问远程http文件
  - `params`: 请求参数
    - 类型: string
    - 是否必填: 否
    - 默认值: 无
    - 示例: {"key":"value"}
    - 说明: http请求参数,json格式
  - `target_sep`: 列分隔符
    - 类型: string
    - 是否必填: 是
    - 默认值: special
    - 示例: ,
    - 说明: 文件中的列分隔符，支持多位字符，不可见字符（\x01）请使用变量special
  - `has_header`: 文件头
    - 类型: boolean
    - 是否必填: 否
    - 默认值: 0
    - 说明: 文件是否存在文件头，如有则去除
  - `table_cols`: 字段列表
    - 类型: string
    - 是否必填: 否
    - 默认值: 无
    - 示例: col1,col2,col3
    - 说明: 文件数据对应的字段列表，逗号隔开，用于标识元数据，与文件头同时存在时取当前值

#### Fate数据源
- **插件名称**: fate
- **插件说明**: Fate服务数据读取插件
- **配置参数**:
  - `role`: fate服务角色
    - 类型: string
    - 是否必填: 是
    - 默认值: guest
    - 示例: guest,host
    - 说明: fate server角色名
  - `party_id`: PartyId
    - 类型: string
    - 是否必填: 是
    - 默认值: 无
    - 示例: 10005
    - 说明: Party ID（-p）
  - `job_id`: 作业id
    - 类型: string
    - 是否必填: 否
    - 默认值: 0
    - 示例: 202209110048528627860
    - 说明: fate提交任务后返回的jobid，列表请使用逗号隔开
  - `cols`: 列名
    - 类型: text
    - 是否必填: 否
    - 默认值: 无
    - 示例: hash_id,score
    - 说明: 需要读取的列名，置空为全部
  - `component_name`: 组件名
    - 类型: string
    - 是否必填: 是
    - 默认值: 无
    - 示例: hetero_feature_binning_0
    - 说明: 指定组件名（-cpn）

#### FateTask数据源
- **插件名称**: fatetask
- **插件说明**: Fate服务任务执行插件
- **配置参数**:
  - `role`: fate服务角色
    - 类型: string
    - 是否必填: 是
    - 默认值: guest
    - 示例: guest,host
    - 说明: fate server角色名
  - `party_id`: PartyId
    - 类型: string
    - 是否必填: 是
    - 默认值: 无
    - 示例: 10005
    - 说明: Party ID（-p）
  - `job_id`: 作业id
    - 类型: string
    - 是否必填: 否
    - 默认值: 0
    - 示例: 202209110048528627860
    - 说明: fate提交任务后返回的jobid，列表请使用逗号隔开
  - `cols`: 列名
    - 类型: text
    - 是否必填: 否
    - 默认值: 无
    - 示例: hash_id,score
    - 说明: 需要读取的列名，置空为全部
  - `component_name`: 组件名
    - 类型: string
    - 是否必填: 是
    - 默认值: 无
    - 示例: hetero_feature_binning_0
    - 说明: 指定组件名（-cpn）
  - `bucket_cnt`: 数据分桶个数
    - 类型: int
    - 是否必填: 是
    - 默认值: 0
    - 示例: 0,50
    - 说明: 数据分桶个数，0为不分桶
  - `job_conf`: 任务配置
    - 类型: text
    - 是否必填: 是
    - 默认值: 无
    - 说明: fate任务执行的配置内容（-c），内置变量bucket_id
  - `job_dsl`: DSL配置
    - 类型: text
    - 是否必填: 是
    - 默认值: 无
    - 说明: fate任务执行的DSL内容（-d）

#### HiveSQL数据源
- **插件名称**: hivesql
- **插件说明**: Hive集群（自定义SQL）读取插件
- **配置参数**:
  - `sql`: SQL查询语句
    - 类型: text
    - 是否必填: 是
    - 默认值: 无
    - 示例: select * from mart_fspinno_queqiao.firpos_custlist where partition_date='${now.delta(1).date}'
    - 说明: 可执行的sql语句，请务必确认测试通过可执行

### DataX引擎 (Source)

#### MySQL数据源
- **插件名称**: mysql
- **插件说明**: MySQL数据库读取插件
- **配置参数**:
  - `sql`: SQL查询语句
    - 类型: text
    - 是否必填: 是
    - 默认值: 无
    - 示例: select * from mart_fspinno_queqiao.firpos_custlist where partition_date='${now.delta(1).date}'
    - 说明: 可执行的sql语句，请务必确认测试通过可执行
  - `split_pk`: 分割字段
    - 类型: string
    - 是否必填: 否
    - 默认值: 无
    - 示例: id
    - 说明: 使用该字段进行数据分片（并行同步），仅支持整数类型

## 数据目标插件(Sink)

### FTPLink引擎 (Sink)

#### Hive数据目标
- **插件名称**: hive
- **插件说明**: Hive集群写入插件
- **配置参数**:
  - `table_name`: 入库表名
    - 类型: string
    - 是否必填: 是
    - 默认值: 无
    - 示例: firpos_custlist
    - 说明: 导入目标表，请勿输入库名（库名根据dsn获取）
  - `table_type`: 入库表类型
    - 类型: set
    - 是否必填: 是
    - 默认值: full
    - 示例: full,partition
    - 说明: full：全量表、partition：日分区表
  - `partition_key`: 分区字段
    - 类型: string
    - 是否必填: 否
    - 默认值: 无
    - 示例: partition_date
    - 说明: 表类型为分区表时生效，将会使用指定字段作为动态分区值写入分区表，若无此值则将默认以partition_date为key，当前日期为value写入分区表
  - `partition_value`: 分区值
    - 类型: string
    - 是否必填: 否
    - 默认值: 无
    - 示例: 2023-11-27
    - 说明: 表类型为分区表时生效，将当前数据表写入指定分区中，若无指定则以partition_key规则为准
  - `custom_cols`: 自定义字段信息
    - 类型: string
    - 是否必填: 否
    - 默认值: 无
    - 示例: col1:A,col2:B
    - 说明: 用户自定义的字段信息，将作为附加字段写入当前表中
  - `write_mode`: 写入模式
    - 类型: set
    - 是否必填: 是
    - 默认值: overwrite
    - 示例: overwrite,append
    - 说明: overwrite：覆盖写入、append：追加写入
  - `target_sep`: 列分隔符
    - 类型: string
    - 是否必填: 是
    - 默认值: ,
    - 示例: ,
    - 说明: 文件中的列分隔符，支持多位字符，不可见字符（\x01）请使用变量$sep01
  - `life_cycle`: 表生命周期
    - 类型: int
    - 是否必填: 否
    - 默认值: 无
    - 示例: 93
    - 说明: 表的生命周期属性标识
  - `table_description`: 表注释
    - 类型: string
    - 是否必填: 否
    - 默认值: 无
    - 示例: "这是一个示例表的注释"
    - 说明: 用于设置Hive表的注释信息

#### Fate数据目标
- **插件名称**: fate
- **插件说明**: Fate服务数据写入插件
- **配置参数**:
  - `party_id`: PartyId
    - 类型: string
    - 是否必填: 是
    - 默认值: 无
    - 示例: 10005
    - 说明: Party ID（-p）
  - `bucket_cnt`: 数据分桶个数
    - 类型: int
    - 是否必填: 是
    - 默认值: 0
    - 示例: 0,50
    - 说明: 数据分桶个数，0为不分桶
  - `bucket_col`: 数据分桶字段
    - 类型: string
    - 是否必填: 否
    - 默认值: 无
    - 示例: col1
    - 说明: 数据分桶字段，将根据该字段的枚举值进行分桶
  - `null_format`: 空值处理
    - 类型: string
    - 是否必填: 否
    - 默认值: 无
    - 示例: NULL
    - 说明: 将会以配置值代替NULL（空值）
  - `partition`: 分区数
    - 类型: int
    - 是否必填: 是
    - 默认值: 8
    - 示例: 8,16
    - 说明: 用于存储数据的分区数
  - `work_mode`: 工作模式
    - 类型: int
    - 是否必填: 是
    - 默认值: 1
    - 示例: 0,1
    - 说明: 0为单机版，1为集群版
  - `namespace`: 命名空间
    - 类型: string
    - 是否必填: 是
    - 默认值: 无
    - 示例: mt_platform
    - 说明: 存储数据表的标识符号
  - `tablename`: 存储表名
    - 类型: string
    - 是否必填: 是
    - 默认值: 无
    - 示例: mt_userlist
    - 说明: 存储数据表的标识符号
  - `key_idx`: 主键位置索引
    - 类型: int
    - 是否必填: 否
    - 默认值: 0
    - 示例: 0
    - 说明: 主键key所在的列索引（分桶时不可为空）

#### FateTask数据目标
- **插件名称**: fatetask
- **插件说明**: Fate服务任务执行插件
- **配置参数**:
  - `party_id`: PartyId
    - 类型: string
    - 是否必填: 是
    - 默认值: 无
    - 示例: 10005
    - 说明: Party ID（-p）
  - `bucket_cnt`: 数据分桶个数
    - 类型: int
    - 是否必填: 是
    - 默认值: 0
    - 示例: 0,50
    - 说明: 数据分桶个数，0为不分桶
  - `task_bucket_cnt`: 任务分桶提交个数
    - 类型: int
    - 是否必填: 否
    - 默认值: 0
    - 示例: 0,50
    - 说明: 任务分桶提交个数，0位不分桶只提交一次任务
  - `job_conf`: 任务配置
    - 类型: text
    - 是否必填: 是
    - 默认值: 无
    - 说明: fate任务执行的配置内容（-c），内置变量bucket_id
  - `job_dsl`: DSL配置
    - 类型: text
    - 是否必填: 是
    - 默认值: 无
    - 说明: fate任务执行的DSL内容（-d）
  - `validate_rate`: 是否校验结果数量
    - 类型: int
    - 是否必填: 否
    - 默认值: -1
    - 示例: 0.9
    - 说明: 非0时进行结果校验（与源文件的数量比，PSI任务使用）
  - `bucket_col`: 数据分桶字段
    - 类型: string
    - 是否必填: 否
    - 默认值: 无
    - 示例: col1
    - 说明: 数据分桶字段，将根据该字段的枚举值进行分桶
  - `null_format`: 空值处理
    - 类型: string
    - 是否必填: 否
    - 默认值: 无
    - 示例: NULL
    - 说明: 将会以配置值代替NULL（空值）
  - `partition`: 分区数
    - 类型: int
    - 是否必填: 是
    - 默认值: 8
    - 示例: 8,16
    - 说明: 用于存储数据的分区数
  - `work_mode`: 工作模式
    - 类型: int
    - 是否必填: 是
    - 默认值: 1
    - 示例: 0,1
    - 说明: 0为单机版，1为集群版
  - `namespace`: 命名空间
    - 类型: string
    - 是否必填: 是
    - 默认值: 无
    - 示例: mt_platform
    - 说明: 存储数据表的标识符号
  - `tablename`: 存储表名
    - 类型: string
    - 是否必填: 是
    - 默认值: 无
    - 示例: mt_userlist
    - 说明: 存储数据表的标识符号
  - `key_idx`: 主键位置索引
    - 类型: int
    - 是否必填: 否
    - 默认值: 0
    - 示例: 0
    - 说明: 主键key所在的列索引（分桶时不可为空）

### DataX引擎 (Sink)

#### MySQL数据目标
- **插件名称**: mysql
- **插件说明**: MySQL数据库写入插件
- **配置参数**:
  - `table_name`: 入库表名
    - 类型: string
    - 是否必填: 是
    - 默认值: 无
    - 示例: firpos_custlist
    - 说明: 目标表名
  - `table_cols`: 写入字段
    - 类型: string
    - 是否必填: 是
    - 默认值: 无
    - 示例: col1,col2,col3
    - 说明: 写入的字段列表，比如与source字段一致
  - `write_mode`: 写入模式
    - 类型: set
    - 是否必填: 是
    - 默认值: overwrite
    - 示例: overwrite,append
    - 说明: overwrite：覆盖写入、append：追加写入
  - `batch_size`: 写入批次大小
    - 类型: int
    - 是否必填: 否
    - 默认值: 1024
    - 说明: 一次性批量提交的记录数大小
  - `pre_sql`: 写入前执行sql
    - 类型: text
    - 是否必填: 否
    - 默认值: 无
    - 说明: 数据写入前将会执行的sql
  - `post_sql`: 写入后执行sql
    - 类型: text
    - 是否必填: 否
    - 默认值: 无
    - 说明: 数据写入后将会执行的sql

#### 本地文件目标
- **插件名称**: file
- **插件说明**: 本地文件写入插件
- **配置参数**:
  - `file_name`: 文件名规则
    - 类型: string
    - 是否必填: 是
    - 默认值: 无
    - 示例: meituan_firpos_custlist_${now.delta(1).datekey}.txt
    - 说明: 目标文件名规则
  - `file_path`: 文件路径
    - 类型: string
    - 是否必填: 是
    - 默认值: 无
    - 示例: /tmp/test/${now.delta(1).datekey}
    - 说明: 目标文件存放路径
  - `target_sep`: 列分隔符
    - 类型: string
    - 是否必填: 是
    - 默认值: ,
    - 示例: ,
    - 说明: 文件中的列分隔符，支持多位字符，不可见字符（\x01）请使用变量$sep01
