#!/usr/bin/env python
# coding=utf-8

"""
专门用于调试 HEADN 接口卡住问题的工具
"""

import subprocess
import time
import threading
import signal
import sys
import os

class HeadnDebugger:
    def __init__(self):
        self.monitoring = False
        self.log_file = "logs/queqiao.log"
        
    def monitor_logs(self, duration=60):
        """实时监控日志，特别关注 HEADN 相关的日志"""
        print(f"🔍 开始监控日志 {duration} 秒...")
        print("=" * 60)
        
        if not os.path.exists(self.log_file):
            print(f"❌ 日志文件不存在: {self.log_file}")
            return
        
        # 使用 tail -f 监控日志
        try:
            cmd = ["tail", "-f", self.log_file]
            process = subprocess.Popen(cmd, stdout=subprocess.PIPE, stderr=subprocess.PIPE, 
                                     universal_newlines=True, bufsize=1)
            
            start_time = time.time()
            headn_started = False
            last_log_time = time.time()
            
            print("📊 监控中... (按 Ctrl+C 停止)")
            print("-" * 60)
            
            while time.time() - start_time < duration:
                try:
                    line = process.stdout.readline()
                    if line:
                        current_time = time.time()
                        last_log_time = current_time
                        
                        # 检查是否是 HEADN 相关日志
                        if any(keyword in line for keyword in ['HEADN_', 'SFTP_HEADN', 'SFTP_RUN_CMD']):
                            timestamp = time.strftime("%H:%M:%S", time.localtime())
                            print(f"[{timestamp}] {line.strip()}")
                            
                            # 检测关键状态
                            if "Starting headn operation" in line:
                                headn_started = True
                                print("🚀 HEADN 操作开始")
                            elif "About to call run_cmd" in line:
                                print("⏳ 即将执行 SSH 命令")
                            elif "run_cmd completed" in line:
                                print("✅ SSH 命令执行完成")
                            elif "headn operation completed" in line:
                                print("🎉 HEADN 操作成功完成")
                                headn_started = False
                            elif "Operation failed" in line:
                                print("❌ HEADN 操作失败")
                                headn_started = False
                        
                        # 检查是否卡住了
                        if headn_started and current_time - last_log_time > 30:
                            print("⚠️  警告: HEADN 操作可能卡住了 (30秒无新日志)")
                    
                    time.sleep(0.1)
                    
                except KeyboardInterrupt:
                    print("\n🛑 用户中断监控")
                    break
                    
            process.terminate()
            
        except Exception as e:
            print(f"❌ 监控日志时出错: {str(e)}")
    
    def test_ssh_command_directly(self, host, username, password, file_path, lines=10):
        """直接测试 SSH 命令执行"""
        print(f"\n🧪 直接测试 SSH 命令...")
        print("=" * 60)
        
        command = f"head -n {lines} '{file_path}'"
        ssh_cmd = [
            "sshpass", "-p", password,
            "ssh", "-o", "StrictHostKeyChecking=no",
            f"{username}@{host}",
            command
        ]
        
        print(f"执行命令: {' '.join(ssh_cmd[:-1])} '{command}'")
        
        try:
            start_time = time.time()
            result = subprocess.run(ssh_cmd, capture_output=True, text=True, timeout=30)
            end_time = time.time()
            
            print(f"⏱️  执行时间: {end_time - start_time:.2f} 秒")
            print(f"返回码: {result.returncode}")
            
            if result.returncode == 0:
                print(f"✅ 命令执行成功")
                print(f"输出长度: {len(result.stdout)} 字符")
                print(f"前100字符: {result.stdout[:100]}...")
            else:
                print(f"❌ 命令执行失败")
                print(f"错误输出: {result.stderr}")
                
        except subprocess.TimeoutExpired:
            print("⏰ SSH 命令执行超时 (30秒)")
        except FileNotFoundError:
            print("❌ sshpass 命令未找到，请安装: brew install sshpass")
        except Exception as e:
            print(f"❌ SSH 命令执行异常: {str(e)}")
    
    def analyze_file_characteristics(self, host, username, password, file_path):
        """分析文件特征，可能影响读取性能"""
        print(f"\n📊 分析文件特征...")
        print("=" * 60)
        
        commands = [
            ("文件大小", f"ls -lh '{file_path}'"),
            ("文件行数", f"wc -l '{file_path}'"),
            ("文件类型", f"file '{file_path}'"),
            ("前5行", f"head -n 5 '{file_path}'"),
            ("磁盘使用", f"df -h $(dirname '{file_path}')"),
        ]
        
        for desc, command in commands:
            print(f"\n🔍 {desc}:")
            ssh_cmd = [
                "sshpass", "-p", password,
                "ssh", "-o", "StrictHostKeyChecking=no",
                f"{username}@{host}",
                command
            ]
            
            try:
                result = subprocess.run(ssh_cmd, capture_output=True, text=True, timeout=10)
                if result.returncode == 0:
                    print(f"   {result.stdout.strip()}")
                else:
                    print(f"   错误: {result.stderr.strip()}")
            except Exception as e:
                print(f"   异常: {str(e)}")
    
    def suggest_solutions(self):
        """提供解决方案建议"""
        print(f"\n💡 问题排查和解决建议:")
        print("=" * 60)
        
        suggestions = [
            "1. 检查网络连接:",
            "   - SSH 连接是否稳定",
            "   - 网络延迟是否过高",
            "   - 防火墙是否阻止连接",
            "",
            "2. 检查文件特征:",
            "   - 文件是否过大 (>1GB)",
            "   - 文件是否为二进制文件",
            "   - 文件权限是否正确",
            "",
            "3. 检查服务器资源:",
            "   - CPU 使用率",
            "   - 内存使用率", 
            "   - 磁盘 I/O 状态",
            "",
            "4. 优化建议:",
            "   - 增加 SSH 命令超时时间",
            "   - 使用更小的行数进行测试",
            "   - 考虑使用备用方案 (流式读取)",
            "",
            "5. 临时解决方案:",
            "   - 修改代码直接使用 _headn_fallback 方法",
            "   - 增加更详细的超时处理",
            "   - 添加命令执行进度监控"
        ]
        
        for suggestion in suggestions:
            print(suggestion)

def main():
    debugger = HeadnDebugger()
    
    print("🔧 HEADN 接口调试工具")
    print("=" * 60)
    
    # 从日志中提取的信息
    file_path = "/one-sftp-xy-bank/queqiao/loan/ylsj_jsyh/min/********/account_fund.period_bill_status_trace_********.txt"
    
    print(f"📁 目标文件: {file_path}")
    print(f"📊 请求行数: 100")
    
    # 提供选项
    print(f"\n选择调试操作:")
    print("1. 实时监控日志 (60秒)")
    print("2. 分析文件特征 (需要SSH信息)")
    print("3. 直接测试SSH命令 (需要SSH信息)")
    print("4. 显示解决建议")
    print("5. 全部执行 (需要SSH信息)")
    
    choice = input("\n请选择 (1-5): ").strip()
    
    if choice == "1":
        debugger.monitor_logs(60)
    elif choice in ["2", "3", "5"]:
        print("\n请提供SSH连接信息:")
        host = input("主机地址: ").strip()
        username = input("用户名: ").strip()
        password = input("密码: ").strip()
        
        if choice == "2":
            debugger.analyze_file_characteristics(host, username, password, file_path)
        elif choice == "3":
            debugger.test_ssh_command_directly(host, username, password, file_path, 10)
        elif choice == "5":
            debugger.analyze_file_characteristics(host, username, password, file_path)
            debugger.test_ssh_command_directly(host, username, password, file_path, 10)
            debugger.monitor_logs(30)
    elif choice == "4":
        debugger.suggest_solutions()
    else:
        print("无效选择")
    
    debugger.suggest_solutions()

if __name__ == "__main__":
    main()
