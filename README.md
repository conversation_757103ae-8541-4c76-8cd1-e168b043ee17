# Queqiao (雀桥)

## 项目简介
Queqiao是一个强大的数据同步和处理平台，支持多种数据源之间的数据传输和转换。该平台提供了丰富的插件系统，支持包括MySQL、Hive、Talos等多种数据源，并具备完善的任务管理、权限控制和监控告警功能。

## 功能特性
- 多数据源支持：支持MySQL、Hive、Talos等多种数据源的读写
- 插件化架构：提供灵活的插件系统，支持自定义数据源和处理逻辑
- 任务管理：完整的任务生命周期管理，包括创建、配置、调度和监控
- 权限控制：细粒度的权限管理系统，确保数据安全
- 监控告警：实时监控任务执行状态，支持多种告警方式
- Web界面：提供友好的Web操作界面

## 环境要求
- Python 3.6+
- Redis
- MySQL
- Celery 5.3.1+
- Flask 1.1.1+
- 其他依赖请参考requirements.txt

## 安装步骤
1. 克隆代码仓库
```bash
git clone [repository_url]
cd queqiao
```

2. 安装依赖
```bash
pip install -r requirements.txt
```

3. 初始化配置
```bash
cd bin
python init.py
```

4. 启动服务
```bash
./start.sh
```

## 使用说明
1. 启动服务后，访问Web界面进行操作
2. 创建数据同步任务：
   - 配置源数据源和目标数据源
   - 设置任务参数和调度策略
   - 提交任务
3. 监控任务执行状态和日志

## 目录结构
- `bin/`: 包含各种管理脚本
- `docs/`: 项目文档
  - `api.md`: API接口文档
  - `plugins.md`: 插件配置文档
- `queqiao/`: 核心代码
  - `api.py`: API接口实现
  - `core/`: 核心功能模块
  - `plugin/`: 插件系统
  - `service/`: 业务服务层
- `tests/`: 测试用例

## 配置说明
主要配置文件位于`instance/`目录：
- `default.py`: 默认配置
- `gunicorn.py`: Gunicorn服务器配置

## 常用命令
- 启动服务：`bin/start.sh`
- 停止服务：`bin/shutdown.sh`
- 重启服务：`bin/restart.sh`
- 查看状态：`bin/status.sh`
- 清理缓存：`bin/clean.sh`

## 更多文档
- [API文档](docs/api.md)
- [插件配置文档](docs/plugins.md)

## 许可证
[License信息]