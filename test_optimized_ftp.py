#!/usr/bin/env python
# coding=utf-8

"""
测试优化后的 FtpServer wc 和 headn 方法
验证流式读取功能
"""

import os
import sys
import tempfile
import io

# 添加项目路径到 sys.path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from queqiao.util.conn.ftp import FtpServer


class MockFtpClient:
    """模拟FTP客户端，用于测试"""
    
    def __init__(self, test_content):
        self.test_content = test_content.encode('utf-8')
        
    def voidcmd(self, cmd):
        """模拟 voidcmd"""
        pass
        
    def retrbinary(self, cmd, callback, blocksize=8192):
        """模拟 retrbinary，分块发送数据"""
        data = self.test_content
        pos = 0
        while pos < len(data):
            chunk = data[pos:pos + blocksize]
            callback(chunk)
            pos += blocksize


def test_wc_method():
    """测试 wc 方法的流式读取"""
    print("Testing FtpServer.wc method with streaming...")
    
    # 创建测试内容
    test_content = "Line 1\nLine 2\nLine 3\nLine 4\nLine 5\n"
    expected_lines = 5
    
    # 创建模拟的 FtpServer
    ftp_config = {'protocol': 'ftp', 'ip': 'test', 'port': 21, 'username': 'test', 'password': 'test', 'work_dir': '/'}
    ftp_server = FtpServer(ftp_config)
    
    # 替换客户端为模拟客户端
    ftp_server.client = MockFtpClient(test_content)
    
    # 测试 wc 方法
    try:
        line_count = ftp_server.wc('test_file.txt')
        print(f"✓ wc method returned: {line_count} lines")
        assert line_count == expected_lines, f"Expected {expected_lines} lines, got {line_count}"
        print("✓ wc method test passed!")
    except Exception as e:
        print(f"✗ wc method test failed: {str(e)}")
        return False
    
    return True


def test_headn_method():
    """测试 headn 方法的流式读取"""
    print("\nTesting FtpServer.headn method with streaming...")
    
    # 创建测试内容
    test_content = "Line 1\nLine 2\nLine 3\nLine 4\nLine 5\nLine 6\nLine 7\nLine 8\nLine 9\nLine 10\n"
    
    # 创建模拟的 FtpServer
    ftp_config = {'protocol': 'ftp', 'ip': 'test', 'port': 21, 'username': 'test', 'password': 'test', 'work_dir': '/'}
    ftp_server = FtpServer(ftp_config)
    
    # 替换客户端为模拟客户端
    ftp_server.client = MockFtpClient(test_content)
    
    # 测试 headn 方法 - 获取前3行
    try:
        content = ftp_server.headn('test_file.txt', n=3)
        expected_content = "Line 1\nLine 2\nLine 3"
        print(f"✓ headn method returned:\n{content}")
        assert content == expected_content, f"Expected:\n{expected_content}\nGot:\n{content}"
        print("✓ headn method test passed!")
    except Exception as e:
        print(f"✗ headn method test failed: {str(e)}")
        return False
    
    return True


def test_large_file_simulation():
    """模拟大文件测试"""
    print("\nTesting with simulated large file...")
    
    # 创建大文件内容（1000行）
    lines = [f"This is line {i+1} with some content to make it longer" for i in range(1000)]
    test_content = '\n'.join(lines) + '\n'
    
    # 创建模拟的 FtpServer
    ftp_config = {'protocol': 'ftp', 'ip': 'test', 'port': 21, 'username': 'test', 'password': 'test', 'work_dir': '/'}
    ftp_server = FtpServer(ftp_config)
    
    # 替换客户端为模拟客户端
    ftp_server.client = MockFtpClient(test_content)
    
    # 测试 wc 方法
    try:
        line_count = ftp_server.wc('large_file.txt')
        print(f"✓ Large file wc: {line_count} lines")
        assert line_count == 1000, f"Expected 1000 lines, got {line_count}"
    except Exception as e:
        print(f"✗ Large file wc test failed: {str(e)}")
        return False
    
    # 测试 headn 方法 - 获取前10行
    try:
        content = ftp_server.headn('large_file.txt', n=10)
        lines_returned = content.split('\n')
        print(f"✓ Large file headn returned {len(lines_returned)} lines")
        assert len(lines_returned) == 10, f"Expected 10 lines, got {len(lines_returned)}"
        assert lines_returned[0] == "This is line 1 with some content to make it longer"
        assert lines_returned[9] == "This is line 10 with some content to make it longer"
    except Exception as e:
        print(f"✗ Large file headn test failed: {str(e)}")
        return False
    
    return True


def main():
    """主测试函数"""
    print("=" * 80)
    print("Testing Optimized FtpServer Methods (No File Download Required)")
    print("=" * 80)
    
    success = True
    
    # 运行测试
    success &= test_wc_method()
    success &= test_headn_method()
    success &= test_large_file_simulation()
    
    print("\n" + "=" * 80)
    if success:
        print("✅ All tests passed!")
        print("\n🚀 Optimization Benefits:")
        print("1. ✓ No temporary file creation required")
        print("2. ✓ No disk I/O for file downloads")
        print("3. ✓ Memory efficient streaming processing")
        print("4. ✓ Network bandwidth optimized")
        print("5. ✓ Faster execution for large files")
        print("\n📋 Technical Details:")
        print("- wc: Uses FTP retrbinary with callback to count lines in stream")
        print("- headn: Uses FTP retrbinary with callback to read only needed lines")
        print("- Both methods process data as it arrives over the network")
        print("- No intermediate file storage required")
    else:
        print("❌ Some tests failed!")
    
    print("=" * 80)
    return 0 if success else 1


if __name__ == "__main__":
    exit(main())
