namespace java com.sankuai.xm.oa.approval.client.thrift

include "definition.thrift"

typedef definition.XmBusinessTException XmBusinessTException
typedef definition.LongResponse LongResponse

/**
 * 同一层级的审批关系
 */
enum Relation {
   /**
    * 某一个审批人通过就行
    */
   OR = 0,

   /**
   * 所有人通过才行,不分顺序
   */
   AND = 1,

   /**
    * 所有人按顺序通过才行
   */
   ORDERED = 2
 }

/**
* 审批层级
**/
struct XmApprovingLevel {
    1: list<i64> approvers;
    2: Relation relation;
}

struct DxApproverStatus {
    1:i64 uid;
    2:i64 cid;
    3:i32 status;
    4:i32 seq;
    5:i16 relation
    6:string comment;
    7:i64 cts;
    8:i64 uts;
}

struct XmApply {
    1: i64 applyId;         // 审批Id
    2: i32 templateId;      // 模板Id
    3: i64 cid;             // 企业Id
    4: string applyName;    // 审批名称
    5: i64 sponsorUid;      // 审批发起人uid
    6: i64 cts;             // 审批创建时间
    7: i64 uts;             // 审批更新时间
    8: i16 status;          // 审批状态
    9: string data;         // 表单数据
}

// 审批实例查询条件
struct XmApplyQuery {
    1: optional i32 offset = 0;     // 查询结果偏移（从0开始）
    2: optional i32 limit = 10;     // 分页大小
    // 3-10保留

    11: optional i64 cid;           // 企业Id
    12: optional i32 templateId;    // 审批模板Id
    13: optional i32 status;        // 审批状态
    14: optional i64 sponsorUid;    // 审批发起人uid
    15: optional i64 ctsFrom;       // 审批创建时间起
    16: optional i64 ctsTo;         // 审批创建时间止
}

// 审批人详情
struct ApproverDetail {
    1: i64 uid;
    2: string name;
    3: string avatar;
    4: string uts;
    5: string result;
    6: i32 seq;
    7: i32 type;
    8: i16 relation;
    9: string comment;
}

// 抄送人详情
struct CopierDetail {
    1: i64 uid;
    2: string name;
    3: string avatar;
}

// 审批详情
struct ApplyDetail {
    1: string applyId;
    2: string templateId;
    3: string componentVersion;
    4: string applyName;
    5: string applyStatus;
    6: string sponsorName;
    7: string sponsorAvatar;
    8: i64 cts;
    9: string nextApprover;
    10: string Data;
    11: list<ApproverDetail> approvers;
    12: list<CopierDetail> copiers;
}
struct DxApprovalDetail {
    1:string businessId;
    2:string templateId;
    3:string name;
    4:string templateDesc;
    5:string applyId;
    6:string applyName;
    7:string applyCode;
    8:i64 cid;
    9:string version;
    10:i64 sponsorUid;
    11:string data;
    12:i32 status;
    13:string detailLink;
    14:i64 cts;
    15:i64 uts;
    16:string approvalDesc;
    17:list<DxApproverStatus> approvers;
    18:list<DxApproverStatus> copiers;
}

struct ApplyIdUidPair {
    1: i64 applyId;
    2: i64 uid;
}

// 提交审批请求参数
struct ApplySubmitReq {
    1: i32 templateId;
    2: i64 sponsorUid;
    3: string applyJsonData;
    4: list<XmApprovingLevel> approvingFlow;
    5: list<i64> ccList;
    6: bool canRelaunch;
}
//根据uid查询分页uid
struct ApplyIdsReq{
    1: i64 uid;
    2: i32 status;
    3: i32 offset;
    4: i32 limit;
}

struct ApplyPageResponse {
    1: bool hasNext;
    2: list<i64> applyIds;
}
// 撤消审批请求
struct ApplyWithdrawRequest {
    1: i64 uid;
    2: i64 applyId;
}

// 撤消审批响应
struct ApplyWithdrawResponse {
    1: i32 rescode;
    2: string message;
    3: map<string,string> data;
}
//催办响应
struct CommonApprovalResponse {
    1: i32 rescode;
    2: string message;
    3: map<string,string> data;
}

service XmApprovalService {
    // 发起审批实例
    LongResponse submitApply(
        1: i32 templateId,
        2: i64 sponsorUid,
        3: string applyJsonData,
        4: list<XmApprovingLevel> approvingFlow,
        5: list<i64> ccList
    ) throws (1: XmBusinessTException e)

    LongResponse submitRelaunchableApply(1: ApplySubmitReq applySubmitReq) throws (1: XmBusinessTException e)

    // 获取单个审批实例
    XmApply getApply(1: i64 applyId) throws (1: XmBusinessTException e)

    // 查询审批实例
    list<XmApply> queryApplys(1: XmApplyQuery applyQuery) throws (1: XmBusinessTException e)


    // 审批详情(包含每个审批人的审批状态)
    ApplyDetail applyDetail(1: i64 applyId,2: i64 uid) throws (1: XmBusinessTException e)

    map<i64,ApplyDetail> batchApplyDetail(1: list<ApplyIdUidPair> parameter) throws (1: XmBusinessTException e)

    // 撤消审批
    ApplyWithdrawResponse withdrawApply(1: ApplyWithdrawRequest request) throws (1: XmBusinessTException e)

    // 查询公共模板
    string listTemplate() throws (1: XmBusinessTException e)

    // 创建公共模板
    string createTemplate(1: i64 userId,2: string templateModifyReq) throws (1: XmBusinessTException e)

    // 更新公共模板
    string updateTemplate(1: string templateModifyReq) throws (1: XmBusinessTException e)

    // 获取模板数据
    string templateDetail(1: i64 templateId) throws (1: XmBusinessTException e)

    // 启用/禁用公共模板
    string enableTemplate(1: string templateEnableReq) throws (1: XmBusinessTException e)

    // 获取公共模板可见性
    string getTemplateVisible(1: i64 templateId) throws (1: XmBusinessTException e)

    // 更新公共模板可见性
    string updateTemplateVisible(1: string visibleUpdateReq) throws (1: XmBusinessTException e)

    string getTemplateName(1:i64 templateId) throws (1: XmBusinessTException e)

    CommonApprovalResponse pushAgree(1:i64 uid, 2:i64 applyId) throws (1: XmBusinessTException e)

    DxApprovalDetail queryDxApprovalDetail(1:i64 uid, 2:i64 applyId) throws (1: XmBusinessTException e)

    ApplyPageResponse queryApplyIdsByPage(1:ApplyIdsReq req) throws (1: XmBusinessTException e)
}