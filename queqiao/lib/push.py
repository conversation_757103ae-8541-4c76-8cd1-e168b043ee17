"""
Author: xiaohei
Date: 2022/5/10
Email: <EMAIL>
Host: xiaohei.info
"""
import abc

import six

from queqiao.conf.system import SystemConfig
from queqiao.log import LogFactory
from queqiao.util.comm import objutil
from queqiao.util.comm.dtutil import timer
from queqiao.util.mt.daxiang import DxPush

log = LogFactory.get_logger()


@six.add_metaclass(abc.ABCMeta)
class Pusher:

    @classmethod
    def get_pusher(cls, pusher_type=None):
        tcls = pusher_type if pusher_type else SystemConfig.read('DEFAULT_PUSHER')
        tcls = f'{tcls}{cls.__name__}'
        log.debug(f'get pusher class: {tcls}')
        return objutil.new_instance(__name__, tcls)

    @abc.abstractmethod
    def push(self, msg, receivers):
        pass


class DaxiangPusher(Pusher):
    def push(self, msg, receivers):
        DxPush.push_msg(msg, receivers)


class FilePusher(Pusher):

    def push(self, msg, receivers):
        msg_template = SystemConfig.read('ALARM_FILE_MSG_TEMP')
        alarm_file = SystemConfig.read('ALARM_FILE_PATH')

        ctime = timer.now().datetime
        receivers = receivers if isinstance(receivers, str) else ','.join(receivers)
        msg = msg_template.format(ctime=ctime, msg=msg, receivers=receivers)

        with open(alarm_file, 'a') as f:
            f.write(msg + '\n')
