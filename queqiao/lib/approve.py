"""
Author: xiaohei
Date: 2022/5/10
Email: <EMAIL>
Host: xiaohei.info
"""
import abc
import json

import six

from queqiao.conf.errors import NoPrivilegeException, ExternalException
from queqiao.conf.system import SystemConfig
from queqiao.lib.push import Pusher
from queqiao.log import LogFactory
from queqiao.util.comm import objutil, strutil
from queqiao.util.conn.ldap import LdapAuth
from queqiao.util.mt.daxiang import DxAudit
from queqiao.util.mt.org import OrgClient

log = LogFactory.get_logger()


@six.add_metaclass(abc.ABCMeta)
class Approver:
    def __init__(self, apply):
        self.apply = apply

    @classmethod
    def get_approver(cls, apply, approver_type=None):
        tcls = approver_type if approver_type else SystemConfig.read('DEFAULT_APPROVER')
        tcls = f'{tcls}{cls.__name__}'
        log.debug(f'get approver: {tcls}')
        return objutil.new_instance(__name__, tcls, apply)

    @abc.abstractmethod
    def create(self):
        pass

    @abc.abstractmethod
    def detail(self):
        pass

    @abc.abstractmethod
    def hurry_up(self):
        pass


# https://km.sankuai.com/page/526183509
class DaxiangApprover(Approver):

    def create(self):
        ldap_config = json.loads(SystemConfig.read('LDAP_REQUIRED_CONFIGS'))
        ldap = LdapAuth(**ldap_config)
        user = ldap.get_user(self.apply.create_user)
        # 获取第一个queqiao_user_用户组,queqiao_admin,queqiao_visitor,queqiao_user_rd,queqiao_user_bs,queqiao_user_sys
        group = [g for g in user.groups if g.startswith('queqiao_user_')]
        if not group:
            raise NoPrivilegeException(
                f'user {self.apply.create_user} dose not has a privilege for create apply, you may be a system visitor')
        user_group = group[0]
        log.debug(f'create daxiang aaprove with user group: {user_group}')

        content = self.apply.to_dict()
        org = OrgClient.from_flask_config()
        leader, org = org.get_emp_info(self.apply.create_user)
        content['apply_id'] = self.apply.id
        content['leader'] = leader
        content['org'] = org
        tasks = content.pop('tasks')
        if len(tasks) == 1:
            task = tasks[0]
            source = task.pop('source')
            sink = task.pop('sink')
            execution_meta = {'source': source['configs'], 'sink': sink['configs']}
            content['execution_meta'] = strutil.hocon_dumps(execution_meta)
            content.update(task)
        else:
            content['task_detail'] = self.apply.uri
        try:
            res = DxAudit.create_audit(self.apply.create_user, user_group, content)
        except Exception as why:
            raise ExternalException(str(why))
        apply_id = res['data']['apply_id']
        information = json.loads(self.apply.information) if self.apply.information else {}
        information['audit_id'] = apply_id
        self.apply.information = json.dumps(information)
        self.apply.save()
        return res['code'] == 0

    def detail(self):
        info = json.loads(self.apply.information)
        res = DxAudit.get_audit_detail(self.apply.create_user, info['audit_id'])
        if res['code'] > 0:
            return None
        return res['data']

    def hurry_up(self):
        info = json.loads(self.apply.information)
        DxAudit.push_agree(self.apply.create_user, info['audit_id'])


class InnerApprover(Approver):
    def __init__(self, apply):
        super(InnerApprover, self).__init__(apply)
        self.__pusher = Pusher.get_pusher()

    def __get_approvers(self):
        admins = self.apply.project.admins
        return admins if admins else SystemConfig.read('SYS_ADMIN')

    def create(self):
        msg = f'用户 {self.apply.create_user} 创建了一个审批任务，点击查看: {self.apply.uri}'
        self.__pusher.push(msg, self.__get_approvers())
        return True

    def detail(self):
        approvers = self.__get_approvers()
        return {'all_approvers': approvers, 'apply_id': self.apply.id, 'next_approver': None,
                'sponsor_name': self.apply.create_user}

    def hurry_up(self):
        self.create()
