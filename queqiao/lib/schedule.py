"""
Author: xiaohei
Date: 2022/5/11
Email: <EMAIL>
Host: xiaohei.info
"""
import abc

import six

from instance.default import PROJECT_PATH
from queqiao.conf.system import SystemConfig
from queqiao.log import LogFactory
from queqiao.util.comm import objutil

log = LogFactory.get_logger()


@six.add_metaclass(abc.ABCMeta)
class Scheduler:
    def __init__(self, task):
        self.task = task

    @classmethod
    def get_scheduler(cls, task, scheduler_type=None):
        tcls = scheduler_type if scheduler_type else SystemConfig.read('DEFAULT_SCHEDULER')
        tcls = f'{tcls}{cls.__name__}'
        log.debug(f'get scheduler class: {tcls}')
        return objutil.new_instance(__name__, tcls, task)

    @abc.abstractmethod
    def gen_cmd(self):
        pass


class CantorScheduler(Scheduler):

    def gen_cmd(self):
        cmd_list = ['任务类型: 其他任务（simple）',
                    f'任务名: data:queqiao-client-{self.task.etl_name}',
                    '启动命令:/home/<USER>/bin/sw orun',
                    '文件名: hadoop-fspinno-queqiao.pyspark.queqiao-client',
                    f'运行时参数: step0-args="{self.task.etl_name} $now.delta(1).date"']
        return '\n'.join(cmd_list)


class AzkabanScheduler(Scheduler):
    def gen_cmd(self):
        cmd = f'{PROJECT_PATH}/bin/client.py {self.task.etl_name} ' + '${etl_date}'
        return cmd
