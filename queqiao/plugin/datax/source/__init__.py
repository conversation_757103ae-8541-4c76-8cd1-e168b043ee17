"""
Author: xiaohei
Date: 2022/4/28
Email: <EMAIL>
Host: xiaohei.info
"""
from abc import abstractmethod

from queqiao.core.engine.base import Source

bigint_type = 'int,tinyint,smallint,mediumint,int,bigint'
double_type = 'float,double,decimal'
string_type = 'varchar,char,tinytext,text,mediumtext,longtext,year,datetime,timestamp,time'
date_type = 'date'
boolean_type = 'bit,bool'
binary_type = 'tinyblob,mediumblob,blob,longblob,varbinary'
total_types = {'bigint': bigint_type, 'double': double_type, 'string': string_type, 'date': date_type,
               'boolean': boolean_type, 'binary': binary_type}


class DataXSource(Source):
    # 固定输出参数
    def read(self, channel):
        self.logger.info(f'set reader config')
        channel.set_pipeline_param(reader=self._get_reader_config())

    @abstractmethod
    def _get_reader_config(self):
        pass
