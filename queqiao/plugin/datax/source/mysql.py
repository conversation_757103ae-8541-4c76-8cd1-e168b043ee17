"""
Author: xiaohei
Date: 2022/4/28
Email: <EMAIL>
Host: xiaohei.info
"""
import json

from queqiao.plugin.datax.source import total_types, DataXSource
from queqiao.util.comm import osutil

component = {
    'comment': 'mysql数据库',
    'configs': {
        'sql': {'name_cn': 'sql', 'type': 'text', 'default': None, 'required': 1,
                'demo': "select * from mart_fspinno_queqiao.firpos_custlist where partition_date='${now.delta(1).date}'",
                'comment': '可执行的sql语句，请务必确认测试通过可执行'},
        'split_pk': {'name_cn': '分割字段', 'type': 'string', 'default': None, 'required': 0,
                     'demo': 'id', 'comment': '使用该字段进行数据分片（并行同步），仅支持整数类型'}
    }
}


class MySQLSource(DataXSource):

    # todo: 多实例分库分表适配
    def _get_reader_config(self):
        dsn = self.config.dsn
        mysql_connect = json.loads(dsn.connect)
        query_sql = self.config.sql if self.config.sql.endswith(';') else f'{self.config.sql};'
        # 需要用户提供mysql_server,query_sql,split_pk
        mysql_reader_config = {
            'name': 'mysqlreader',
            'parameter': {
                'username': mysql_connect['username'],
                'password': mysql_connect['password'],
                'splitPk': self.config.split_pk,
                'connection': [
                    {
                        'querySql': [query_sql],
                        'jdbcUrl': [f'jdbc:mysql://{mysql_connect["ip"]}:{mysql_connect["port"]}/{mysql_connect["db"]}']
                    }
                ]
            }
        }
        return mysql_reader_config

    # def schema(self):
    #     dsn = self.config.dsn
    #     mysql_connect = json.loads(dsn.connect)
    #     sql = self.config.query_sql
    #
    #     mysql_table = sql.split(" from ")[-1].split(" ")[0]
    #     mysql_sql = f'use {mysql_connect["db"]};desc {mysql_table}'
    #     mysql_cmd = f'mysql -h {mysql_connect["ip"]} -u{mysql_connect["username"]} ' \
    #         f'-p{mysql_connect["password"]} -P{mysql_connect["port"]} -e "{mysql_sql}"'
    #     self.logger.info(f'getting mysql table meta, cmd: {mysql_cmd}')
    #     mysql_result = osutil.callb(mysql_cmd)
    #     if len(mysql_result) == 0:
    #         self.logger.error('get mysql column meta error, set meta to empty')
    #         columns = []
    #     else:
    #         mrs = [str(mr).replace("b'", "").replace("\\n'", "") for mr in mysql_result if '\\tNull\\t' not in str(mr)]
    #         mrs = [list((mr.split('\\t')[0], mr.split('\\t')[1], mr.split('\\t')[-1])) for mr in mrs]
    #         columns = [{"name": r[0], "type": self.__mysql_type_to_hive_type(r[1]), "comment": r[2]} for r in mrs]
    #         self.logger.info(f'get meta success, columns meta: {columns}')
    #     return columns
    #
    # def __mysql_type_to_hive_type(self, mysql_type):
    #     for hive_type in total_types.keys():
    #         mysql_types = total_types[hive_type].split(',')
    #         for t in mysql_types:
    #             if t in mysql_type:
    #                 return hive_type
    #     return 'string'
