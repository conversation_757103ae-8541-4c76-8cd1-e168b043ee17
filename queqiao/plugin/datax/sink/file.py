"""
Author: xiaohei
Date: 2022/4/28
Email: <EMAIL>
Host: xiaohei.info
"""

from queqiao.plugin.datax.sink import DataXSink

component = {
    'comment': '本地文件',
    'configs': {
        'file_name': {'name_cn': '文件名规则', 'type': 'string', 'default': None, 'required': 1,
                      'demo': 'meituan_firpos_custlist_${now.delta(1).datekey}.txt', 'comment': '目标文件名规则'},
        'file_path': {'name_cn': '文件路径', 'type': 'string', 'default': None, 'required': 1,
                      'demo': '/tmp/test/${now.delta(1).datekey}', 'comment': '目标文件存放路径'},
        # {'name': 'ok_file', 'name_cn': '就绪文件规则', 'type': 'string', 'default': None, 'required': 0,
        #  'demo': '.ok', 'comment': '就绪文件名，解析规则见: https://km.sankuai.com/page/1316077828'},
        # {'name': 'compress_type', 'name_cn': '压缩类型', 'type': 'set', 'default': 'none', 'required': 1,
        #  'demo': 'none,zip,gzip,tar.gz', 'comment': '目标文件的压缩类型，none为普通文本文件'},
        # {'name': 'compress_passwd', 'name_cn': '压缩密码', 'type': 'string', 'default': None, 'required': 0,
        #  'demo': None, 'comment': '若压缩包有加密则填入解压密码'},
        'target_sep': {'name_cn': '列分隔符', 'type': 'string', 'default': ',', 'required': 1,
                       'demo': ',', 'comment': '文件中的列分隔符，支持多位字符，不可见字符（\\x01）请使用变量$sep01'}

    }
}


class FileSink(DataXSink):

    def _get_writer_config(self):
        # 需要target_sep
        target_sep = '\x01' if self.config.target_sep == 'special' else self.config.target_sep
        file_writer_config = {
            "name": "txtfilewriter",
            "parameter": {
                "path": f"{self.config.file_path}",
                "fileName": f"{self.config.file_name}",
                "writeMode": "truncate",
                "encoding": "utf8",
                "fieldDelimiter": f"{target_sep}"
            }
        }
        return file_writer_config
