"""
Author: xiaohei
Date: 2022/5/24
Email: <EMAIL>
Host: xiaohei.info
"""
import json

from queqiao.plugin.datax.sink import DataXSink

component = {
    'comment': 'mysql数据库',
    'configs': {
        'table_name': {'name_cn': '入库表名', 'type': 'string', 'default': None, 'required': 1,
                       'demo': 'firpos_custlist', 'comment': '目标表名'},
        'table_cols': {'name_cn': '写入字段', 'type': 'string', 'default': None, 'required': 1,
                       'demo': 'col1,col2,col3', 'comment': '写入的字段列表，比如与source字段一致'},
        'write_mode': {'name_cn': '写入模式', 'type': 'set', 'default': 'overwrite', 'required': 1,
                       'demo': 'overwrite,append', 'comment': 'overwrite：覆盖写入、append：追加写入'},
        'batch_size': {'name_cn': '写入批次大小', 'type': 'int', 'default': '1024', 'required': 0,
                       'demo': None, 'comment': '一次性批量提交的记录数大小'},
        'pre_sql': {'name_cn': '写入前执行sql', 'type': 'text', 'default': None, 'required': 0,
                    'demo': None, 'comment': '数据写入前将会执行的sql'},
        'post_sql': {'name_cn': '写入后执行sql', 'type': 'text', 'default': None, 'required': 0,
                     'demo': None, 'comment': '	数据写入后将会执行的sql'}
    }
}


class MySQLSink(DataXSink):
    def _get_writer_config(self):
        dsn = self.config.dsn
        mysql_connect = json.loads(dsn.connect)
        mysql_writer_config = {
            "name": "mysqlwriter",
            "parameter": {
                "writeMode": self.config.write_mode,
                "username": mysql_connect['username'],
                "password": mysql_connect['username'],
                "column": self.config.table_cols.split(',') if isinstance(self.config.table_cols,
                                                                          str) else self.config.table_cols,
                "session": ["set session sql_mode='ANSI'"],
                "connection": [
                    {
                        "jdbcUrl":
                            f'jdbc:mysql://{mysql_connect["ip"]}:{mysql_connect["port"]}/{mysql_connect["db"]}',
                        "table": [self.config.table_name]
                    }
                ]
            }
        }
        if hasattr(self.config, 'pre_sql') and self.config.pre_sql:
            mysql_writer_config['parameter']['preSql'] = [self.config.pre_sql]
        if hasattr(self.config, 'post_sql') and self.config.post_sql:
            mysql_writer_config['parameter']['postSql'] = [self.config.post_sql]
        if hasattr(self.config, 'batch_size') and self.config.batch_size:
            mysql_writer_config['parameter']['batchSize'] = [self.config.batch_size]
        return mysql_writer_config
