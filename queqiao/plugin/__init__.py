"""
Author: xiaohei
Date: 2022/4/29
Email: <EMAIL>
Host: xiaohei.info
"""
from queqiao.core.engine.base import Source, Sink
from queqiao.log import LogFactory
from queqiao.util.comm import objutil

log = LogFactory.get_logger()

plugins = objutil.find_cls_in_pkg(__path__, __package__,
                                  lambda c: (issubclass(c, Source) or issubclass(c, Sink)) and len(
                                      str(c).split('.')) == 6, mdl_only=True, self_pkg_only=True)
log.info(f'load plugins: {plugins.keys()}')
