"""
Author: xiaohei
Date: 2022/4/29
Email: <EMAIL>
Host: xiaohei.info
"""
import random
import time

from queqiao.conf.enums import ExecutionStatus
from queqiao.plugin.ftplink.source.basehive import BaseHiveSource
from queqiao.util.mt.talos import TalosClient

component = {
    'comment': 'hive集群（美团talos）',
    'configs': {
        'sql': {'name_cn': 'sql', 'type': 'text', 'default': None, 'required': 1,
                'demo': "select * from mart_fspinno_queqiao.firpos_custlist where partition_date='${now.delta(1).date}'",
                'comment': '可执行的sql语句，请务必确认测试通过可执行'},
        'uname': {'name_cn': 'talos用户名', 'type': 'string', 'default': None, 'required': 0,
                  'demo': None, 'comment': '没有个人账号则使用默认系统账号talos_algo_ftplink'},
        'passwd': {'name_cn': 'talos密码', 'type': 'sstring', 'default': None, 'required': 0,
                   'demo': None, 'comment': '个人账号对应的密码'},
        'engine': {'name_cn': 'talos执行引擎', 'type': 'string', 'default': None, 'required': 0,
                   'demo': 'onesql/hive/spark', 'comment': 'talos执行引擎，默认为onesql'}
    }
}


class TalosSource(BaseHiveSource):

    def __init__(self, source_config):
        super().__init__(source_config)
        # 从配置中获取账号信息
        username = self.connect['uname'] if not self.config.uname else self.config.uname
        password = self.connect['passwd'] if not self.config.uname else self.config.passwd
        engine = self.connect['engine'] if not self.config.engine else self.config.engine
        self.client = TalosClient(username, password, engine, logger=self.logger)
        self.logger.info(f'init talos client with uname: {username}, engine: {engine}')
        self.client.open()

    def before_read(self, channel):
        # 判断当前talos任务数是否超出阈值，是则等待指定时间后再次判断，否则执行
        from queqiao.dba.models import Execution
        from queqiao.conf.system import SystemConfig
        reading_executions = Execution.get(status=ExecutionStatus.READING.value)
        talos_queued_max = int(SystemConfig.read('TALOS_QUEUED_MAX'))
        talos_queued_wait_time = int(SystemConfig.read('TALOS_QUEUED_WAIT_TIME'))
        self.logger.info(
            f"talos_queued_max: {talos_queued_max}, talos_queued_wait_time: {talos_queued_wait_time}, reading_executions: {len(reading_executions)}")
        while len(reading_executions) > talos_queued_max:
            sleep_time = talos_queued_wait_time + random.randint(-10, 10)
            self.logger.info(
                f'current has {len(reading_executions)} reading executions, block talos source task and wait {sleep_time}s...')
            time.sleep(sleep_time)
            reading_executions = Execution.get(status=ExecutionStatus.READING.value)
