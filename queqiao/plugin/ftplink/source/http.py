"""
Author: xia<PERSON><PERSON>
Date: 2023/1/13
Email: <EMAIL>
Host: xiaohei.info
"""

from queqiao.plugin.ftplink.source.file import FileSource
from queqiao.util.comm import strutil
from queqiao.util.conn.http import HttpClient

component = {
    'comment': 'HTTP远程文件',
    'configs': {
        'url': {'name_cn': '请求地址', 'type': 'string', 'default': None, 'required': 1,
                'demo': 'http://xxx.com/xxx.csv', 'comment': '访问远程http文件'},
        'params': {'name_cn': '请求参数', 'type': 'string', 'default': None, 'required': 0,
                   'demo': '{"key":"value"}', 'comment': 'http请求参数,json格式'},
        'target_sep': {'name_cn': '列分隔符', 'type': 'string', 'default': 'special', 'required': 1,
                       'demo': ',', 'comment': '文件中的列分隔符，支持多位字符，不可见字符（\\x01）请使用变量special'},
        'has_header': {'name_cn': '文件头', 'type': 'boolean', 'default': '0', 'required': 0,
                       'demo': None, 'comment': '文件是否存在文件头，如有则去除'},
        'table_cols': {'name_cn': '字段列表', 'type': 'string', 'default': None, 'required': 0,
                       'demo': 'col1,col2,col3', 'comment': '文件数据对应的字段列表，逗号隔开，用于标识元数据，与文件头同时存在时取当前值'}
    }
}


class HttpSource(FileSource):
    def _get_file(self):
        params = strutil.hocon_loads(self.config.params) if self.config.params else None
        HttpClient.download(self.config.url, self.local_filepath, params=params)
