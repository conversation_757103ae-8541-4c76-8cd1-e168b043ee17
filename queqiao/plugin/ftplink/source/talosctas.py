"""
Author: xiaohei
Date: 2023/9/11
Email: <EMAIL>
Host: xiaohei.info
"""
import json

from queqiao.plugin.ftplink.source import FtplinkSource
from queqiao.util.mt.talos import TalosClient

component = {
    'comment': 'talos查询建表',
    'configs': {
        'sql': {'name_cn': 'sql', 'type': 'text', 'default': None, 'required': 1,
                'demo': "select * from mart_fspinno_queqiao.firpos_custlist where partition_date='${now.delta(1).date}'",
                'comment': '可执行的sql语句，请务必确认测试通过可执行'},
        'table': {'name_cn': '表名', 'type': 'text', 'default': None, 'required': 1,
                  'demo': "tianwen_tmp_72dbfcfcea8d42a6860fefc586f7414c",
                  'comment': '写入表名，库名固定为mart_fsp_security_safetmp金服临时库'}
    }
}


class TalosCtas(FtplinkSource):

    def __init__(self, source_config):
        super().__init__(source_config)
        self.connect = json.loads(source_config.dsn.connect)
        # 从配置中获取账号信息
        username = self.connect['uname']
        password = self.connect['passwd']
        engine = self.connect['engine']
        self.client = TalosClient(username, password, engine, logger=self.logger)
        self.logger.info(f'init talos client with uname: {username}, engine: {engine}')
        self.client.open()

    def _save_to_local(self):
        ctas_sql = f'create table mart_fsp_security_safetmp.{self.config.table} as {self.config.sql}'
        query_info = self.client.exec(ctas_sql)
        with open(self.local_filepath, 'w') as f:
            f.write(json.dumps(query_info))
