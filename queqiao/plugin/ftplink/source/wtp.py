"""
Author: xia<PERSON><PERSON>
Date: 2022/5/24
Email: <EMAIL>
Host: xiaohei.info
"""
import copy

from queqiao.plugin.ftplink.source import ftp
from queqiao.plugin.ftplink.source.ftp import FtpSource
from queqiao.util.comm import osutil

component = copy.deepcopy(ftp.component)


class WtpSource(FtpSource):

    def _save_to_local(self):
        super()._save_to_local()
        # 特殊文件格式处理
        # 浦发银行，去除文件最后两行无用信息
        # deal_cmd = f'sed -i "$(( $(wc -l < {self.local_filepath})-1+1 )),$ d" {self.local_filepath}'
        # osutil.call(deal_cmd)
        # 删除文件最后两行, 上面的命令在最后两行连续都是\n结尾时会有问题
        deal_cmd = f"sed -i '$ d' {self.local_filepath} && sed -i '$ d' {self.local_filepath}"
        osutil.call(deal_cmd)
        # 去除文件最后一个分隔符，浦发文件每条数据末也拼接了分隔符
        del_sep_cmd = f"sed -i 's/.\\{{{len(self._col_sep)}\\}}$//g' {self.local_filepath}"
        osutil.call(del_sep_cmd)

    def _get_ctime(self):
        md5_info = self.client.stat(self.check_file)
        self.logger.info(f'check file {self.check_file} md5_info: {md5_info}')
        return md5_info
