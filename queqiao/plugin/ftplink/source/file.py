"""
Author: xia<PERSON><PERSON>
Date: 2022/4/29
Email: <EMAIL>
Host: xiaohei.info
"""
from queqiao.conf.errors import InternalException
from queqiao.plugin.ftplink.source import FtplinkSource
from queqiao.util.comm import osutil

component = {
    'comment': '上传文件',
    'configs': {
        'file': {'name_cn': '上传本地文件', 'type': 'file', 'default': None, 'required': 1,
                 'demo': None, 'comment': '本地的txt/csv/excel等文件'},
        'target_sep': {'name_cn': '列分隔符', 'type': 'string', 'default': 'special', 'required': 1,
                       'demo': ',', 'comment': '文件中的列分隔符，支持多位字符，不可见字符（\\x01）请使用变量special'},
        'has_header': {'name_cn': '文件头', 'type': 'boolean', 'default': '0', 'required': 0,
                       'demo': None, 'comment': '文件是否存在文件头，如有则去除'},
        'table_cols': {'name_cn': '字段列表', 'type': 'string', 'default': None, 'required': 0,
                       'demo': 'col1,col2,col3', 'comment': '文件数据对应的字段列表，逗号隔开，用于标识元数据，与文件头同时存在时取当前值'}
    }
}


class FileSource(FtplinkSource):

    def _deal_with_header(self):
        self.header = None
        if self.config.has_header is not None and bool(int(self.config.has_header)):
            self.header = osutil.remove_header(self.local_filepath)

    def _get_file(self):
        if '*' in self.config.file:
            ret_code, self.config.file = osutil.merge_files(self.config.file)
            if ret_code > 0:
                raise InternalException(f'merge file execute error!')
        self.logger.info(f'move origin file {self.config.file} to {self.local_filepath}')
        osutil.mv(self.config.file, self.local_filepath)

    def _save_to_local(self):
        self._get_file()
        self._deal_with_header()

    def _gen_schema(self):
        table_cols = None
        if self.config.table_cols:
            table_cols = self.config.table_cols.split(',')
        elif self.header:
            table_cols = self.header.split(self._col_sep)
        return [{'name': c, 'type': 'string', 'comment': ''} for c in table_cols] if table_cols else []
