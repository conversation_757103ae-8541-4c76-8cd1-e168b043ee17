"""
Author: xiaohei
Date: 2022/7/27
Email: <EMAIL>
Host: xiaohei.info
"""
import json

from queqiao.conf import Config
from queqiao.conf.errors import InternalException
from queqiao.conf.system import SystemConfig
from queqiao.dba.extend_model import Alarm
from queqiao.plugin.ftplink.source import FtplinkSource
from queqiao.util.comm import sqlutil, osutil


# 不可直接用
class BaseHiveSource(FtplinkSource):
    def __init__(self, source_config):
        super().__init__(source_config)
        self.client = None
        self.connect = json.loads(source_config.dsn.connect)

    def _save_to_local(self):
        self.logger.debug(f'config.sql: {self.config.sql}')
        config_dict = Config.to_dict(self.config)
        self.logger.debug(f'config dict: {config_dict}')
        self.sql = self.config.sql.format(**config_dict)
        query_tables = list(sqlutil.get_tables(self.sql))
        self.logger.debug(f'reformat sql: {self.sql}')
        self.logger.debug(f'get query tables from sql: {query_tables}')
        self.target_table = query_tables[0]
        self.logger.info(f'get target_table:{self.target_table} from sql: {self.sql}')

        self.logger.debug(f'submit sql with config: {self.config}')
        try:
            self.client.set_default_col_sep(self._col_sep)
            query_result = self.client.query(self.sql)
            self.logger.info(f'get query result: {query_result}')
        except Exception as why:
            raise InternalException(str(why))
        result_total = int(query_result['total']) if query_result['total'] else 0
        result_size = round(int(query_result['resultSize']) / 1024 / 1024 / 1024, 2)
        self.select_columns = query_result['columns']

        if (result_total > int(SystemConfig.read('ALARM_HIVE_DOWNLOAD_MAX_NUM')) or result_size > int(SystemConfig.read(
                'ALARM_HIVE_DOWNLOAD_MAX_GB'))):
            alarm_msg = f'result total {result_total} is great then ALARM_HIVE_DOWNLOAD_MAX_NUM: ' \
                f'{SystemConfig.read("ALARM_HIVE_DOWNLOAD_MAX_NUM")} or result size {result_size} is great then ' \
                f'ALARM_HIVE_DOWNLOAD_MAX_GB: {SystemConfig.read("ALARM_HIVE_DOWNLOAD_MAX_GB")}, trigger the alarm!'
            Alarm._info_pure(alarm_msg, SystemConfig.read('SYS_ADMIN'))
            self.logger.warn(alarm_msg)

        self.logger.info(f'get sql result file: {query_result["file_path"]}, move it to {self.local_filepath}')
        osutil.mv(query_result['file_path'], self.local_filepath)

    def _gen_schema(self):
        for col in self.select_columns:
            if 'comment' not in col:
                col['comment'] = ''
        self.logger.info(f'get schema: {self.select_columns}')
        return self.select_columns

    def __finish__(self):
        self.client.close()
