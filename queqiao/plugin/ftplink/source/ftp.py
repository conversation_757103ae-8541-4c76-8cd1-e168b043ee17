"""
Author: xiaohei
Date: 2022/5/24
Email: <EMAIL>
Host: xiaohei.info
"""
import csv
import json
import os
import platform
import time

from queqiao.conf.enums import CompressType
from queqiao.conf.errors import AlarmException, IllegalParamsException, \
    InternalException
from queqiao.dba.extend_model import Alarm
from queqiao.log.util import calc_elapsed
from queqiao.plugin.ftplink.source import FtplinkSource
from queqiao.util.comm import osutil, fileutil, strutil
from queqiao.util.comm.dtutil import timer
from queqiao.util.conn.ftp import RemoteFileServer

component = {
    'comment': 'ftp/sftp远程文件服务',
    'configs': {
        'file_name': {'name_cn': '文件名规则,支持匹配符,支持列表(逗号分隔)', 'type': 'string', 'default': None, 'required': 1,
                      'demo': 'meituan_firpos_custlist_${now.delta(1).datekey}.txt', 'comment': '目标文件名规则'},
        'file_path': {'name_cn': '文件路径', 'type': 'string', 'default': None, 'required': 1,
                      'demo': 'card/gd/${now.delta(1).datekey}',
                      'comment': '目标文件存放路径，相对路径从/one-sftp-xy-bank/queqiao后开始'},
        'ok_file': {'name_cn': '就绪文件规则', 'type': 'string', 'default': None, 'required': 0,
                    'demo': '.ok', 'comment': '就绪文件名，解析规则见: https://km.sankuai.com/page/**********'},
        'target_sep': {'name_cn': '列分隔符', 'type': 'string', 'default': ',', 'required': 1,
                       'demo': ',', 'comment': '文件中的列分隔符，支持多位字符，不可见字符（\\x01）请使用变量$sep01'},
        're_target_sep': {'name_cn': '重新替换列分隔符', 'type': 'string', 'default': None, 'required': 0,
                          'demo': None, 'comment': '重新替换文件中的原有列分隔符，支持多位字符，不可见字符（\\x01）请使用变量$sep01'},
        'row_sep_to': {'name_cn': '替换后的行分隔符', 'type': 'string', 'default': None,
                       'required': 0, 'demo': ',', 'comment': 'row_sep_to替换成row_sep_from行分隔符'},
        'row_sep_from': {'name_cn': '原行分隔符', 'type': 'string', 'default': None,
                         'required': 0, 'demo': ',', 'comment': 'row_sep_from替换成row_sep_to行分隔符'},
        'has_header': {'name_cn': '文件头', 'type': 'boolean', 'default': '0', 'required': 1,
                       'demo': None, 'comment': '文件是否存在文件头，入库时将去除文件头'},
        'table_cols': {'name_cn': '字段列表', 'type': 'string', 'default': None, 'required': 0,
                       'demo': 'col1,col2,col3',
                       'comment': '文件数据对应的字段列表，逗号隔开，与文件头配置的关系见: https://km.sankuai.com/page/**********'},
        'compress_type': {'name_cn': '压缩类型', 'type': 'set', 'default': 'none', 'required': 1,
                          'demo': 'none,zip,gzip,tar.gz', 'comment': '目标文件的压缩类型，none为普通文本文件'},
        'compress_passwd': {'name_cn': '压缩密码', 'type': 'string', 'default': None, 'required': 0,
                            'demo': None, 'comment': '若压缩包有加密则填入解压密码'},
        'compress_with_ok_file': {'name_cn': '和标识文件一起打包后压缩数据类型', 'type': 'boolean', 'default': None,
                                  'required': 0,'demo': None, 'comment': '是否和标识文件一起打包'},
        'compress_merge_suffix': {'name_cn': '文件合并的后缀', 'type': 'string', 'default': None,
                                  'required': 0, 'demo': None, 'comment': '文件合并的后缀'},
        'lastest_ready_time': {'name_cn': '最晚就绪时间', 'type': 'string', 'default': 9999, 'required': 1,
                               'demo': '0530', 'comment': '超出此时间将触发延迟告警，小于0表示不告警'},
        'ftp_polling_sec': {'name_cn': '轮询间隔时间', 'type': 'int', 'default': '600', 'required': 1,
                            'demo': None, 'comment': '间隔性检测ftp文件是否就绪的时间，若无就绪文件且文件较大，建议使用长轮询时间避免读到中间态文件'},
        'max_waiting_hours': {'name_cn': '最大等待时长(小时)', 'type': 'int', 'default': '12', 'required': 1,
                              'demo': 12, 'comment': '轮询等待远程文件的最长时间（单位小时）'},
        'file_encode': {'name_cn': '目标文件编码', 'type': 'string', 'default': 'utf-8', 'required': 0,
                        'demo': 'utf-8,gbk', 'comment': '目标文件的编码格式，默认以utf8编码解析'},
        'conflict_sep': {'name_cn': '处理分隔符冲突', 'type': 'boolean', 'default': '0', 'required': 0,
                         'demo': None, 'comment': '处理源文件中的分隔符冲突问题，重写为不可见字符'}
    }
}


class FtpSource(FtplinkSource):
    def __init__(self, source_config):
        super().__init__(source_config)
        self.client = RemoteFileServer.get_connect(json.loads(source_config.dsn.connect), logger=self.logger)
        self.client.open()

    def _save_to_local(self):
        file_path = self.config.file_path if self.config.file_path else ''
        # 如果提供文件列表则取最后一个文件名最为目标文件,即提供文件列表时以默认最后一个文件为检测就绪文件,可以将最晚就绪的文件放在最后一个
        target_file = os.path.join(file_path, self.config.file_name if ',' not in self.config.file_name else
        self.config.file_name.split(',')[-1])
        self.config.ok_file = None if not self.config.ok_file else (
            self.config.file_name + self.config.ok_file if self.config.ok_file.startswith('.') else self.config.ok_file)
        target_okfile = os.path.join(file_path, self.config.ok_file if self.config.ok_file else '')

        compress_with_ok_file = bool(int(self.config.compress_with_ok_file)) if self.config.compress_with_ok_file else False
        self.check_file = target_file if not self.config.ok_file or compress_with_ok_file else target_okfile
        # 后续记录使用
        self.remote_file = target_file
        self.logger.info(f'use check_file: {self.check_file} for target_file: {target_file}')

        poll_time = timer.now().datetime
        while not self.client.exists(self.check_file):
            self._trigger_alarm(poll_time)
            self.logger.info(f'check file {self.check_file} does not exists, remote data is not ready, '
                             f'sleep {self.config.ftp_polling_sec} and wait...')
            time.sleep(int(self.config.ftp_polling_sec))

        target_files = self.client.match_files(self.config.file_path,
                                               self.config.file_name) if '*' in target_file else (
            [os.path.join(file_path, f) for f in
             self.config.file_name.split(',')] if ',' in self.config.file_name else [target_file])
        self.logger.info(f'get {len(target_files)} target files: {target_files}')
        # 根据ftp文件后缀处理
        self.local_filepath = self.local_filepath if self.config.file_name.endswith(
            '.txt') else self.local_filepath.replace('.txt', fileutil.get_filename_suffix(self.config.file_name))
        # 记录真实文件名
        real_local_filepath = self.local_filepath
        local_files = []
        # 处理所有匹配到的文件
        for idx in range(0, len(target_files)):
            target_file = target_files[idx]
            self.local_filepath = f'{real_local_filepath}.{idx}'
            with calc_elapsed('download from ftp', self.logger):
                self.client.download(target_file, self.local_filepath)
            self._deal_with_compress()
            self._deal_with_encode()
            self._deal_with_header()
            self._deal_with_conflict_sep()
            local_files.append(self.local_filepath)
            self.check_file = target_file if '*' in self.check_file else self.check_file
        self.logger.info(f'get {len(local_files)} local files: {local_files}')
        # 处理最后一行没有\n的场景, 防止多个文件合并出现，a文件末尾和b文件第一行相连的问题
        for idx in range(0, len(local_files)):
            sed_cmd = "sed -i " if platform.system() == 'Linux' else "sed -i ''"
            osutil.call(f"{sed_cmd} '$ s/$/\\n/' {local_files[idx]}") if osutil.calls(
                f"tail -n1 {local_files[idx]} | wc -l") == '0' else None
        if len(local_files) > 1:
            with calc_elapsed('merge sub local files', self.logger):
                for idx in range(1, len(local_files)):
                    osutil.call(f'cat {local_files[idx]} >> {local_files[0]}')
                    osutil.rm(local_files[idx])
        # 合并文件以.0结尾则为非压缩文件，使用原有的local_filepath
        # 不以.0结果则为解压缩后的文件名，以此文件后缀为准
        if not local_files[0].endswith('.0'):
            real_local_filepath = real_local_filepath.replace(fileutil.get_filename_suffix(real_local_filepath),
                                                              fileutil.get_filename_suffix(local_files[0]))
        self.logger.info(f'resave {local_files[0]} to {real_local_filepath}')
        osutil.mv(local_files[0], real_local_filepath)
        self.local_filepath = real_local_filepath
        self._check_ok_file(target_okfile)

        if not self.config.table_cols:
            raise IllegalParamsException(f'table cols not found, please set header or table_cols')

    def _check_ok_file(self, target_okfile):
        if self.config.ok_file:
            local_okfilepath = f'{self.local_filepath}.ok'
            self.client.download(target_okfile, local_okfilepath)
            # 检查元数据
            with open(local_okfilepath, 'r') as ok_file:
                ok_content = ok_file.read()
                try:
                    json_content = json.loads(ok_content)
                except Exception as why:
                    self.logger.warn(f'ok content: {ok_content} ,it may not a json string, exception: {why}')
                    json_content = {'ok_content': ok_content}

                def check_meta(meta_type, local_meta_value):
                    if meta_type in json_content and json_content[meta_type] != local_meta_value:
                        Alarm.alarm(alarm_content=f'{meta_type} check failed, remote file {meta_type}: '
                                                  f'{json_content[meta_type]}, '
                                                  f'local file {meta_type}: {local_meta_value}',
                                    execution_id=self.config.execution.id)

                check_meta('md5', strutil.md5_file(self.local_filepath))
                check_meta('bytes', osutil.bytes(self.local_filepath))
                check_meta('row_cnt', osutil.wc(self.local_filepath))
                check_meta('col_cnt', osutil.nf(self.local_filepath, self._col_sep))
                self.ok_content = json_content

    def _trigger_alarm(self, poll_time):
        curr_hour = timer.now().hourmin
        if int(curr_hour) >= int(self.config.lastest_ready_time):
            Alarm.alarm(
                alarm_content=f'check file {self.check_file} does not exists at {curr_hour}, please check remote file',
                execution_id=self.config.execution.id)
        if timer.time_diff(timer.now().datetime, poll_time) >= float(self.config.max_waiting_hours) * 3600:
            raise AlarmException(
                f'check file {self.check_file} does not exists at {curr_hour}, already waiting '
                f'for {self.config.max_waiting_hours} hours, error exit')

    def _deal_with_compress(self):
        with calc_elapsed('deal with compress', self.logger):
            if self.config.compress_type and self.config.compress_type != CompressType.NONE.value:
                self.logger.info('found zip file, start unzip file')
                try:
                    self.local_filepath = fileutil.decompress(self.local_filepath, self.config.compress_type,
                                                              self.config.compress_passwd,
                                                              self.config.compress_merge_suffix)
                    self.logger.info(f'decompress changed local_filepath to {self.local_filepath}')
                except Exception as why:
                    raise InternalException(str(why))

    def _deal_with_encode(self):
        with calc_elapsed('deal with encode', self.logger):
            if self.config.file_encode and self.config.file_encode not in ['utf8', 'utf-8']:
                ret = fileutil.reset_encode(self.local_filepath, self.config.file_encode)
                self.logger.info(f'encode finished with code: {ret}')
                # if ret > 0:
                #     raise InternalException(f'reset file encode failed!')

    def _deal_with_header(self):
        # 有has_header则去除第一行，根据是否有table_cols决定是否使用表头作为字段（可能存在中文表头的问题无法使用故以table_cols为准）
        # 不指定table_cols时取表头，有table_cols时直接使用
        with calc_elapsed('deal with header', self.logger):
            if self.config.has_header is not None and bool(int(self.config.has_header)):
                header_nf = osutil.nf(self.local_filepath, self._col_sep)
                header = osutil.remove_header(self.local_filepath)
                # 列数是否符合
                line1_nf = osutil.nf(self.local_filepath, self._col_sep)
                if not self.config.table_cols and header_nf == line1_nf:
                    self.config.table_cols = ','.join(list(filter(None, header.split(self._col_sep))))

    # 使用conflict_sep后列分隔符将会被替换为不可见字符，需要在sink中设置target_sep为special才能正常解析
    # 使用re_target_sep后列分隔符将会被替换为re_target_sep，需要在sink中设置target_sep为re_target_sep才能正常解析
    def _deal_with_conflict_sep(self):
        with calc_elapsed('deal with conflict_sep and re_target_sep', self.logger):
            special_sep = '\x01'
            if self.config.re_target_sep:
                re_target_sep = self.config.re_target_sep.replace("\\t", "\t").replace("\\x01", "\x01")
            elif self.config.conflict_sep and bool(int(self.config.conflict_sep)):
                re_target_sep = special_sep
            else:
                re_target_sep = None
            if re_target_sep:
                in_path = self.local_filepath
                out_path = f'{self.local_filepath}.reset'
                self.logger.debug(
                    f'rewrite target_sep from {self._col_sep} to {re_target_sep}, in_path: {in_path}, out_path: {out_path}')
                if len(re_target_sep) > 1:
                # 如果新的分隔符长度大于1，则直接使用字符串替换方法
                    with open(in_path, 'r') as infile, open(out_path, 'w') as outfile:
                        content = infile.read()
                        content = content.replace(self._col_sep, re_target_sep)
                        outfile.write(content)
                else:
                    with open(in_path, 'r') as infile, open(out_path, 'w') as outfile:
                        reader = csv.reader(infile, delimiter=self._col_sep)
                        writer = csv.writer(outfile, delimiter=re_target_sep)
                        for row in reader:
                            writer.writerow(row)
                self._col_sep = re_target_sep
                osutil.rm(in_path)
                osutil.mv(out_path, in_path)

    # ftp source的ctime统一使用check_file的时间
    # 部分无法获取远程文件信息的自行实现
    def _get_ctime(self):
        file_stat = self.client.stat(self.check_file)
        return file_stat.st_mtime

    def _get_ok_dict(self):
        ok_dict = super()._get_ok_dict()
        table_cols = []
        if self.config.ok_file:
            if 'table_schema' in self.ok_content:
                table_cols = self.ok_content.pop('table_schema')
            ok_dict.update(self.ok_content)
        if not table_cols:
            table_cols.extend(
                [{'name': col, 'type': 'string', 'comment': ''} for col in self.config.table_cols.split(",")])

        ok_dict['table_schema'] = table_cols
        ok_dict['remote_file'] = self.remote_file
        ok_dict['ctime'] = self._get_ctime()
        return ok_dict

    def __finish___(self):
        self.client.close()
