"""
Author: xiaohei
Date: 2022/4/29
Email: <EMAIL>
Host: xiaohei.info
"""

from queqiao.plugin.ftplink.source.basehive import BaseHiveSource
from queqiao.util.conn.mysql import MySQLClient

component = {
    'comment': 'mysql数据库',
    'configs': {
        'sql': {'name_cn': 'sql', 'type': 'text', 'default': None, 'required': 1,
                'demo': "select * from mart_fspinno_queqiao.firpos_custlist where partition_date='${now.delta(1).date}'",
                'comment': '可执行的sql语句，请务必确认测试通过可执行'},
        'ip': {'name_cn': 'mysql主机地址', 'type': 'string', 'default': None, 'required': 0,
                     'demo': None, 'comment': '指定将覆盖默认的dsn配置'},
        'port': {'name_cn': 'mysql主机端口', 'type': 'int', 'default': None, 'required': 0,
                     'demo': None, 'comment': '指定将覆盖默认的dsn配置'},
        'username': {'name_cn': 'mysql用户名', 'type': 'string', 'default': None, 'required': 0,
                     'demo': None, 'comment': '指定将覆盖默认的dsn配置'},
        'password': {'name_cn': 'mysql密码', 'type': 'sstring', 'default': None, 'required': 0,
                     'demo': None, 'comment': '个人账号对应的密码'},
        'db': {'name_cn': '目标库', 'type': 'string', 'default': None, 'required': 0,
               'demo': 'onesql/hive/spark', 'comment': '连接的目标数据库'}
    }
}


class MysqlSource(BaseHiveSource):
    def __init__(self, source_config):
        super().__init__(source_config)
        ip = self.connect['ip'] if not self.config.ip else self.config.ip
        port = self.connect['port'] if not self.config.port else self.config.port
        username = self.connect['username'] if not self.config.username else self.config.username
        password = self.connect['password'] if not self.config.password else self.config.password
        database = self.connect['db'] if not self.config.db else self.config.db
        self.client = MySQLClient(host=ip, port=port, user=username, password=password,
                                  database=database)
        self.client.open()
