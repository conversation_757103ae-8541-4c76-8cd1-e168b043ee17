"""
Author: xiaohei
Date: 2022/5/24
Email: <EMAIL>
Host: xiaohei.info
"""
from queqiao.conf.errors import IllegalParamsException
from queqiao.conf.system import SystemConfig
from queqiao.plugin.ftplink.source.hivesql import HivesqlSource
from queqiao.util.comm.dtutil import timer

component = {
    'comment': 'hive集群（配置式）',
    'configs': {
        'table_name': {'name_cn': '目标表名', 'type': 'string', 'default': None, 'required': 1,
                       'demo': 'mart_fspinno_queqiao.firpos_custlist', 'comment': '数据导出的目标表'},
        'table_cols': {'name_cn': '读取字段', 'type': 'string', 'default': None, 'required': 0,
                       'demo': 'col1,col2,col3', 'comment': '默认读取全部字段'},
        'data_range': {'name_cn': '同步范围', 'type': 'set', 'default': 'full', 'required': 1,
                       'demo': 'full,partition', 'comment': 'full：全量数据、partition：分区数据'},
        'partition_key': {'name_cn': '分区键', 'type': 'string', 'default': 'partition_date',
                          'required': 0, 'demo': 'partition_date', 'comment': '指定分区键，默认使用partition_date'},
        'partition_value': {'name_cn': '分区值', 'type': 'date', 'default': None, 'required': 0,
                            'demo': None, 'comment': '选择分区数据范围（partition模式必选）'}
    }
}


class HiveSource(HivesqlSource):
    def __init__(self, source_config):
        super().__init__(source_config)
        self.db = self.connect['db']

    def _save_to_local(self):
        target_table = self.config.table_name
        target_db = self.db

        table_name = f'{target_db}.{target_table}'
        table_cols = self.config.table_cols if self.config.table_cols else '*'

        sql = [f'select {table_cols} from {table_name}']
        self.config.target_date = timer.now().datekey
        if self.config.data_range == 'partition':
            partition_key = self.config.partition_key if self.config.partition_key else SystemConfig.read(
                'DEFAULT_PARTITION_KEY')
            if not self.config.partition_value:
                raise IllegalParamsException(f'partitioned hive source must have a partition value')
            partition_values = [v for v in self.config.partition_value.split(',') if v]
            if len(partition_values) < 1:
                raise IllegalParamsException(f'partitioned hive source must have a least one partition value')
            if len(partition_values) == 1:
                sql.append(f"where {partition_key}='{partition_values[0]}'")
            else:
                sql.append(
                    f"where {partition_key} >= '{partition_values[0]}' and {partition_key} <= '{partition_values[1]}'")
                # target_date默认保存在第一个分区
            self.config.target_date = partition_values[0].replace('-', '')
        self.config.sql = ' '.join(sql)
        self.logger.info(f'generate hive sql: {self.config.sql}')
        super()._save_to_local()
