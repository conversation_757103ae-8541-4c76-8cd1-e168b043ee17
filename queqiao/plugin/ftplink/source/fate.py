"""
Author: xiaohei
Date: 2022/9/12
Email: <EMAIL>
Host: xiaohei.info
"""
import json
import os

from instance.default import TMP_FOLDER
from queqiao.conf.errors import ExecuteFailedException
from queqiao.plugin.ftplink.source import FtplinkSource
from queqiao.util.comm import osutil, fileutil
from queqiao.util.conn.fate import FateClient
import pandas as pd

component = {
    'comment': '读取fate',
    'configs': {
        'role': {'name_cn': 'fate服务角色', 'type': 'string', 'default': 'guest', 'required': 1,
                 'demo': 'guest,host', 'comment': 'fate server角色名'},
        'party_id': {'name_cn': 'PartyId', 'type': 'string', 'default': None, 'required': 1,
                     'demo': '10005', 'comment': 'Party ID（-p）'},
        'job_id': {'name_cn': '作业id', 'type': 'string', 'default': '0', 'required': 0,
                   'demo': '202209110048528627860', 'comment': 'fate提交任务后返回的jobid，列表请使用逗号隔开'},
        'cols': {'name_cn': '列名', 'type': 'text', 'default': None, 'required': 0,
                 'demo': 'hash_id,score', 'comment': '需要读取的列名，置空为全部'},
        'component_name': {'name_cn': '组件名', 'type': 'string', 'default': None, 'required': 1,
                           'demo': 'hetero_feature_binning_0', 'comment': '指定组件名（-cpn）'}
    }
}


class FateSource(FtplinkSource):
    def __init__(self, source_config):
        super().__init__(source_config)
        connect = json.loads(source_config.dsn.connect)
        self.client = FateClient(connect['ip'], connect['port'], logger=self.logger)
        self.client.open()

    def _wait4finish(self, job_id):
        self.logger.info(f'wait {job_id} to finish and get task output')
        status = self.client.wait_job(job_id, role=self.config.role)
        if status == 'failed':
            self.logger.info(f'job {job_id} execute failed, retry once')
            self.client.job_rerun(job_id)
            status = self.client.wait_job(job_id, role=self.config.role)
            self.logger.info(f'rerun job {job_id} status {status}')
            if status == 'failed':
                raise ExecuteFailedException(f'job {job_id} execute failed, please check in fate board')

    def _save_to_local(self):
        job_ids = self.config.job_id.split(',')
        self.logger.info(f'get {len((job_ids))} jobs, detail: {job_ids}')
        data_files = []
        curr = 1
        total_cnt = len(job_ids)
        for job_id in job_ids:
            self._wait4finish(job_id)
            save_path = f'{TMP_FOLDER}/fatesource'
            save_path = self.client.output_data(job_id, self.config.role, self.config.party_id,
                                                self.config.component_name, save_path)
            list_files = osutil.list_files(save_path, suffix='.csv')
            if not list_files:
                raise ExecuteFailedException(f'can not fund csv result data file in {save_path}')
            data_file = os.path.join(save_path, list_files[0])
            # meta_file = os.path.join(saved_path, f'{self.config.data_name}.meta')
            df = pd.read_csv(data_file, index_col=False)
            if self.config.cols:
                # pandas处理
                self.logger.info(f'read file to dataframe and get columns: {self.config.cols}')
                self.logger.info(f'dataframe shape: {df.shape}')
                df = df[self.config.cols.split(',')]
                self.logger.info(f'dataframe sample: {df.head()}')
                # 统一输出分隔符为不可见字符
            df.to_csv(data_file, index=False, header=False, sep='\x01')
            self.config.cols = ','.join(df.columns)
            data_files.append(data_file)
            self.logger.info(f'task progress [{curr}/{total_cnt}]')
            curr += 1
        for i in range(1, len(data_files)):
            self.logger.info(f'merge index {i} file {data_files[i]} to {data_files[0]}')
            osutil.call(f'cat {data_files[i]} >> {data_files[0]}')
            osutil.rm(data_files[i])
        self.logger.info(f'get final data file {data_files[0]} and move to local_filepath: {self.local_filepath}')
        osutil.mv(data_files[0], self.local_filepath)

    def _gen_schema(self):
        table_schema = [{'name': c, 'type': 'string', 'comment': ''} for c in self.config.cols.split(',')]
        self.logger.info(f'get schema: {table_schema}')
        return table_schema
