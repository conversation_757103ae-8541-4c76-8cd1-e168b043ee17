"""
Author: xiaohei
Date: 2022/4/28
Email: <EMAIL>
Host: xiaohei.info
"""
import os
from abc import abstractmethod

from instance.default import LOCAL_DATA_FOLDER
from queqiao.conf.system import SystemConfig
from queqiao.core.engine.base import Source
from queqiao.dba.models import Org
from queqiao.util.comm import osutil, strutil, mathutil, fileutil


class FtplinkSource(Source):
    # 固定输出参数
    def read(self, channel):
        target_sep = self.config.target_sep if hasattr(self.config, 'target_sep') and \
                                               self.config.target_sep else SystemConfig.read('LOCALFILE_DEFAULT_SEP')
        self._col_sep = '\x01' if target_sep == '\\u0001' or target_sep == 'special' or target_sep == '\\x01' else target_sep
        self._row_sep_to = self.config.row_sep_to if hasattr(self.config, 'row_sep_to') and self.config.row_sep_to else None
        self._row_sep_from = self.config.row_sep_from if hasattr(self.config, 'row_sep_from') and self.config.row_sep_from else None
        local_data_folder = LOCAL_DATA_FOLDER[mathutil.mod(self.config.execution.id, LOCAL_DATA_FOLDER)] \
            if isinstance(LOCAL_DATA_FOLDER, list) else LOCAL_DATA_FOLDER
        target_date = self.config.current.replace('-', '')
        org = Org.get_one(id=self.config.execution.source_org_id)
        self.local_savedir = os.path.join(local_data_folder, org.pyname, target_date)
        self.logger.debug(f'target_date: {target_date}, local_savedir: {self.local_savedir}')
        if not osutil.exists(self.local_savedir):
            osutil.mkdir(self.local_savedir)
        # 避免相同dsn下不同路径的相同文件名覆盖问题
        self.local_filepath = os.path.join(self.local_savedir,
                                           f'{self.config.execution.task_name}_{self.config.execution.id}_{target_date}.txt')
        if osutil.exists(self.local_filepath):
            osutil.rm(self.local_filepath)
        self.logger.info(f'start read from source and save to local: {self.local_filepath}')
        self._save_to_local()
        self.logger.info(f'save source file to local: {self.local_filepath}')
        if self._row_sep_from is not None and self._row_sep_to is not None:
            self.logger.info(f'file md5 before row sep replacement: {strutil.md5_file(self.local_filepath)}')
            fileutil.reset_row_sep(self.local_filepath, self._row_sep_from, self._row_sep_to)
            self.logger.info(f'row sep replacement finish: {self.local_filepath}')
        channel.set_pipeline_param(local_filepath=self.local_filepath)
        ok_dict = self._get_ok_dict()
        if ok_dict:
            channel.set_pipeline_param(ok_dict=ok_dict)
            self.logger.debug(f'get ok dict: {ok_dict}')
        self.logger.info(f'do finish')
        self.__finish___()

    @abstractmethod
    def _save_to_local(self):
        pass

    def _get_ok_dict(self):
        self.logger.info('getting file meta and save to ok dict...')
        target_sep = self._col_sep
        bytes = osutil.bytes(self.local_filepath)
        # 生成文件元数据信息:bytes,md5,row_cnt,col_cnt,ctime
        local_meta = fileutil.get_meta(self.local_filepath, target_sep) if bytes < int(
            SystemConfig.read('CHECK_META_MAX_BYTES', default=0)) else fileutil.get_meta_simple(self.local_filepath)
        file_meta = {
            'bytes': bytes,
            'md5': strutil.md5_file(self.local_filepath),
            'col_cnt': osutil.nf(self.local_filepath, target_sep),
            'ctime': osutil.ctime(self.local_filepath),
            'local_file': self.local_filepath,
            'source': self.__class__.__name__.replace('Source', '').lower(),
            'table_schema': self._gen_schema(),
            'target_sep': target_sep
        }
        file_meta.update(local_meta)
        file_meta['pcol_distlen_cnt'] = 0 if file_meta['row_num'] == 0 else 1
        return file_meta

    def _gen_schema(self):
        '''
        [
            {'name': 'col1', 'type': 'string', 'comment': ''}
        ]
        :return:
        '''
        return []

    def __finish___(self):
        pass
