"""
Author: xiaohei
Date: 2022/9/14
Email: <EMAIL>
Host: xiaohei.info
"""
import copy
import json
import os

from instance.default import TMP_FOLDER
from queqiao.plugin.ftplink.source import fate
from queqiao.plugin.ftplink.source.fate import FateSource
from queqiao.util.comm import osutil, strutil

component = copy.deepcopy(fate.component)
component['configs']['bucket_cnt'] = {'name_cn': '数据分桶个数', 'type': 'int', 'default': '0', 'required': 1,
                                      'demo': '0,50', 'comment': '数据分桶个数，0为不分桶'}
component['configs']['job_conf'] = {'name_cn': '任务配置', 'type': 'text', 'default': None, 'required': 1,
                                    'demo': None, 'comment': 'fate任务执行的配置内容（-c），内置变量bucket_id'}
component['configs']['job_dsl'] = {'name_cn': 'DSL配置', 'type': 'text', 'default': None, 'required': 1,
                                   'demo': None, 'comment': 'fate任务执行的DSL内容（-d）'}
component['comment'] = '读取fate(执行任务)'


class FateTaskSource(FateSource):

    def __submit_task(self):
        task_results = {}
        bucket_cnt = int(self.config.bucket_cnt)
        self.logger.info(f'bucket count: {bucket_cnt}, submit task to fate server')
        if bucket_cnt <= 1:
            task_results[-1] = self.client.job_submit(self.config.job_conf, self.config.job_dsl)
        else:
            for bucket_id in range(0, bucket_cnt):
                bucket_id = str(bucket_id)
                self.logger.info(f'bucket id: {bucket_id}')
                job_conf = self.config.job_conf.replace('{bucket_id}', bucket_id)
                job_dsl = self.config.job_dsl.replace('{bucket_id}', bucket_id)
                task_results[bucket_id] = self.client.job_submit(job_conf, job_dsl)
        self.logger.info(f'{len(task_results)} task submited')
        return task_results

    def _save_to_local(self):
        task_results = self.__submit_task()
        task_job_ids = [task['jobId'] for task in list(task_results.values())]
        self.logger.info(f'get {len(task_job_ids)} job id, detail: {task_job_ids}')
        self.config.job_id = ','.join(task_job_ids)
        super()._save_to_local()
