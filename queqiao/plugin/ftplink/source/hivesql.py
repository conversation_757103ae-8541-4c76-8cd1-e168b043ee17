"""
Author: xiaohei
Date: 2022/4/29
Email: <EMAIL>
Host: xiaohei.info
"""

from queqiao.plugin.ftplink.source.basehive import BaseHiveSource
from queqiao.util.hadoop.pyhive import PyhiveClient

component = {
    'comment': 'hive集群（自定义sql）',
    'configs': {
        'sql': {'name_cn': 'sql', 'type': 'text', 'default': None, 'required': 1,
                'demo': "select * from mart_fspinno_queqiao.firpos_custlist where partition_date='${now.delta(1).date}'",
                'comment': '可执行的sql语句，请务必确认测试通过可执行'}
    }
}


class HivesqlSource(BaseHiveSource):
    def __init__(self, source_config):
        super().__init__(source_config)
        krb_host = self.connect['krb_host'] if 'krb_host' in self.connect else None
        self.client = PyhiveClient(self.connect['pyhive_host'], self.connect['pyhive_port'],
                                   self.connect['kerberos_service_name'], self.connect['kerberos_user'],
                                   self.connect['beeline_u'],
                                   self.connect['keytab'], krb_host=krb_host, logger=self.logger)
        self.client.open()
