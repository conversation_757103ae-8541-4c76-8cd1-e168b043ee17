"""
Author: xiaohei
Date: 2022/5/25
Email: <EMAIL>
Host: xiaohei.info
"""
import copy

from queqiao.conf.errors import SqlExecuteFailedException
from queqiao.plugin.ftplink.sink import hive
from queqiao.plugin.ftplink.sink.basehive import BaseHiveSink
from queqiao.util.hadoop.spark import SparkCmdClient

component = copy.deepcopy(hive.component)
component['configs']['msck_table'] = {'name_cn': '是否修复表结构', 'type': 'int', 'default': '0', 'required': 1,
                                           'demo': '0', 'comment': '是否修改表结构数据，大量分区情况下可能会导致与 hive metastore 通讯阻塞，对于需要更新结构列信息的场景需要设置为 1，其他场景可以使用默认的 0'}
component['comment'] = '美团hive集群'


class MthiveSink(BaseHiveSink):
    def __init__(self, sink_config):
        super().__init__(sink_config)
        self.client = SparkCmdClient(logger=self.logger)
        yarn_app_name = f'{self.__class__.__name__}:{sink_config.execution.execution_name}' \
            f':{self.target_db}.{self.target_table}'
        self.client.set_yarn_mode(yarn_app_name)
        self.client.set_exec_cache()
        self.client.open()
        self.logger.info(f'init mt spark client, yarn app name: {yarn_app_name}')

    def _write(self, local_filepath, ok_dict):
        super()._write(local_filepath, ok_dict)
        rcode = self.client.exec('', async_=True)
        if rcode > 0:
            raise SqlExecuteFailedException(
                f'spark-sql execute failed, see /tmp/queqiao_sparksql_*.err for more information')
