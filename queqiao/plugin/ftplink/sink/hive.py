"""
Author: xia<PERSON>ei
Date: 2022/5/20
Email: <EMAIL>
Host: xiaohei.info
"""

from queqiao.plugin.ftplink.sink.basehive import BaseHiveSink
from queqiao.util.hadoop.pyhive import PyhiveClient

component = {
    'comment': 'hive集群',
    'configs': {
        'table_name': {'name_cn': '入库表名', 'type': 'string', 'default': None, 'required': 1,
                       'demo': 'firpos_custlist', 'comment': '导入目标表，请勿输入库名（库名根据dsn获取）'},
        'table_type': {'name_cn': '入库表类型', 'type': 'set', 'default': 'full', 'required': 1,
                       'demo': 'full,partition', 'comment': 'full：全量表、partition：日分区表'},
        'partition_key': {'name_cn': '分区字段', 'type': 'string', 'default': None, 'required': 0,
                          'demo': 'partition_date',
                          'comment': '表类型为分区表时生效，将会使用指定字段作为动态分区值写入分区表，若无此值则将默认以partition_date为key，当前日期为value写入分区表'},
        'partition_value': {'name_cn': '分区值', 'type': 'string', 'default': None, 'required': 0,
                            'demo': '2023-11-27',
                            'comment': '表类型为分区表时生效，将当前数据表写入指定分区中，若无指定则以partition_key规则为准'},
        'custom_cols': {'name_cn': '自定义字段信息', 'type': 'string', 'default': None, 'required': 0,
                        'demo': 'col1:A,col2:B',
                        'comment': '用户自定义的字段信息，将作为附加字段写入当前表中'},
        'write_mode': {'name_cn': '写入模式', 'type': 'set', 'default': 'overwrite', 'required': 1,
                       'demo': 'overwrite,append', 'comment': 'overwrite：覆盖写入、append：追加写入'},
        'target_sep': {'name_cn': '列分隔符', 'type': 'string', 'default': ',', 'required': 1,
                       'demo': ',', 'comment': '文件中的列分隔符，支持多位字符，不可见字符（\\x01）请使用变量$sep01'},
        # 前端根据是否调度任务设置93 or null
        'life_cycle': {'name_cn': '表生命周期', 'type': 'int', 'default': None, 'required': 0,
                       'demo': '93', 'comment': '表的生命周期属性标识'},
        'null_format': {'name_cn': '空值存储格式', 'type': 'string', 'default': None, 'required': 0,
                        'demo': 'NULL', 'comment': 'hive表底层空值存储格式，配置后可以通过is null过滤此值（默认为\\N）'},
        'disable_result_check': {'name_cn': '关闭落库结果检查', 'type': 'boolean', 'default': 1, 'required': 0,
                        'demo': '0/1', 'comment': '1为关闭（默认），关闭后将不进行落库后的数据校验（性能有一定提升）'},
        'table_description': {'name_cn': '表注释', 'type': 'string', 'default': None, 'required': 0,
                        'demo': '这是一个示例表', 'comment': '创建Hive表时的表级注释信息'}
    }
}


class HiveSink(BaseHiveSink):

    def __init__(self, sink_config):
        super().__init__(sink_config)
        # todo: dsn需要控制权限
        krb_host = self.connect['krb_host'] if 'krb_host' in self.connect else None
        self.client = PyhiveClient(self.connect['pyhive_host'], self.connect['pyhive_port'],
                                   self.connect['kerberos_service_name'], self.connect['kerberos_user'],
                                   self.connect['beeline_u'],
                                   self.connect['keytab'], krb_host=krb_host, logger=self.logger)
        self.client.open()
