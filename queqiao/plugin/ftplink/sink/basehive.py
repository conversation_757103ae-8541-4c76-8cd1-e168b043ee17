"""
Author: xiaohei
Date: 2022/7/28
Email: <EMAIL>
Host: xiaohei.info
"""

from kombu.utils import json

from queqiao.conf.enums import HiveTableType
from queqiao.conf.errors import SqlExecuteFailedException, CmdExecuteFailedException, IllegalParamsException, \
    InternalException
from queqiao.conf.system import SystemConfig
from queqiao.dba.extend_model import Alarm
from queqiao.dba.models import Org
from queqiao.plugin.ftplink.sink import FtplinkSink
from queqiao.util.comm import osutil, fileutil, strutil
from queqiao.util.comm.sqlutil import convert_mysql_to_hive_type

QUEQIAO_RESULT_FLAG = 'queqiao_result_flag'


# 不可直接用
class BaseHiveSink(FtplinkSink):
    def __init__(self, sink_config):
        super().__init__(sink_config)
        self.client = None
        self.connect = json.loads(sink_config.dsn.connect)
        self.default_hdfspath = self.connect['hdfs_path']
        self.target_db = self.connect['db']
        self.target_table = self.config.table_name
        self.logger.info(f'connect to hs2 with pyhive, default hdfs path: {self.default_hdfspath}, '
                         f'target db: {self.target_db}, target table: {self.target_table}')

    def _write(self, local_filepath, ok_dict):
        self.data_filepath = local_filepath
        self.data_filename = self.data_filepath.split('/')[-1]

        # 处理多位分隔符
        if len(self._col_sep) > 1:
            self.logger.info(f'target_sep for current task is {self._col_sep}, '
                             f'length great then 1, reset to specail char')
            ret = fileutil.reset_sep(self.data_filepath, self._col_sep)
            if ret > 0:
                raise InternalException(f'reset sep failed!')
            self._col_sep = '\x01'
            self._hive_sep = '\\u0001'
            self.logger.info('target_sep reseted')

        self.table_cols = {}
        if 'table_schema' not in ok_dict:
            raise IllegalParamsException(f'table_schema must in ok_dict, current ok_dict: {ok_dict}')
        table_schema = ok_dict['table_schema']
        for col in table_schema:
            col_name = strutil.check_special_chars_in_str(col['name'], '')
            self.table_cols[col_name] = (convert_mysql_to_hive_type(col['type']), col['comment'])
        # 注入用户指定的字段
        if self.config.custom_cols:
            self.config.custom_cols = strutil.check_special_chars_in_str(self.config.custom_cols, '')
            self.config.custom_cols = strutil.hocon_loads(self.config.custom_cols)
            self.logger.info(f'get hocon_loads custom cols: {self.config.custom_cols}')
            for name in self.config.custom_cols.keys():
                col_name = strutil.check_special_chars_in_str(name, '')
                self.table_cols[col_name] = ('string', 'user-defind')

        self.is_partition_table = True if self.config.table_type == 'partition' else False
        # 1.非分区表没有分区值
        if self.config.table_type != 'partition':
            self.table_type = HiveTableType.FULL
            self.partition_key = None
            self.partition_value = None
        else:
            # 2. 分区表 & 没有指定分区键 & 没有指定分区值 -> 都使用默认
            if not self.config.partition_key and not self.config.partition_value:
                self.table_type = HiveTableType.PARTITION_WITHOUT_KV
                self.partition_key = SystemConfig.read('DEFAULT_PARTITION_KEY')
                self.partition_value = self.config.current
            # 3.分区表&指定了分区键&没有分区值的情况
            elif self.config.partition_key and not self.config.partition_value:
                self.partition_key = self.config.partition_key
                # 3.1 如果分区键不在字段列表中，则填充当前日期
                if self.config.partition_key not in self.table_cols:
                    self.table_type = HiveTableType.PARTITION_WITH_K_V_FROM_DEFAULT
                    self.partition_value = self.config.current
                # 3.2 如果分区键在字段列表中，则视为文件中携带分区值可以直接使用
                else:
                    self.table_type = HiveTableType.PARTITION_WITH_K_V_FROM_FILE
                    # 从文件中读取，可能有多个值
                    self.partition_value = None
            # 4.分区表 & 没有指定分区键 & 指定了分区值 -> 取默认分区键
            elif not self.config.partition_key and self.config.partition_value:
                self.table_type = HiveTableType.PARTITION_WITH_V_K_FROM_DEFAULT
                self.partition_key = SystemConfig.read('DEFAULT_PARTITION_KEY')
                self.partition_value = self.config.partition_value
            # 分区表& 指定了分区键 & 指定了分区值 -> 都使用指定参数
            else:
                self.table_type = HiveTableType.PARTITION_WITH_KV
                self.partition_key = self.config.partition_key
                self.partition_value = self.config.partition_value

            self.table_cols[self.partition_key] = ('string', '分区字段')
        self.logger.info(
            f"hive table type: {self.table_type}, partition_key: {self.partition_key}, partition_value: {self.partition_value}")

        # 避免相同dsn的不同文件+相同目标表 覆盖问题
        tmp_tablename = f'{self.target_table}_queqiao_tmp_{self.config.execution.id}'
        self.tmp_tablename = f'{self.target_db}.{tmp_tablename}'
        self.parquet_tablename = f'{self.target_db}.{self.target_table}'
        dw_hdfspath = f'{self.default_hdfspath}' if self.target_db in self.default_hdfspath else f'{self.default_hdfspath}/{self.target_db}.db'
        org = Org.get_one(id=self.config.execution.source_org_id)
        self.tmp_hdfspath = f'{dw_hdfspath}/{org.pyname}/{self.config.current.replace("-", "")}/{tmp_tablename}'
        self.parquet_hdfspath = f'{dw_hdfspath}/{self.target_table}'
        self.logger.info(f'tmp_hdfspath: {self.tmp_hdfspath}, parquet_hdfspath: {self.parquet_hdfspath}')

        self._step1_drop_tmp_table()
        self._step2_put_file2hdfs()
        self._step3_create_tmp_table()
        self._step4_create_parquet_table()
        self._step5_insert_parquet_from_tmp()
        self._step1_drop_tmp_table()

    def _check_import_result(self):
        if self.config.write_mode == 'append':
            self.logger.info(f'current write mode is [insert into], skip check import result')
            return
        # hive表最终数据量与ftp文件核对、1+NF列的最大最小值（是否为最新文件数据）、partition_date+指定字段长度去重（字段是否错位）
        cols = list(self.table_cols.keys())
        last_idx = len(cols) - 2 if self.is_partition_table else len(cols) - 1
        self.logger.info(f'get cols lastest index: {last_idx}')
        if last_idx < 0:
            self.logger.info('lastest col index is less then 0, skip check result')
            return
        col1, coln = cols[0], cols[last_idx]
        self.logger.debug(f'col1: {col1}, coln: {coln}')
        sql_list = [f'select count(1) as row_num,'
                    f'max({col1}) as col1_max,'
                    f'min({col1}) as col1_min,'
                    f'max({coln}) as coln_max,'
                    f'min({coln}) as coln_min,'
                    f'count(distinct(length({coln}))) as coln_len_num']
        if self.partition_key:
            sql_list.append(f',count(distinct(length({self.partition_key}))) as partition_len_num')
        sql_list.append(f'from {self.parquet_tablename}')

        if self.is_partition_table:
            if self.partition_value:
                partition_key_idx = len(cols)
                partition_values = [self.partition_value]
            else:
                partition_key_idx = cols.index(self.partition_key)
                self.logger.debug(f'get partition_key index: {partition_key_idx}')
                partition_values = fileutil.get_distinct_values(self.data_filepath, self._col_sep, partition_key_idx)
            partition_value = "'" + "','".join(partition_values) + "'"
            sql_list.append(f'where {self.partition_key} in ({partition_value})')

        check_sql = ' '.join(sql_list)
        self.client.set_default_col_sep(self._col_sep)
        query_info = self.client.query(check_sql)
        check_result = osutil.calls(f'cat {query_info["file_path"]}')
        self.logger.debug(f'get sql check result: {check_result}')
        check_result = check_result.split(self._col_sep)
        # 统一将NULL转换为空字符串方便后续对比判断
        check_result = [c if c != 'NULL' else '' for c in check_result]
        hive_results = {
            'row_num': int(check_result[0]),
            'col1_max': check_result[1],
            'col1_min': check_result[2],
            'coln_max': check_result[3],
            'coln_min': check_result[4],
            'coln_len_num': int(check_result[5]),
            'colv_len_num': int(check_result[5] if len(check_result) == 6 else check_result[6]),
        }

        # awk_cmd = f'export LC_COLLATE=POSIX;cat {self.data_filepath} | awk -F ' + f"'{self._col_sep}'"
        # 指定了partition_value且partition_key在最后一列的情况下取倒数第二列，其余情况取倒数第一列
        # 不使用-1/-2，计算文件元数据时不准确
        coln_offset = -1
        if self.is_partition_table:
            # 文件中存在分区值时，字段列表与文件字段列表长度一致
            coln_offset = -2
            if not self.partition_value:
                # partition_value可能在字段列表中的任意位置
                # 如果为最后一个字段，则取倒数第二个字段索引作为偏移
                # 其他情况则都取倒数第一个字段索引作为偏移
                coln_offset = -2 if partition_key_idx == len(cols) - 1 else -1
        coln_idx = len(cols) + coln_offset
        local_results = fileutil.get_meta(self.data_filepath, self._col_sep, coln_idx,
                                          coln_idx if not self.is_partition_table else partition_key_idx)
        # 本地文件不存在分区值的情况下，partition_key_idx==len(cols)是个无效值，取默认分区长度数量为1
        if self.is_partition_table and self.partition_value:
            local_results['colv_len_num'] = 1 if local_results['row_num'] != 0 else 0
        self.logger.info(f'hive_results: {hive_results}')
        self.logger.info(f'local_results: {local_results}')

        misses = []
        for k in hive_results.keys():
            hive_value = hive_results[k]
            local_value = local_results[k]
            if hive_value != local_value:
                msg = f'key: {k}, hive: {hive_value}, local: {local_value}'
                self.logger.error(msg)
                misses.append(msg)
        if len(misses) > 0:
            msg = f'[{self.config.execution.execution_name}] table {self.parquet_tablename} ' \
                  f'hive value can not equals to local value: {misses}'
            Alarm._info_pure(msg, self.config.execution.alarm_receivers)
            self.logger.error(msg)
            # raise Exception(f'hive value can not equals to local value: {misses}')

    def __finish___(self):
        if SystemConfig.read('CHECK_IMPORT_RESULT', default=True) and int(self.config.disable_result_check) == 0:
            try:
                self._check_import_result()
            except Exception as why:
                self.logger.exception(f'get hive recheck result and compare error: {why}')
        else:
            self.logger.info(
                f"system config CHECK_IMPORT_RESULT: {SystemConfig.read('CHECK_IMPORT_RESULT', default=True)}, self.config.disable_result_check: {self.config.disable_result_check}, skip check import result")
        self.client.close()
        # 清理本地txt文件
        osutil.rm(self.data_filepath)

    def __execute_sql(self, sql):
        self.logger.info(f'execute sql: {sql}')
        rcode = self.client.exec(sql)
        if rcode > 0:
            raise SqlExecuteFailedException(f'sql execute failed: {sql}')

    def __execute_cmd(self, cmd):
        self.logger.info(f'execute cmd: {cmd}')
        rcode = osutil.call(cmd)
        if rcode > 0:
            raise CmdExecuteFailedException(f'cmd execute failed: {cmd}')

    def _com_drop_table(self, tablename):
        drop_table_sql = f'drop table if exists {tablename}'
        self.__execute_sql(drop_table_sql)

    def _com_create_table(self, tablename, table_cols_meta, tmp=False, external=True,
                          sep=None, stored='parquet',
                          location=None,
                          purge=True, null_format=None):
        sql_create_list = []
        external_sql = 'external' if external else ''
        sql_create_list.append(f'create {external_sql} table if not exists {tablename}')

        # 分区表：不包含partition_key；非分区表：包含所有字段
        table_cols = [f"`{col}` {table_cols_meta[col][0]} comment '{table_cols_meta[col][1]}'" for col in
                      table_cols_meta.keys() if not self.is_partition_table or col != self.partition_key]
        table_cols_sql = ','.join(table_cols)
        # 非分区表：不再处理
        # 分区表-分区值从文件中读取：tmp带partition_key，非tmp不带partition_key
        # 分区表-分区值使用额值填充：tmp表不带partition_key（后期select当前日期填充），非tmp不带partition_key
        # 角度2：只有tmp表才需要带上partition_key，当tmp表不是分区表或者分区表有指定的分区值时都不需要带上partition_key（1没必要2后面追加）
        table_cols_sql = f'{table_cols_sql}, {self.partition_key} string' \
            if self.is_partition_table and not self.partition_value and tmp else table_cols_sql
        sql_create_list.append(f'({table_cols_sql})')
        table_description = f" comment '{self.config.table_description}'" if hasattr(self.config, 'table_description') and self.config.table_description else ''
        sql_create_list.append(f' {table_description}')

        # 临时表一律不需要partitioned by/正式表只有分区表需要
        if not tmp and self.is_partition_table:
            sql_create_list.append(f"partitioned by ({self.partition_key} string)")
        if sep:
            sql_create_list.append(f"row format delimited fields terminated by '{sep}'")
        sql_create_list.append(f'stored as {stored}')
        if location:
            sql_create_list.append(f"location '{location}'")

        tblproperties = []
        # 自动导入流程从ok文件中获取life_cycle并创建execution params(sink config)
        if self.config.life_cycle:
            tblproperties.append(f"'life_cycle'='{self.config.life_cycle}'")
        if purge:
            tblproperties.append("'external.table.purge'='true'")
        # 该参数对sparksql无效，暂时去除单独处理
        # if tmp and self.has_header:
        #     tblproperties.append("'skip.header.line.count'='1'")
        if len(tblproperties) > 0:
            sql_create_list.append(f'tblproperties({",".join(tblproperties)})')

        sql_create = ' '.join(sql_create_list)
        self.__execute_sql(sql_create)
        if null_format:
            self.__execute_sql(f"alter table {tablename} set serdeproperties('serialization.null.format'='NULL')")
        if not tmp and self.is_partition_table:
            self.__execute_sql(f'msck repair table {tablename}')

    def _com_insert_table(self, source, target, where_sql=None):
        if not self.config.write_mode:
            write_mode = 'overwrite'
        elif self.config.write_mode == 'append':
            write_mode = 'into'
        else:
            write_mode = 'overwrite'
        sql_insert_list = [f'insert {write_mode} table {target}']
        partition_value = ''
        if self.is_partition_table:
            sql_insert_list.append(f'partition({self.partition_key})')
            partition_value = '' if not self.partition_value else \
                f',"{self.partition_value}" as `{self.partition_key}`'
        custom_cols = ''
        if self.config.custom_cols:
            custom_cols_arr = []
            for name in self.config.custom_cols:
                custom_cols_arr.append(f'"{self.config.custom_cols[name]}" as `{name}`')
            custom_cols = "," + ",".join(custom_cols_arr)

        sql_insert_list.append(f'select *{custom_cols}{partition_value} from {source}')
        if where_sql:
            sql_insert_list.append(f'where {where_sql}')
        sql_insert = ' '.join(sql_insert_list)
        self.__execute_sql(sql_insert)

    def _step1_drop_tmp_table(self):
        self._com_drop_table(self.tmp_tablename)

    def _step2_put_file2hdfs(self):
        cmd_hdfs_rm_tmppath = f'hdfs dfs -rmr {self.tmp_hdfspath}'
        # 执行失败不需要抛异常
        osutil.call(cmd_hdfs_rm_tmppath)

        cmd_hdfs_mkdir_tmppath = f'hdfs dfs -mkdir -p {self.tmp_hdfspath}'
        self.__execute_cmd(cmd_hdfs_mkdir_tmppath)

        cmd_hdfs_put_tmpfile = f'hdfs dfs -put {self.data_filepath} {self.tmp_hdfspath}'
        self.__execute_cmd(cmd_hdfs_put_tmpfile)
        # self.logger.info(f'data file put to hdfs success, delete local file {self.data_filepath}')

    def _step3_create_tmp_table(self):
        # 删除并创建ftplink_临时表关联（txt、外部、可清理）
        table_cols_meta = {}
        for col in self.table_cols.keys():
            # 临时表无需添加自定义字段，insert过程将会通过select填充
            if self.table_cols[col][-1] == 'user-defind':
                continue
            table_cols_meta[col] = ('string', '')
        null_format = self.config.null_format if self.config.null_format else SystemConfig.read(
            'HIVE_TMP_NULL_FORMAT')
        null_format = None if null_format == '\\N' else null_format
        self.logger.debug(f'get tmp table nullformat: {null_format}')
        self._com_create_table(self.tmp_tablename, table_cols_meta, tmp=True, external=True,
                               sep=self._hive_sep, stored='textfile', location=self.tmp_hdfspath, purge=True,
                               null_format=null_format
                               )

    def _step4_create_parquet_table(self, purge=False):
        # 删除并创建正式表（联营：parquet、外部、不可清理、美团：parquet、外部、可清理）
        # 20250712 update: 只有在msck repair table 的情况下才需要重新建表
        self.logger.info(f'current msck_table config: {self.config.msck_table}')
        if self.config.msck_table is not None and bool(int(self.config.msck_table)):
            self._com_drop_table(self.parquet_tablename)
            self._com_create_table(self.parquet_tablename, self.table_cols, tmp=False, external=True,
                               sep=None, stored='parquet',
                               location=self.parquet_hdfspath,
                               purge=purge,
                               null_format=None)
        else:
            self.__execute_sql(f"alter table {self.parquet_tablename} add if not exists partition({self.partition_key}='{self.partition_value}');")

    def _step5_insert_parquet_from_tmp(self):
        # 临时表动态分区写入正式表
        self._com_insert_table(self.tmp_tablename, self.parquet_tablename, where_sql=None)
