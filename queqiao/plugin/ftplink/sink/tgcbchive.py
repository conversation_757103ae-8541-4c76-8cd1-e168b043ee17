"""
Author: xiaoh<PERSON>
Date: 2022/5/25
Email: <EMAIL>
Host: xiaohei.info
"""
import copy
import json

from queqiao.conf import IllegalParamsException
from queqiao.conf.system import SystemConfig
from queqiao.plugin.ftplink.sink import hive
from queqiao.plugin.ftplink.sink.hive import HiveSink

component = copy.deepcopy(hive.component)
component['comment'] = '天宫hive集群'
component['configs']['distribute_col'] = {'name_cn': '机构分发字段', 'type': 'string', 'default': None, 'required': 0,
                                         'demo': 'org_id', 'comment': '根据此字段从mtdw分发数据到各行dw'}


# 主要做后续的mtdw文件分发
class TgcbchiveSink(HiveSink):

    # view_tg固定部分在创建任务时的sink表名中人工定义去除
    def _write(self, local_filepath, ok_dict):
        super()._write(local_filepath, ok_dict)
        if self.config.distribute_col:
            if self.config.distribute_col not in self.table_cols:
                raise IllegalParamsException(f'{self.config.distribute_col} not in table column list, please recheck!')
            self.__distribute_by_orgcol()

    def _step4_create_parquet_table(self, purge=False):
        purge = True if self.target_db == 'mtdw' else False
        super()._step4_create_parquet_table(purge)

    def __distribute_by_orgcol(self):
        self.logger.info(f'found org_col {self.config.distribute_col}, start distribute data to banks')
        org_dws = json.loads(SystemConfig.read('TG_CBC_TL_ONLINE_BANKDW'))
        for org_dw in org_dws.keys():
            org_code = org_dws[org_dw]
            # 美团侧数据：删除并创建银行表（parquet、外部、不可清理）
            bank_tablename = f'{org_dw}.{self.target_table}'
            self._com_drop_table(bank_tablename)
            self._com_create_table(bank_tablename, self.table_cols, tmp=False, external=True,
                                   sep=None, stored='parquet', location=None,
                                   purge=False,
                                   null_format=None)

            # 美团侧数据：mtdw库动态分区写入银行库
            self._com_insert_table(self.parquet_tablename, f'{org_dw}.{self.target_table}',
                                   where_sql=f"{self.config.distribute_col}='{org_code}'")
