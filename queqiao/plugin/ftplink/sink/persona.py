"""
Author: xia<PERSON><PERSON>
Date: 2022/9/12
Email: <EMAIL>
Host: xiaohei.info
"""
import time


from queqiao.lib.push import Pusher
from queqiao.log.util import calc_elapsed
from queqiao.plugin.ftplink.sink import FtplinkSink
from queqiao.util.mt.utvsapi import Utvs

component = {
    'comment': 'persona接口调用',
    'configs': {
        'alarm_receivers': {'name_cn': '告警接收人', 'type': 'string', 'default': None, 'required': 1,
                            'demo': 'jiangyuande', 'comment': '接口调用异常时将会发送告警至指定接收人'},
        'sleep_sec': {'name_cn': '睡眠时间', 'type': 'int', 'default': 0.1, 'required': 1,
                      'demo': 1, 'comment': '每次调用接口后等待的时间，单位秒，默认为0.1秒，避免QPS过高'},
    }
}


class PersonaSink(FtplinkSink):
    def _write(self, local_filepath, ok_dict):
        start_time = time.time()
        pusher = Pusher.get_pusher()
        with open(local_filepath, 'r', encoding='utf-8') as f:
            for persona_group_id in f:
                persona_group_id = persona_group_id.strip()
                try:
                    if not persona_group_id.isdigit():
                        self.logger.error(
                            f'Invalid persona_group_id: {persona_group_id}')
                        continue
                    response = Utvs.rerun_crowd(persona_group_id)
                    self.logger.info(f'persona_group_id: {persona_group_id}, response: {response}')
                    if response['code'] != 0:
                        pusher.push(
                            f'persona rerunCrowd 接口调用失败，persona_group_id: {persona_group_id}, message: {response["message"]}', self.config.alarm_receivers)
                except Exception as e:
                    self.logger.error(
                        f'failed to call persona API, group id: {persona_group_id}, error: {e}')
                    pusher.push(
                        f'persona rerunCrowd 接口调用异常，persona_group_id: {persona_group_id}, error: {e}', self.config.alarm_receivers)
                time.sleep(float(self.config.sleep_sec))
        self.logger.info(
            f'call persona API succeed, elapsed time: {calc_elapsed(start_time, self.logger)}')
