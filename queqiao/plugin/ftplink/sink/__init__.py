"""
Author: xiaohei
Date: 2022/4/28
Email: <EMAIL>
Host: xiaohei.info
"""
import json
from abc import abstractmethod

from queqiao.conf.system import SystemConfig
from queqiao.core.engine.base import Sink
from queqiao.dba.models import FtplinkHistoryFile


class FtplinkSink(Sink):
    # 固定输入参数
    def write(self, channel):
        # 部分sink没有target_sep，如ftp
        if hasattr(self.config, 'target_sep'):
            target_sep = self.config.target_sep if self.config.target_sep else SystemConfig.read(
                'LOCALFILE_DEFAULT_SEP')
            self._hive_sep = '\\u0001' if target_sep == 'special' or target_sep == '\x01' or target_sep == '\\x01' else target_sep
            self._col_sep = '\x01' if target_sep == '\\u0001' or target_sep == 'special' or target_sep == '\\x01' else target_sep
        local_filepath = channel.get_pipeline_param('local_filepath')
        self.logger.info(f'get local_filepath: {local_filepath}')
        ok_dict = channel.get_pipeline_param('ok_dict')
        self.logger.debug(f'get ok_dict: {ok_dict}')
        self._write(local_filepath, ok_dict)
        self.logger.info('save file info to ftplink history files')
        try:
            self.__save_ftplink_history_file(ok_dict)
        except Exception as e:
            self.logger.error(f'write to ftplink history file failed, {e.args}')
        self.logger.info(f'do finish')
        self.__finish___()

    @abstractmethod
    def _write(self, local_filepath, ok_dict):
        pass

    def __save_ftplink_history_file(self, ok_dict):
        param_dict = ok_dict.copy()
        history_file = FtplinkHistoryFile.new(create_user=self.config.execution.create_user,
                                              source=param_dict.pop('source'),
                                              sink=self.__class__.__name__.replace('Sink', '').lower(),
                                              local_file=param_dict.pop('local_file'),
                                              bytes=int(param_dict.pop('bytes')),
                                              md5=param_dict.pop('md5'),
                                              row_num=int(param_dict.pop('row_num')),
                                              col_num=int(param_dict.pop('col_cnt')),
                                              ctime=param_dict.pop('ctime'),
                                              execution_id=self.config.execution.id,
                                              task_id=self.config.execution.task_id,
                                              project_id=self.config.execution.project_id,
                                              source_org_id=self.config.execution.source_org_id,
                                              sink_org_id=self.config.execution.sink_org_id
                                              )
        history_file.remote_file = None if 'remote_file' not in param_dict else param_dict.pop('remote_file')
        check_file_type = history_file.remote_file if history_file.source == 'ftp' or history_file.sink == 'ftp' \
            else history_file.local_file
        history_file.file_type = 'tar.gz' if check_file_type.endswith('tar.gz') else check_file_type.split('.')[-1]
        if 'table_schema' in param_dict:
            table_schema = param_dict.pop('table_schema')
            history_file.col_list = ','.join([col['name'] for col in table_schema])
        history_file.check_meta = json.dumps(param_dict)
        history_file.save()

    def __finish___(self):
        pass
