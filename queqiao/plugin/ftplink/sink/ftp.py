"""
Author: xiaohei
Date: 2022/4/29
Email: <EMAIL>
Host: xiaohei.info
"""
import json
import os

from queqiao.conf.enums import CompressType
from queqiao.log.util import calc_elapsed
from queqiao.plugin.ftplink.sink import FtplinkSink
from queqiao.util.comm import fileutil, osutil, strutil
from queqiao.util.comm.dtutil import timer
from queqiao.util.conn.ftp import RemoteFileServer

component = {
    'comment': 'ftp服务器',
    'configs': {
        'file_name': {'name_cn': '文件名规则', 'type': 'string', 'default': None, 'required': 1,
                      'demo': 'meituan_firpos_custlist_${now.delta(1).datekey}.txt', 'comment': '目标文件名规则'},
        'file_path': {'name_cn': '文件路径', 'type': 'string', 'default': None, 'required': 1,
                      'demo': 'card/gd/${now.delta(1).datekey}', 'comment': '目标文件存放路径'},
        'ok_file': {'name_cn': '就绪文件规则', 'type': 'string', 'default': None, 'required': 0,
                    'demo': '.ok', 'comment': '就绪文件名，解析规则见: https://km.sankuai.com/page/1316077828'},
        'md5_file': {'name_cn': 'md5文件规则', 'type': 'string', 'default': None, 'required': 0,
                     'demo': '.md5', 'comment': 'md5文件名，内容为数据文件的md5值，文件名规则与就绪文件一致'},
        'with_header': {'name_cn': '结果文件是否携带表头', 'type': 'boolean', 'default': 0, 'required': 0,
                        'demo': None, 'comment': '结果文件是否携带表头,默认不携带'},
        # 'target_sep': {'name_cn': '列分隔符', 'type': 'string', 'default': ',', 'required': 1,
        #                'demo': ',', 'comment': '文件中的列分隔符，支持多位字符，不可见字符（\\x01）请使用变量$sep01'},
        # 'has_header': {'name_cn': '文件头', 'type': 'boolean', 'default': '0', 'required': 1,
        #                'demo': None, 'comment': '是否生成文件头'},
        'compress_type': {'name_cn': '压缩类型', 'type': 'set', 'default': 'none', 'required': 1,
                          'demo': 'none,zip,gzip,tar.gz', 'comment': '目标文件的压缩类型，none为普通文本文件'},
        'compress_passwd': {'name_cn': '压缩密码', 'type': 'string', 'default': None, 'required': 0,
                            'demo': None, 'comment': '若压缩包有加密则填入解压密码'},
        'compress_file_type': {'name_cn': '压缩前的文件类型', 'type': 'string', 'default': None, 'required': 0,
                               'demo': 'csv', 'comment': '文件类型，默认txt(已弃用)'},
        'compress_with_ok_file': {'name_cn': '和标识文件一起打包后压缩数据类型', 'type': 'boolean', 'default': None,
                                  'required': 0,'demo': None, 'comment': '是否和标识文件一起打包'},
        'retlist_file': {'name_cn': '是否生成list文件', 'type': 'boolean', 'default': 0, 'required': 0,
                         'demo': None, 'comment': '是将会生成MT_FILE_yyyymmdd.list记录每个文件的上传时间点信息'}
    }
}


class FtpSink(FtplinkSink):

    def __init__(self, sink_config):
        super().__init__(sink_config)
        self.client = RemoteFileServer.get_connect(json.loads(sink_config.dsn.connect), logger=self.logger)
        self.client.open()
        dsn_config  = json.loads(sink_config.dsn.connect)
        if 'password' in dsn_config:
            dsn_config['password'] = '******'
        self.logger.info(f'connect to remote file server({self.client}) with dsn: {dsn_config}')

    def _write(self, local_filepath, ok_dict):
        # 处理文件头
        if bool(int(self.config.with_header)):
            header = ok_dict['target_sep'].join([col['name'] for col in ok_dict['table_schema']])
            with calc_elapsed('add header to file', self.logger):
                osutil.add_header(local_filepath, header)
            ok_dict['md5'] = strutil.md5_file(local_filepath)

        # 初始化远程文件名
        remote_filename = self.config.file_name

        is_already_compressed = False
        if remote_filename.endswith(f".{self.config.compress_type}"):
            is_already_compressed = True
            self.logger.info(f'File {remote_filename} is already in compressed format, skipping compression')

        # 文件压缩前提前将本地文件rename为对应文件名,否则解压后文件名与最终文件名不匹配
        filepath = os.path.dirname(local_filepath)
        new_filename = self.config.file_name
        local_filepath_new = f'{filepath}/{new_filename}'
        self.logger.info(f'get new local_filepath: {local_filepath_new}')
        osutil.mv(local_filepath, local_filepath_new)
        local_filepath = local_filepath_new
        compress_with_ok_file = bool(int(self.config.compress_with_ok_file)) if self.config.compress_with_ok_file is not None else False
            
        # 压缩本地文件
        if (self.config.compress_type and self.config.compress_type != CompressType.NONE.value
                and not compress_with_ok_file and not is_already_compressed):
            local_filepath_compress = '.'.join(local_filepath.split('.')[:-1]) + f'.{self.config.compress_type}'
            self.logger.info(
                f'compress_type is {self.config.compress_type} with compress_passwd: {self.config.compress_passwd}')
            self.logger.info('get compress file: %s, start compressing...' % local_filepath_compress)

            local_filepath_compress = fileutil.compress(local_filepath, local_filepath_compress,
                                                        self.config.compress_type, self.config.compress_passwd)

            self.logger.info('compress success! origin file size: %s, compressed file size: %s' % (
                os.path.getsize(local_filepath), os.path.getsize(local_filepath_compress)))
            os.remove(local_filepath)
            self.logger.info('origin file %s has been deleted' % local_filepath)
            local_filepath = local_filepath_compress
            # 更新压缩后的远程文件名
            remote_filename = remote_filename.split('.')[0] + f'.{self.config.compress_type}'

        # file_path需要包含日期
        remote_savedir = f'{self.client.work_dir}/{self.config.file_path}' if not self.config.file_path.startswith(
            '/') else self.config.file_path

        local_filepath_ok = local_filepath + ".ok"
        with open(local_filepath_ok, 'w') as okf:
            ok_content = json.dumps(ok_dict, ensure_ascii=False) if ok_dict else ''
            okf.write(ok_content)
            self.logger.info(f'write task info %s to ok file {local_filepath_ok}' % ok_content)

        # ok文件打包压缩文件
        if compress_with_ok_file and self.config.ok_file and self.config.compress_type and not is_already_compressed:
            self.logger.info(f'compress_with_ok_file is true, start compressing data file and ok file with {self.config.compress_type}')
            local_filepath_compress = '.'.join(local_filepath.split('.')[:-1]) + f'.{self.config.compress_type}'
            self.logger.info('get compress file: %s, start compressing...' % local_filepath_compress)
            local_filepath_ok_tmp = f'{os.path.dirname(local_filepath)}/{self.config.ok_file}' if not self.config.ok_file.startswith(
                '.') else f'{local_filepath}{self.config.ok_file}'
            # 更新ok文件名为目标文件名
            osutil.mv(local_filepath_ok, local_filepath_ok_tmp)
            local_filepath_ok = local_filepath_ok_tmp
            if self.config.compress_file_type and not local_filepath.endswith(self.config.compress_file_type):
                local_filepath_tmp = '.'.join(local_filepath.split('.')[:-1]) + f'.{self.config.compress_file_type}'
                osutil.mv(local_filepath, local_filepath_tmp)
                local_filepath = local_filepath_tmp
            local_filepath_compress = fileutil.compress([local_filepath, local_filepath_ok], local_filepath_compress,
                                                        self.config.compress_type, self.config.compress_passwd)
            os.remove(local_filepath)
            self.logger.info('origin file %s has been deleted' % local_filepath)
            local_filepath = local_filepath_compress
            # 更新压缩后的远程文件名
            remote_filename = remote_filename.split('.')[0] + f'.{self.config.compress_type}'
        
        remote_filepath = os.path.join(remote_savedir, remote_filename)
        self.logger.info(f'remote_filename: {remote_filename}, remote_filepath: {remote_filepath}')
        ok_dict['remote_file'] = remote_filepath
        
        list_filename = "MT_FILES.list"
        remote_filepath_list = os.path.join(remote_savedir, list_filename)
        self.logger.info(f'list_filename: {list_filename}, remote_filepath_list: {remote_filepath_list}')

        def upload():
            self.client.upload(local_filepath, remote_filepath)
            self.logger.info(f'upload local_filepath: {local_filepath} to remote_filepath: {remote_filepath}')
            # if self.config.delete_localfile:
            # osutil.rm(local_filepath)
            # osutil.rm(local_filepath_ok)
            # self.logger.info(f'delete local_filepath, local_filepath_ok')
            if self.config.ok_file and not compress_with_ok_file:
                remote_filename_ok = self.config.ok_file if not self.config.ok_file.startswith(
                    '.') else f'{remote_filename}{self.config.ok_file}'
                remote_filepath_ok = os.path.join(remote_savedir, remote_filename_ok)
                self.logger.info(f'remote_filename_ok: {remote_filename_ok}, remote_filepath_ok: {remote_filepath_ok}')
                self.client.upload(local_filepath_ok, remote_filepath_ok)
                self.logger.info(
                    f'upload local_filepath_ok: {local_filepath_ok} to remote_filepath_ok: {remote_filepath_ok}')
            if self.config.md5_file:
                local_filepath_md5 = local_filepath + ".md5"
                with open(local_filepath_md5, 'w') as md5f:
                    md5_content = ok_dict['md5'] if 'md5' in ok_dict else strutil.md5_file(local_filepath)
                    md5f.write(md5_content)
                    self.logger.info(f'write file md5 %s to md5 file {local_filepath_md5}' % md5_content)
                remote_filename_md5 = self.config.md5_file if not self.config.md5_file.startswith(
                    '.') else f'{remote_filename}{self.config.md5_file}'
                remote_filepath_md5 = os.path.join(remote_savedir, remote_filename_md5)
                self.logger.info(
                    f'remote_filename_md5: {remote_filename_md5}, remote_filepath_md5: {remote_filepath_md5}')
                self.client.upload(local_filepath_md5, remote_filepath_md5)
                self.logger.info(
                    f'upload local_filepath_md5: {local_filepath_md5} to remote_filepath_md5: {remote_filepath_md5}')
            if self.config.retlist_file:
                self.logger.info(f'generate retlist file')
                origin_content = ""
                if self.client.exists(remote_filepath_list):
                    # 获取远程list文件内容
                    origin_content = self.client.read(remote_filepath_list)
                    origin_content = origin_content if isinstance(origin_content, str) else origin_content.decode()
                    self.logger.info(f'get remote origin_content: {origin_content}')
                # 更新list文件内容
                list_content = remote_filename + ' ' + f'{timer.now().hourmin}'
                content = origin_content + list_content + '\r\n'
                self.client.write(content, remote_filepath_list)
                self.logger.info('rewrite %s to %s' % (list_content, remote_filepath_list))

        try:
            upload()
        except Exception as why:
            self.logger.error(f'upload files to remote server error: {why}, retry onece')
            upload()

    def __finish___(self):
        self.client.close()
