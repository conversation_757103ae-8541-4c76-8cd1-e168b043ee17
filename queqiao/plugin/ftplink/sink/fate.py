"""
Author: xiaohei
Date: 2022/9/12
Email: <EMAIL>
Host: xiaohei.info
"""
import json

import mmh3

from queqiao.conf.errors import ExecuteFailedException, NotFoundException
from queqiao.log.util import calc_elapsed
from queqiao.plugin.ftplink.sink import FtplinkSink
from queqiao.util.comm import osutil, fileutil
from queqiao.util.conn.fate import FateClient

component = {
    'comment': '写入fate',
    'configs': {
        'party_id': {'name_cn': 'PartyId', 'type': 'string', 'default': None, 'required': 1,
                     'demo': '10005', 'comment': 'Party ID（-p）'},
        'bucket_cnt': {'name_cn': '数据分桶个数', 'type': 'int', 'default': '0', 'required': 1,
                       'demo': '0,50', 'comment': '数据分桶个数，0为不分桶'},
        'bucket_col': {'name_cn': '数据分桶字段', 'type': 'string', 'default': None, 'required': 0,
                       'demo': 'col1', 'comment': '数据分桶字段，将根据该字段的枚举值进行分桶'},
        'null_format': {'name_cn': '空值处理', 'type': 'string', 'default': None, 'required': 0,
                        'demo': 'NULL', 'comment': '将会以配置值代替NULL（空值）'},
        'partition': {'name_cn': '分区数', 'type': 'int', 'default': 8, 'required': 1,
                      'demo': '8,16', 'comment': '用于存储数据的分区数'},
        'work_mode': {'name_cn': '工作模式', 'type': 'int', 'default': 1, 'required': 1,
                      'demo': '0,1', 'comment': '0为单机版，1为集群版'},
        'namespace': {'name_cn': '命名空间', 'type': 'string', 'default': None, 'required': 1,
                      'demo': 'mt_platform', 'comment': '存储数据表的标识符号'},
        'tablename': {'name_cn': '存储表名', 'type': 'string', 'default': None, 'required': 1,
                      'demo': 'mt_userlist', 'comment': '存储数据表的标识符号'},
        'key_idx': {'name_cn': '主键位置索引', 'type': 'int', 'default': 0, 'required': 0,
                    'demo': '0', 'comment': '主键key所在的列索引（分桶时不可为空）'},
    }
}


class FateSink(FtplinkSink):
    def __init__(self, sink_config):
        super().__init__(sink_config)
        connect = json.loads(sink_config.dsn.connect)
        self.client = FateClient(connect['ip'], connect['port'], logger=self.logger)
        self.client.open()

    def __preprocess(self, local_filepath, ok_dict):
        target_sep = ok_dict['target_sep']
        header = target_sep.join([c['name'] for c in ok_dict['table_schema']]) if len(
            ok_dict['table_schema']) > 0 else None

        if self.config.null_format is not None:
            # todo: 确认是否有字符串的NULL
            self.logger.info(f'reset NULL with {self.config.null_format}')
            fileutil.reset_sep(local_filepath, curr_sep='NULL', target_sep=self.config.null_format)
        if header:
            self.logger.info(f'add header {header} to {local_filepath}')
            osutil.add_header(local_filepath, header)
        if len(header) > 1 and target_sep and target_sep != ',':
            self.logger.info(f'reset target_sep from {target_sep} to ,')
            fileutil.reset_sep(local_filepath, curr_sep=target_sep, target_sep=',')

    def __upload_full(self, local_filepath):
        self.logger.info('upload full file to fate server')
        result = self.client.data_upload(local_filepath, self.config.partition, self.config.namespace,
                                         self.config.tablename, self.config.work_mode)
        return {'-1': result}

    def __upload_bucket(self, local_filepath, bucket_list, bucket_func):
        self.logger.info(f'upload bucket files to fate server with bucket list: {bucket_list}')
        header = osutil.remove_header(local_filepath)
        self.logger.info(f'get header {header} from {local_filepath}')
        bucket_files = {}
        for bucket_id in bucket_list:
            bucket_files[bucket_id] = open(f'{local_filepath}.{bucket_id}', 'w')
        self.logger.info(f'init bucket files: {bucket_files}, split data and write to bucket file')
        with open(local_filepath, 'r') as input:
            for line in input:
                bucket_id = bucket_func(line)
                output = bucket_files[bucket_id]
                output.write(line)
        self.logger.info(
            f'split {local_filepath} to {len(bucket_list)} files, bucket id list: {bucket_files.keys()}')
        self.logger.info('upload bucket files to fate server')
        task_results = {}
        for bucket_id in bucket_files.keys():
            bucket_file = bucket_files[bucket_id]
            bucket_file.close()
            self.logger.info(f'bucket id: {bucket_id}, close bucket file: {bucket_file} and add header {header}')
            osutil.add_header(f'{local_filepath}.{bucket_id}', header)
            task_results[bucket_id] = self.client.data_upload(f'{local_filepath}.{bucket_id}',
                                                              self.config.partition,
                                                              self.config.namespace,
                                                              f'{self.config.tablename}_{bucket_id}',
                                                              self.config.work_mode)
        osutil.rm(f'{local_filepath}')
        return task_results

    def __upload(self, local_filepath, ok_dict):
        self.logger.info(f'bucket count: {self.config.bucket_cnt}, bucket col: {self.config.bucket_col}')
        if self.config.bucket_col:
            table_cols = [c['name'] for c in ok_dict['table_schema']]
            if self.config.bucket_col not in table_cols:
                raise NotFoundException(f'bucket col {self.config.bucket_col} not found in {table_cols}')
            bucket_col_idx = table_cols.index(self.config.bucket_col)
            with calc_elapsed('get bucket col values', self.logger):
                cmd = "awk -F ',' '{if (NR>1){print $INDEX}}' ".replace('INDEX',
                                                                        str(bucket_col_idx + 1)) + f"{local_filepath} | sort | uniq"
                self.logger.debug(cmd)
                bucket_col_values = osutil.calls(cmd)
            self.logger.info(
                f'bucket_col_idx: {bucket_col_idx}, bucket_col_len: {len(bucket_col_values)}, bucket_col_values: {bucket_col_values}')
            return self.__upload_full(local_filepath) if len(bucket_col_values) <= 1 else self.__upload_bucket(
                local_filepath, bucket_col_values, lambda line: line.split(',')[bucket_col_idx].strip())
        else:
            bucket_cnt = int(self.config.bucket_cnt)
            bucket_list = [i for i in range(0, bucket_cnt)]
            key_idx = 0 if self.config.key_idx is None else int(self.config.key_idx)
            self.logger.info(f'bucket cnt key idx: {key_idx}')

            def bucket_func(line):
                key = line.split(',')[key_idx] if len(ok_dict['table_schema']) > 1 else line
                bucket_id = mmh3.hash(key, 42) % bucket_cnt
                return bucket_id

            return self.__upload_full(local_filepath) if bucket_cnt <= 1 else self.__upload_bucket(local_filepath,
                                                                                                   bucket_list,
                                                                                                   bucket_func)

    def _wait4finish(self, job_id):
        status = self.client.wait_job(job_id)
        if status == 'failed':
            self.logger.info(f'job {job_id} execute failed, retry once')
            self.client.job_rerun(job_id)
            status = self.client.wait_job(job_id)
            self.logger.info(f'rerun job {job_id} status {status}')
            if status == 'failed':
                raise ExecuteFailedException(f'job {job_id} execute failed, please check in fate board')

    def _write(self, local_filepath, ok_dict):
        self.__preprocess(local_filepath, ok_dict)
        task_results = self.__upload(local_filepath, ok_dict)
        job_ids = [task_results[bucket_id]['jobId'] for bucket_id in task_results.keys()]
        for job_id in job_ids:
            self.logger.info(f'wait {job_id} to finish')
            self._wait4finish(job_id)
            self.logger.info(f'task progress [{job_ids.index(job_id) + 1}/{len(job_ids)}]')
        # 提供给status接口
        from queqiao.dba.models import Execution
        execution = Execution.get(id=self.config.execution.id)
        execution.message = json.dumps(
            [{'namespace': self.config.namespace, 'tablename': self.config.tablename + '_' + str(bucket_id),
              'job_id': task_results[bucket_id]['jobId']} for bucket_id in task_results.keys()] if len(
                task_results) > 1 else [{'namespace': self.config.namespace, 'tablename': self.config.tablename,
                                         'job_id': job_ids[0]}])
        execution.save()
