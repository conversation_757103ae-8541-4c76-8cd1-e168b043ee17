"""
Author: xia<PERSON><PERSON>
Date: 2022/9/14
Email: <EMAIL>
Host: xiaohei.info
"""
import copy

from queqiao.dba.extend_model import Alarm
from queqiao.plugin.ftplink.sink import fate
from queqiao.plugin.ftplink.sink.fate import FateSink

component = copy.deepcopy(fate.component)
# fate.bucket_cnt用于文件上传分桶，task_bucket_cnt用于任务的分桶策略
# 存在上传时文件不分桶（对方分桶）、提交任务时需要分桶任务策略等上传与任务执行分桶策略不一致的情况
component['configs']['task_bucket_cnt'] = {'name_cn': '任务分桶提交个数', 'type': 'int', 'default': '0', 'required': 0,
                                           'demo': '0,50', 'comment': '任务分桶提交个数，0位不分桶只提交一次任务'}
component['configs']['job_conf'] = {'name_cn': '任务配置', 'type': 'text', 'default': None, 'required': 1, 'demo': None,
                                    'comment': 'fate任务执行的配置内容（-c），内置变量bucket_id'}
component['configs']['job_dsl'] = {'name_cn': 'DSL配置', 'type': 'text', 'default': None, 'required': 1,
                                   'demo': None, 'comment': 'fate任务执行的DSL内容（-d）'}
component['configs']['validate_rate'] = {'name_cn': '是否校验结果数量', 'type': 'int', 'default': -1, 'required': 0,
                                         'demo': 0.9, 'comment': '非0时进行结果校验（与源文件的数量比，PSI任务使用）'}
component['comment'] = '写入fate(执行任务)'


class FateTaskSink(FateSink):
    def __submit_task(self):
        task_results = {}
        self.logger.info('submit task to fate server')
        bucket_cnt = int(self.config.task_bucket_cnt)
        # 提交任务
        if bucket_cnt > 1:
            for bucket_id in range(0, bucket_cnt):
                bucket_id = str(bucket_id)
                job_conf = self.config.job_conf.replace('{bucket_id}', bucket_id)
                job_dsl = self.config.job_dsl.replace('{bucket_id}', bucket_id)
                task_results[bucket_id] = self.client.job_submit(job_conf, job_dsl)
        else:
            task_results[-1] = self.client.job_submit(self.config.job_conf, self.config.job_dsl)
        return task_results

    def __validate_result(self, job_id):
        if self.config.validate_rate and float(self.config.validate_rate) > 0:
            self.logger.info(f'get {job_id} summary and validate result')
            summary = self.client.get_summary(job_id, self.config.role, self.config.party_id,
                                              # 固定取交集
                                              'intersection_0')
            self.logger.info(f'get {job_id} intersection_0 summary: {summary}')
            intersect_rate = summary['data']['intersect_rate']
            if intersect_rate < float(self.config.validate_rate):
                # 告警
                Alarm.alarm(alarm_content=f'job {job_id} intersect_rate: {intersect_rate} is less then '
                                          f'{self.config.validate_rate}, please check the PSI job',
                            execution_id=self.config.execution.id)

    def _write(self, local_filepath, ok_dict):
        super()._write(local_filepath, ok_dict)
        task_results = self.__submit_task()
        self.logger.info(f'get {len(task_results)} task submit result')
        # 轮询等待
        job_ids = [task_results[bucket_id]['jobId'] for bucket_id in task_results.keys()]
        for job_id in job_ids:
            self.logger.info(f'wait {job_id} to finish')
            self._wait4finish(job_id)
            self.logger.info(f'task progress [{job_ids.index(job_id) + 1}/{len(job_ids)}]')
            # 结果校验
            self.__validate_result(job_id)
