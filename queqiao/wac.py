"""
Author: xiaohei
Date: 2022/4/19
Email: <EMAIL>
Host: xiaohei.info
"""
# todo: wac参数 lastest_ready_time,auto_import
import time

from queqiao.conf.system import SystemConfig
from queqiao.core.execute.task import Task
from queqiao.log import LogFactory
from queqiao.util.comm.dtutil import timer

log = LogFactory.get_logger()

if __name__ == '__main__':
    while True:
        curr_date = timer.now().date
        tasks = Task.get_auto_import_tasks()
        for task in tasks:
            Task.execute(task.name)
        wac_sleep_sec = SystemConfig.read('WAC_SLEEP_SEC')
        log.info(f'{len(tasks)} ext ftp tasks check done, sleep {wac_sleep_sec}')
        time.sleep(wac_sleep_sec)
