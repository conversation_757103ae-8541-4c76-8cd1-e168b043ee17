"""
Author: xiaohei
Date: 2022/4/22
Email: <EMAIL>
Host: xiaohei.info
"""
import logging

from flask import has_request_context, request


class RequestFormatter(logging.Formatter):

    def format(self, record):
        if has_request_context():
            record.remote_addr = request.remote_addr
            record.url = request.url
            record.user = request.user.uid if hasattr(request, 'user') else 'no_login'
        else:
            record.remote_addr = None
            record.url = None
            record.reqid = None
            record.user = None

        return super(RequestFormatter, self).format(record)
