# coding=utf-8
import logging
import os
from logging import handlers
from logging.config import dictConfig

from instance import default
from instance.default import EXE_LOG_PATH, LOGGING_SETTINGS, log_level
from queqiao.conf.env import curr_queqiao_app
from queqiao.util.comm import osutil

dictConfig(LOGGING_SETTINGS)


def pure_logger_name(name):
    return name.replace('-', '').replace(':', '.')


def _get_self_logger(lf):
    lf = pure_logger_name(lf)
    logger = logging.getLogger(lf)
    if logger.hasHandlers():
        return logger
    log_format = f'[%(asctime)s %(levelname)s/%(processName)s/%(process)s %(module)s:%(lineno)d] %(message)s'
    format_str = logging.Formatter(log_format)

    if not osutil.exists(EXE_LOG_PATH):
        osutil.mkdir(EXE_LOG_PATH)
    filename = f'{EXE_LOG_PATH}/{lf}.log'
    logger.setLevel(log_level)
    th = handlers.TimedRotatingFileHandler(filename=filename, when='D', backupCount=3, encoding='utf-8')
    th.setFormatter(format_str)
    logger.addHandler(th)
    return logger


def get_logger(log_name=None):
    if logging.root.hasHandlers():
        logging.root.handlers = []

    if not log_name:
        log_name = curr_queqiao_app
    # __file__
    if '.py' in log_name:
        log_name = os.path.basename(log_name).replace('.py', '')
    # __name__
    # elif '.' in log_name:
    #     log_name = log_name.split('.')[-1]
    if log_name not in default.apps and log_name != 'console':
        return _get_self_logger(log_name)
        # log_name = default.DEFAULT_LOGGER
    return logging.getLogger(log_name)
