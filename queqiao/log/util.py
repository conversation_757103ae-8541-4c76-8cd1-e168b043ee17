"""
Author: xiaohei
Date: 2022/8/18
Email: <EMAIL>
Host: xiaohei.info
"""

import datetime
from contextlib import contextmanager


# from queqiao.log import LogFactory
#
# log = LogFactory.get_logger()
#
#
# def record_elapsed(func):
#     def wrapper(*args, **kw):
#         start_time = datetime.datetime.now()
#         res = func(*args, **kw)
#         end_time = datetime.datetime.now()
#         elapsed = (end_time - start_time).total_seconds()
#         log.debug(f'func [{func.__name__}] elapsed {elapsed}s')
#         return res
#
#     return wrapper


@contextmanager
def calc_elapsed(name, logger):
    start_time = datetime.datetime.now()
    elapsed = 0
    yield elapsed
    end_time = datetime.datetime.now()
    elapsed = (end_time - start_time).total_seconds()
    logger.info(f'process [{name}] elapsed {elapsed}s')
