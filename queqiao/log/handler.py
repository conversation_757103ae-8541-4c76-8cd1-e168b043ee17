"""
Author: xiaohei
Date: 2022/4/22
Email: <EMAIL>
Host: xiaohei.info
"""
from logging import handlers
import codecs
import datetime
import os


class DailyRotatingFileHandler(handlers.BaseRotatingHandler):

    def __init__(self, filename, backups=100, encoding=None, delay=False):
        self.baseFilename = filename
        self.backupCount = backups
        self.currentFileName = self._get_current_filename()
        handlers.BaseRotatingHandler.__init__(
            self, filename, 'a', encoding, delay
        )

    def shouldRollover(self, *args, **kwargs):

        if self.stream and self.stream.name != self.currentFileName:
            return True
        if self.currentFileName != self._get_current_filename():
            return True
        return False

    def doRollover(self):
        if self.stream:
            self.stream.close()
            self.stream = None
        self.currentFileName = self._get_current_filename()
        oldest_logfile = self._get_oldest_filename()
        if os.path.exists(oldest_logfile):
            os.remove(oldest_logfile)

    def _get_oldest_filename(self):
        oldest_day = datetime.datetime.now() - datetime.timedelta(
            days=self.backupCount
        )
        return self.baseFilename + oldest_day.strftime('.%Y-%m-%d')

    def _get_current_filename(self):
        today = datetime.datetime.now()
        return self.baseFilename + today.strftime('.%Y-%m-%d')

    def _open(self):
        if self.encoding is None:
            stream = open(self.currentFileName, self.mode)
        else:
            stream = codecs.open(self.currentFileName, self.mode, self.encoding)
        return stream
