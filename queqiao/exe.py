"""
Author: xiaohei
Date: 2022/4/19
Email: <EMAIL>
Host: xiaohei.info
"""
from __future__ import absolute_import, unicode_literals

import json
import os
import sys

sys.path.append(os.path.dirname(sys.path[0]))

from instance.default import IPADDR
from queqiao.conf.system import SystemConfig
from queqiao.conf.env import EnvConfig
from queqiao.dba.extend_model import Alarm
from queqiao.util.comm import osutil
from celery import Celery
from queqiao.api import app
from queqiao.log import LogFactory

log = LogFactory.get_logger()


class CeleryAppFactory:

    @classmethod
    def create_app(cls):
        celery = Celery('Execution', backend=app.config['CELERY_RESULT_BACKEND'],
                        broker=app.config['CELERY_BROKER_URL'])
        celery.conf.update(app.config)

        # celery.conf.CELERY_ACKS_LATE = True
        # celery.conf.CELERYD_PREFETCH_MULTIPLIER = 1

        class ContextTask(celery.Task):
            def __call__(self, *args, **kwargs):
                with app.app_context():
                    return self.run(*args, **kwargs)

            def on_success(self, retval, task_id, args, kwargs):
                with app.app_context():
                    Alarm.success(execution_qid=task_id)
                log.info(f'task {task_id} execute success')
                return super().on_success(retval, task_id, args, kwargs)

            def on_failure(self, exc, task_id, args, kwargs, einfo):
                with app.app_context():
                    Alarm.failed(execution_qid=task_id)
                log.info(f'task {task_id} execute failed')
                return super().on_failure(exc, task_id, args, kwargs, einfo)

            def on_retry(self, exc, task_id, args, kwargs, einfo):
                with app.app_context():
                    Alarm.retry(execution_qid=task_id)
                log.info(f'task {task_id} execute failed, retry later')
                return super().on_retry(exc, task_id, args, kwargs, einfo)

        celery.Task = ContextTask
        return celery


queue = CeleryAppFactory.create_app()
log.info('celery app is created')
if __name__ == '__main__':
    celery_queues = EnvConfig.get('CELERY_TASK_QUEUES')
    celery_workers = EnvConfig.get("CELERY_WORKERS")
    queues = celery_queues.get(IPADDR, None)
    if not queues:
        log.error(f'current host {IPADDR} can not get any celery queue from {json.dumps(celery_queues)}')
        sys.exit(1)
    worker_cmd = f'celery -A queqiao.core.engine worker --loglevel=info -Q {",".join(queues)} ' \
                 f'-P eventlet -c {celery_workers}'
    osutil.call(worker_cmd)
# celery -A queqiao.celery_test worker --loglevel=info -Q low,celery -P eventlet -c 20000
# celery multi start 1 -A proj -l INFO -c4 --pidfile=/var/run/celery/%n-%i.pid --logfile=/var/run/celery/%n-%i.log
# kill -TERM [WorkerMainProcessID]  #  worker主进程的进程ID需要通过ps -ef | grep celery获取；或者通过 celery -A app inspect stats的输出结果获取
# if __name__ == '__main__':
#     if len(sys.argv) < 3:
#         print(f'usage: [modiile] [queues]')
#         sys.exit(1)
#     # queqiao.core.engine.base
#     module = sys.argv[1]
#     # normal,bigtask,timing
#     queues = sys.argv[2]
#     args = f'-A {module} worker -P eventlet -c 1000 -Q {queues} -l info'
#     # args = f'-A {module} worker -c 1 -Q {queues} -l info'
#     queue.worker_main(args.split(' '))
