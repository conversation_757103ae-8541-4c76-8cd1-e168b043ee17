"""
Author: xiaoh<PERSON>
Date: 2022/5/5
Email: <EMAIL>
Host: xiaohei.info
"""
import json

from queqiao.conf import IllegalParamsException
from queqiao.conf.env import EnvConfig
from queqiao.dba import models
from queqiao.log import LogFactory
from queqiao.util.comm.cache import local_cache
from queqiao.util.conn.redis import redis_client

log = LogFactory.get_logger()


class SystemConfig(models.SystemConfig):
    @classmethod
    def from_dict(cls, param_dict):
        obj = cls()
        for k in param_dict.keys():
            setattr(obj, k, param_dict[k])
        return obj

    @classmethod
    @redis_client()
    @local_cache()
    def read(cls, key, default=None, value_only=True, redis=None):
        log.debug(f'key: {key}, default: {default}, value_only: {value_only}')
        key = key.upper()
        redis_conf_key = f'system:config:{key}'
        cached_value = redis.get(redis_conf_key)
        if cached_value:
            log.debug(f'get key: [{redis_conf_key}] value: [{cached_value}] in redis cache')
            system_config_dict = json.loads(cached_value)
            return system_config_dict['value'] if value_only else cls.from_dict(system_config_dict)
        log.debug(f'key [{redis_conf_key}] not found in redis cache, read system config in db')
        # 包含了env_config内容
        system_config = super().get_one(key=key)
        if not system_config:
            log.debug(f'system config [{key}] not found in db, return default')
            return default
        log.debug(f'get system config [{key}] from db, cache to redis')
        redis.set(redis_conf_key, json.dumps(system_config.to_dict()))
        redis.expire(redis_conf_key, EnvConfig.get('REDIS_KEY_EXPIRE'))
        return system_config.value if value_only else system_config

    @classmethod
    @redis_client()
    def dump(cls, key, value, name=None, opt_user=None, comment=None, redis=None):
        log.debug(f'key: {key}, value: {value}')
        key = key.upper()
        value = json.dumps(value) if isinstance(value, (dict, list, set)) or value is None else value
        log.debug(f'dumps value: {value}')
        system_config = super().get_one(key=key)
        if not system_config:
            if not name and not opt_user:
                raise IllegalParamsException(f'name or opt_user must be set!')
            log.debug(f'key [{key}] not found in system config, new and save')
            system_config = super().new(key=key, value=value, name=name, create_user=opt_user,
                                        update_user=opt_user,
                                        comment=comment)
        else:
            log.debug(f'key [{key}] found in system config, update and save')
            system_config.value = value
            if opt_user:
                system_config.update_user = opt_user
            if name:
                system_config.name = name
            if comment:
                system_config.comment = comment
        system_config.save()
        redis_conf_key = f'system:config:{key}'
        redis.set(redis_conf_key, json.dumps(system_config.to_dict()))
        redis_key_expire = EnvConfig.get('REDIS_KEY_EXPIRE')
        redis.expire(redis_conf_key, redis_key_expire)
        log.debug(f'cache system config [{key}] to redis with expire {redis_key_expire}')
        return system_config

    def __str__(self):
        return json.dumps(self.to_dict())

    def __eq__(self, other):
        return self.key == other.key and self.value == other.value and self.name == other.name

    def __hash__(self):
        return hash(self.key + self.value + self.name)
