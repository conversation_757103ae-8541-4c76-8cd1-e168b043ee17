from typing import Any, Optional

from dataclasses import dataclass
from flask import jsonify, Response

from queqiao.conf.errors import InternalException


@dataclass(frozen=True)
class ApiResult:  # noqa
    code: int
    msg: str

    def __call__(
            self,
            data: Any = None,
            detail: Optional[str] = None,
    ) -> Response:

        if detail:
            final_msg = f'{self.msg} {detail}'
        else:
            final_msg = self.msg
        ret = {'code': self.code, 'message': final_msg}
        from queqiao.dba.base_model import BaseModel
        if data is not None:
            if not isinstance(data, dict) and not isinstance(data, BaseModel) and not isinstance(data, list):
                ret['data'] = data
            else:
                if isinstance(data, list):
                    data_list = []
                    for d in data:
                        if not isinstance(d, dict) and not isinstance(d, BaseModel):
                            raise InternalException(
                                f'element in data must be dict or a BaseModel! current type: {type(d)}, content: {d}')
                        data_list.append(d if isinstance(d, dict) else d.to_dict())
                    ret['data'] = data_list
                elif isinstance(data, dict):
                    ret['data'] = data
                else:
                    ret['data'] = data.to_dict()
        return jsonify(ret)


SUCCESS = ApiResult(0, 'success')
DO_NOTHING = ApiResult(0, '什么都不用做可真爽~~')
UNKNOW_ERROR = ApiResult(999999, '未知异常，请联系管理员！')
LOGIN_FAILED = ApiResult(201001, '登录失败，请检查用户名或密码！')
ILLEGAL_PARAMS = ApiResult(201002, '非法的参数:')
MISSING_PARAMS = ApiResult(201003, '下列参数缺失:')
AUTH_FAILED = ApiResult(201401, '当前用户未登录，请重新登录!')
RESOURCE_EXISTS = ApiResult(201402, '操作失败！下列资源已存在:')
NO_PRIVILEGE = ApiResult(201403, '当前用户无此权限，禁止该操作!')
RESOURCE_NOT_FOUND = ApiResult(201404, '操作失败！无法找到以下资源:')
RESOURCE_NOT_AVAILABLE = ApiResult(201405, '操作失败！资源不可用或不在指定状态:')
FILE_ERROR = ApiResult(202001, '文件异常，请检查文件是否正确!')
SERVER_MISSING_PARAMS = ApiResult(203404, '操作失败！下列参数缺失:')
EXECUTE_FAILED = ApiResult(500401, '任务执行失败！')
SERVER_NOT_FOUND = ApiResult(500404, '服务错误！无法找到该服务:')
INTERVAL_ERROR = ApiResult(500500, '服务错误，系统内部异常!')
EXTERNAL_ERROR = ApiResult(500600, '服务错误，外部系统异常!')
