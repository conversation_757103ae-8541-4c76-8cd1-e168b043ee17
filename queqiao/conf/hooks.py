from typing import Optional

from flask import Flask, Response, request

from queqiao.conf import ApiResponse
from queqiao.conf.errors import NotFoundException, NotAvaliableException, InternalException, \
    ExternalException, AuthFailedException, IllegalParamsException, NoPrivilegeException
from queqiao.log import LogFactory

log = LogFactory.get_logger()


class Hook:

    def __init__(self, app: Flask) -> None:
        self.app = app

    def before_request(self):
        # if '/health/' not in request.path and \
        #         '/sso/' not in request.path and \
        #         '/alarm/' not in request.path and \
        #         'from_etl' not in request.args:
        log.info("get params from request: %s" % str(request.values))

    # @staticmethod
    # def after_request(response: Response) -> Response:
    #     response.headers.add('X-Request-id', g.reqid)
    #     return response

    def global_error_handler(
            self,
            exc: Exception
    ) -> Optional[Response]:  # yapf: disable
        exc_msg = str(exc)
        log.exception(f"The server have a 5XX error! url is {request.url}, exception: {exc_msg}")

        if isinstance(exc, NotFoundException):
            return ApiResponse.RESOURCE_NOT_FOUND(detail=exc_msg)
        if isinstance(exc, NotAvaliableException):
            return ApiResponse.RESOURCE_NOT_AVAILABLE(detail=exc_msg)
        if isinstance(exc, AuthFailedException):
            return ApiResponse.LOGIN_FAILED(detail=exc_msg)
        if isinstance(exc, IllegalParamsException):
            return ApiResponse.ILLEGAL_PARAMS(detail=exc_msg)
        if isinstance(exc, NoPrivilegeException):
            return ApiResponse.NO_PRIVILEGE(detail=exc_msg)

        if isinstance(exc, InternalException):
            return ApiResponse.INTERVAL_ERROR(detail=exc_msg)
        if isinstance(exc, ExternalException):
            return ApiResponse.EXTERNAL_ERROR(dadetailta=exc_msg)

        return ApiResponse.UNKNOW_ERROR(detail=exc_msg)

    def inject_user(self):
        user = getattr(request, 'user', None)
        return dict(user=user)

    def hookup(self) -> None:
        self.app.register_error_handler(Exception, self.global_error_handler)
        self.app.before_request(self.before_request)
        self.app.context_processor(self.inject_user)
        # self.app.after_request(self.after_request)
