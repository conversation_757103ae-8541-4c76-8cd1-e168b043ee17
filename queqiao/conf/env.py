"""
Author: xiaohei
Date: 2022/7/7
Email: <EMAIL>
Host: xiaohei.info
"""
import importlib
import os

from instance.default import system_env
from queqiao.util.comm import osutil


def get_curr_app():
    pid = os.getpid()
    pcmd = osutil.get_pcmd(pid)
    print(f'get cmd [{pcmd}] for pid {pid}')
    if 'gunicorn' in pcmd:
        return 'api'
    elif 'wac' in pcmd:
        return 'wac'
    elif 'pytest' in pcmd:
        return 'console'
    else:
        return 'exe'


curr_queqiao_app = get_curr_app()
system_default_config = importlib.import_module(f'instance.default')
system_env_config = importlib.import_module(f'instance.env.{system_env}')


class EnvConfig:
    @classmethod
    def get(cls, key, default=None):
        if hasattr(system_env_config, key):
            return getattr(system_env_config, key)
        if hasattr(system_default_config, key):
            return getattr(system_default_config, key)
        return default
