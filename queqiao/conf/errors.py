from requests import ReadTimeout


class BaseQueqiaoException(Exception):
    pass


# ==base
class InternalException(BaseQueqiaoException):
    pass


class ExternalException(BaseQueqiaoException):
    pass


# ==interval

class NotFoundException(InternalException):
    pass


class NotAvaliableException(InternalException):
    pass


class IllegalParamsException(InternalException):
    pass


class NoPrivilegeException(InternalException):
    pass


class ExecutionKilledException(InternalException):
    pass


# ==external

class AuthFailedException(ExternalException):
    pass


class CantorRequestFailedException(ExternalException):
    pass


class OrgRequestFailedException(ExternalException):
    pass


class ExecuteFailedException(ExternalException):
    pass


class SqlExecuteFailedException(ExecuteFailedException):
    pass


class CmdExecuteFailedException(ExecuteFailedException):
    pass


# ===alarm
class AlarmException(BaseQueqiaoException):
    pass


# ==success
class SuccessFinishException(BaseQueqiaoException):
    pass


class SuccessAndClearExecutionException(SuccessFinishException):
    pass


class FtpfileHasBeenImportedException(SuccessFinishException):
    pass


# ==

class BaseExtError(Exception):
    pass


class HttpResultError(BaseExtError):
    pass


class HttpRequestError(BaseExtError):
    pass


NETWORK_ERRORS = ReadTimeout, ConnectionError
