"""
Author: xiaohei
Date: 2022/4/19
Email: <EMAIL>
Host: xiaohei.info
"""
import json

from queqiao.conf.errors import IllegalParamsException


class Config:
    def __init__(self, dict_params):
        if not dict_params or not isinstance(dict_params, dict):
            raise IllegalParamsException(f'dict_params must be a dict')
        for k in dict_params.keys():
            v = dict_params[k]
            if isinstance(v, dict):
                setattr(self, k, Config(v))
            else:
                setattr(self, k, v)

    @classmethod
    def to_dict(cls, config):
        result = {}
        for i in dir(config):
            if i.startswith('_') or i.endswith('_') or 'to_dict' in i:
                continue
            v = getattr(config, i)
            iv = v if not isinstance(v, Config) else cls.to_dict(v)
            result[i] = iv
        return result

    def __str__(self):
        return json.dumps(Config.to_dict(self))
