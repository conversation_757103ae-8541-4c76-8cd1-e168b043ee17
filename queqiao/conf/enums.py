"""
Author: xiaohei
Date: 2022/5/20
Email: <EMAIL>
Host: xiaohei.info
"""
from enum import Enum, unique


@unique
class CompressType(Enum):
    NONE = 'none'
    ZIP = 'zip'
    GZIP = 'gzip'
    GZ = 'gz'
    TARGZ = 'tar.gz'
    Z7 = '7z'

    def __str__(self):
        return self.name


# 1:prepare,2:executing,3:suc,4:except
@unique
class ExecutionStatus(Enum):
    # execution创建
    INIT = 101
    QUEUED = 102
    # execution doing action
    RUNNING_PRE = 201
    READING = 202
    WRITING = 203
    RUNNING_POST = 204
    RETRY = 205
    SUCCESS = 301
    FAILED = 401
    KILLED = 402

    def __str__(self):
        return self.name


@unique
class ApplyStatus(Enum):
    APPROVING = 0
    AGREE = 1
    REJECT = -1

    def __str__(self):
        return self.name


@unique
class TaskStatus(Enum):
    # SUCCESS=ONLINE
    OFFLINE = -1
    APPROVING = 0
    SUCCESS = 301
    RUNNING = 201
    FAILED = 401

    def __str__(self):
        return self.name


@unique
class TaskPriority(Enum):
    EMERGENCY = 0
    HIGH = 1
    MEDIUM = 3
    LOW = 5
    DEFAULT = 9

    def __str__(self):
        return self.name


@unique
class TaskQueue(Enum):
    NORMAL = 'normal'
    BIGTASK = 'bigtask'
    TIMING = 'timing'

    def __str__(self):
        return self.name


@unique
class LinuxPermission(Enum):
    READ = 4
    WRITE = 2
    EXECUTE = 1
    READ_WRITE_EXECUTE = READ + WRITE + EXECUTE
    READ_WRITE = READ + WRITE
    READ_EXECUTE = READ + EXECUTE
    WRITE_EXECUTE = WRITE + EXECUTE

    def __str__(self):
        return self.name


@unique
class PermissionStatus(Enum):
    ENTABLE = 1
    APPROVING = 0
    REJECT = -1

    def __str__(self):
        return self.name


@unique
class APPROVER_TYPE(Enum):
    DAXIANG = 'Daxiang'
    INNER = 'Inner'

    def __str__(self):
        return self.name


@unique
class PUSHER_TYPE(Enum):
    DAXIANG = 'Daxiang'
    FILE = 'File'

    def __str__(self):
        return self.name


@unique
class SCHEDULER_TYPE(Enum):
    CANTOR = 'Cantor'
    AZKABAN = 'Azkaban'

    def __str__(self):
        return self.name


@unique
class TransType(Enum):
    SCHEDULE = 1
    SINGLE = 0

    def __str__(self):
        return self.name


@unique
class ProjectRoleType(Enum):
    ADMIN = 1
    USER = 0

    def __str__(self):
        return self.name


@unique
class HiveTableType(Enum):
    FULL = 0
    PARTITION_WITHOUT_KV = 1
    PARTITION_WITH_KV = 2
    PARTITION_WITH_K_V_FROM_FILE = 3
    PARTITION_WITH_K_V_FROM_DEFAULT = 4
    PARTITION_WITH_V_K_FROM_DEFAULT = 5

    def __str__(self):
        return self.name
