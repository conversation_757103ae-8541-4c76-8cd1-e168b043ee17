"""
Author: xiaohei
Date: 2022/5/24
Email: <EMAIL>
Host: xiaohei.info
"""
from flask import request

from queqiao.conf.ApiResponse import SUCCESS, RESOURCE_NOT_FOUND
from queqiao.conf.enums import ApplyStatus, TaskStatus, LinuxPermission, PermissionStatus
from queqiao.core.auth.entity import login_required
from queqiao.core.execute.apply import Apply
from queqiao.dba.models import TaskType, TaskConfig, TaskPermission
from queqiao.lib.approve import Approver
from queqiao.log import LogFactory
from queqiao.log.util import calc_elapsed
from queqiao.service.v1 import regist_api
from queqiao.util.comm import requtil, strutil
from queqiao.util.comm.requtil import params_required, params_optional

api = regist_api(__name__)

required_fileds, optional_fields = Apply.get_required_and_optional_fields()

log = LogFactory.get_logger()


@api.route('/', methods=['POST'])
@login_required
@params_required(*list(set(required_fileds).difference(['status', 'project_id'])), 'tasks')
def create_apply(user, **params):
    information = requtil.get_request_param(request, 'information', '')
    exec_immediately_tasks = []
    tasks = params.pop('tasks')
    with calc_elapsed('create apply with task/task_permission/task_configs', log):
        apply = Apply.new(status=ApplyStatus.APPROVING.value, create_user=user.uid, update_user=user.uid,
                          project_id=user.project_id, information=information, **params)
        apply.save()

        try:
            for task_info in tasks:
                source_configs = task_info.pop('source')
                sink_configs = task_info.pop('sink')
                exec_immediately = task_info.pop('exec_immediately')

                source_id = source_configs.pop('cid')
                sink_id = sink_configs.pop('cid')
                task_type = TaskType.get(source_id=source_id, sink_id=sink_id, only_one=1)
                if not task_type:
                    apply.rollback()
                    return RESOURCE_NOT_FOUND(detail=f'source_id={source_id},sink_id={sink_id} 的任务类型')
                # if '.' not in task_info['name']:
                #     apply.rollback()
                #     return ILLEGAL_PARAMS(detail=f'task name {task_info["name"]}, must be: project.name')
                task_params = {
                    'create_user': user.uid,
                    'update_user': user.uid,
                    'name': task_info['name'],
                    'trans_type': task_info['trans_type'],
                    'status': TaskStatus.APPROVING.value,
                    'apply_id': apply.id,
                    'task_type_id': task_type.id,
                    'engine_id': task_info['engine_id'],
                    'project_id': user.project_id,

                }
                if task_info.get('alarm_receivers', None):
                    task_params['alarm_receivers'] = task_info['alarm_receivers']
                if task_info.get('params', None):
                    task_params['params'] = task_info['params']
                from queqiao.core.execute.task import Task
                task = Task.new(**task_params)
                task.save()
                task_permission = TaskPermission.new(task_id=task.id, user_id=user.uid,
                                                     permission=LinuxPermission.READ_WRITE_EXECUTE.value,
                                                     status=PermissionStatus.ENTABLE.value)
                task_permission.save()
                if task.alarm_receivers:
                    for receiver in task.alarm_receivers.split(','):
                        if receiver == user.uid:
                            continue
                        task_permission = TaskPermission.new(task_id=task.id, user_id=receiver,
                                                             permission=LinuxPermission.READ_EXECUTE.value,
                                                             status=PermissionStatus.ENTABLE.value)
                        task_permission.save()

                for key in source_configs.keys():
                    value = source_configs[key]
                    task_config = TaskConfig.new(create_user=user.uid, update_user=user.uid, task_id=task.id,
                                                 cid=source_id, key=key, value=value)
                    task_config.save()
                for key in sink_configs.keys():
                    value = sink_configs[key]
                    task_config = TaskConfig.new(create_user=user.uid, update_user=user.uid, task_id=task.id,
                                                 cid=sink_id, key=key, value=value)
                    task_config.save()

                # todo: 前端增加选项框
                if exec_immediately:
                    exec_immediately_tasks.append(task.id)

            if exec_immediately_tasks:
                information = strutil.hocon_loads(information)
                information['exe_after_approve_tasks'] = ','.join(exec_immediately_tasks)
                information = strutil.hocon_dumps(information)
                apply.information = information
                apply.save()

            approver = Approver.get_approver(apply)
            approver.create()
        except Exception as why:
            apply.rollback()
            raise why
    return SUCCESS(data={'id': apply.id})


@api.route('/progress/<int:id>', methods=['GET'])
@login_required
def apply_progress(user, id):
    apply = Apply.get(id=id)
    approver = Approver.get_approver(apply)
    detail = approver.detail()
    return SUCCESS(data=detail)


@api.route('/push/<int:id>', methods=['GET'])
@login_required
def push_apply(user, id):
    apply = Apply.get(id=id)
    approver = Approver.get_approver(apply)
    approver.hurry_up()
    return SUCCESS()


@api.route('/<int:id>', methods=['GET'])
@login_required
def get_apply(user, id):
    apply = Apply.get(id=id)
    return SUCCESS(data=apply.to_dict())


@api.route('/agree/<int:id>', methods=['GET'])
@login_required
def agree_apply(user, id):
    Apply.agree(id, user.uid)
    return SUCCESS()


@api.route('/reject/<int:id>', methods=['GET', 'POST'])
@login_required
@params_optional('comment')
def reject_apply(user, id, comment=None):
    Apply.reject(id, user.uid, comment)
    return SUCCESS()
