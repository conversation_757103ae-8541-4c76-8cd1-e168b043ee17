"""
Author: xiaohei
Date: 2023/1/14
Email: <EMAIL>
Host: xiaohei.info
"""
from flask import request

from queqiao.conf.ApiResponse import RESOURCE_NOT_AVAILABLE, SUCCESS, RESOURCE_NOT_FOUND
from queqiao.core.auth.entity import login_required
from queqiao.dba.models import Dsn
from queqiao.log import LogFactory
from queqiao.service import update_db_obj, add_db_obj
from queqiao.service.v1 import regist_api
from queqiao.util.comm.requtil import params_required

api = regist_api(__name__)

log = LogFactory.get_logger()

required_fileds, optional_fields = Dsn.get_required_and_optional_fields()


@api.route('/', methods=['GET'])
@login_required
def get_all_dsns(user):
    dsns = Dsn.get()
    log.info(f'user {user.uid} get {len(dsns)} dsns')
    return SUCCESS(data=dsns)


@api.route('/<int:id>', methods=['GET'])
@login_required
def get_dsn(user, id):
    dsn = Dsn.get(id=id)
    if not dsn:
        return RESOURCE_NOT_FOUND(detail=f'dsn id {id}')
    return SUCCESS(data=dsn)


@api.route('/<string:name>', methods=['GET'])
def get_dsn_by_name(name):
    dsn = Dsn.get_one(name=name)
    if not dsn:
        return RESOURCE_NOT_FOUND(detail=f'dsn {name}')
    return SUCCESS(data=dsn)


@api.route('/', methods=['POST'])
@login_required
@params_required(*required_fileds)
def add_dsn(user, **params):
    return add_db_obj(request, user, Dsn, params, optional_fields, f'{params["name"]} dsn名')


@api.route('/<int:id>', methods=['POST'])
@login_required
def update_dsn(user, id):
    dsn = Dsn.get(id=id)
    if dsn and dsn.create_time.year < 2023:
        return RESOURCE_NOT_AVAILABLE(detail=f'创建时间在2023年之前的dsn {id} 不可更新，请联系管理员！')
    return update_db_obj(request, user, dsn, required_fileds, optional_fields, f'{id} dsn')
