"""
Author: xiaohei
Date: 2022/6/14
Email: <EMAIL>
Host: xiaohei.info
"""
from flask import request

from instance.default import TMP_FOLDER, SYS_ADMIN
from queqiao.conf.ApiResponse import ILLEGAL_PARAMS, SUCCESS, INTERVAL_ERROR, RESOURCE_NOT_FOUND, MISSING_PARAMS, \
    RESOURCE_EXISTS
from queqiao.conf.enums import TaskStatus, TransType, LinuxPermission, PermissionStatus
from queqiao.conf.system import SystemConfig
from queqiao.core.auth.entity import login_required
from queqiao.core.execute.task import Task
from queqiao.dba.extend_model import TaskPermission
from queqiao.dba.models import Component, TaskConfig, TaskType, Engine, Project, Dsn
from queqiao.lib.schedule import Scheduler
from queqiao.log import LogFactory
from queqiao.log.util import calc_elapsed
from queqiao.service.v1 import regist_api, check_task_permission
from queqiao.util.comm import fileutil, strutil
from queqiao.util.comm.requtil import params_required, params_optional, params_optional_none, get_request_param

api = regist_api(__name__)

log = LogFactory.get_logger()

required_fileds, optional_fields = Task.get_required_and_optional_fields()

docfile_suffixs = ['doc', 'docx']


@api.route('/doc/parse', methods=['POST'])
@params_required('docfile')
def parse_doc(docfile):
    suffix = docfile.filename.split('.')[-1]
    if suffix not in docfile_suffixs:
        return ILLEGAL_PARAMS(detail=f'the correct suffix is {docfile_suffixs}, {docfile.filename} suffix is illegal')
    local_file = fileutil.save_to_local(docfile, TMP_FOLDER)
    # todo: 解析本地word文件
    return SUCCESS()


@api.route('/doc/download', methods=['GET'])
@params_required('source', 'sink')
def download_doc(source, sink):
    source_component = Component.get(datasource=source, operator='source', only_one=1)
    sink_component = Component.get(datasource=sink, operator='sink', only_one=1)
    # todo: 组装组件参数并写入word
    return SUCCESS()


@api.route('/doc/download/<int:id>', methods=['GET'])
@login_required
@check_task_permission('read')
def download_doc_by_id(task, user, id):
    # todo: 组装组件参数并写入word
    return SUCCESS()


@api.route('/', methods=['GET'])
@login_required
def get_tasks(user):
    tasks = Task.get_tasks_by_user(user)
    result = [task.to_dict() for task in tasks]
    log.info(f'user {user.uid} get {len(tasks)} tasks')
    return SUCCESS(data=result)


@api.route('/search', methods=['POST'])
@login_required
@params_optional('create_user', 'name', 'trans_type', 'status', 'apply_id', 'task_type_id', 'engine_id', 'project_id',
                 'create_time', 'source_org_id', 'sink_org_id')
def search_tasks(user, **params):
    tasks = Task.get_tasks_by_user(user)
    result = []
    for task in tasks:
        passed_cnt = 0
        log.info(f'params: {params}')
        for key in params.keys():
            value = params[key]
            db_value = getattr(task, key) if key != 'create_time' else str(getattr(task, key)).split(' ')[0]
            log.info(f'value:{value},db_value:{db_value}')
            if db_value == value:
                passed_cnt += 1
        log.info(f'passed_cnt:{passed_cnt}, params_cnt: {len(params)}')
        if passed_cnt == len(params):
            result.append(task.to_dict())
    log.info(f'user {user.uid} get {len(tasks)} tasks by params: {params}')
    return SUCCESS(data=result)


@api.route('/<int:id>', methods=['GET'])
@login_required
@check_task_permission('read')
def get_task(user, id, task):
    return SUCCESS(task.to_dict(component_configs=True, task_permissions=True))


@api.route('/<string:name>', methods=['GET'])
def get_task_by_name(name):
    task = Task.get_one(name=name)
    if not task:
        return RESOURCE_NOT_FOUND(detail=f'task {name}')
    return SUCCESS(task.to_dict(component_configs=True, task_permissions=True))


@api.route('/<int:id>', methods=['POST'])
@login_required
@check_task_permission('write')
@params_optional('name', 'tran_type', 'alarm_receivers', 'params', 'engine_id', 'source_configs', 'sink_configs')
def update_task(user, id, task, **params):
    def update_config(operator):
        if operator in params:
            configs = params.pop(operator)
            cid = configs.pop('cid')
            for key in configs.keys():
                task_config = TaskConfig.get_one(task_id=id, cid=cid, key=key)
                task_config.value = configs[key]
                task_config.update_user = user.uid
                task_config.save()

    with calc_elapsed('update_task_config', log):
        update_config('source_configs')
        update_config('sink_configs')

    for key in params.keys():
        setattr(task, key, params[key])
    task.update_user = user.uid
    task.save()
    return SUCCESS(data={'id': id})


@api.route('/offline/<int:id>', methods=['GET'])
@login_required
@check_task_permission('write')
def task_offline(user, id, task):
    task.status = TaskStatus.OFFLINE.value
    task.update_user = user.uid
    task.save()
    return SUCCESS()


@api.route('/online/<int:id>', methods=['GET'])
@login_required
@check_task_permission('write')
def task_online(user, id, task):
    task.status = TaskStatus.SUCCESS.value
    task.update_user = user.uid
    task.save()
    return SUCCESS()


@api.route('/cmd/<int:id>', methods=['GET'])
@params_optional_none('etl_system')
def get_task_cmd(id, etl_system):
    try:
        task = Task.get(id=id)
        scheduler = Scheduler.get_scheduler(task, etl_system)
    except Exception as why:
        return INTERVAL_ERROR(detail=str(why))
    return SUCCESS(data=scheduler.gen_cmd())


@api.route('/apply/<int:id>', methods=['GET'])
@login_required
@check_task_permission('read')
def get_task_apply(user, id, task):
    result = task.apply.to_dict(with_tasks=False)
    return SUCCESS(data=result)


@api.route('/exec/<string:name>', methods=['GET'])
@login_required
@check_task_permission('execute')
def execute_task(user, name, task):
    req_args = request.args.to_dict()
    log.info(f'get req_args: {req_args}')
    params = strutil.hocon_loads(req_args['params']) if 'params' in req_args else {}
    delay = int(params['async']) if 'async' in params else 1
    delay = delay == 1
    log.info(f'user {user.uid} execute task {name} with delay: {delay}, params: {params}')
    if 'etl_date' in req_args:
        params['current'] = req_args.get('etl_date')
    task.update_user = user.uid
    task.save()
    execution_id = Task.execute(name, params, delay=delay)
    return SUCCESS(data={'execution_id': execution_id})


@api.route('/execution/<int:id>', methods=['GET'])
@login_required
@check_task_permission('read')
def get_executions(user, id, task):
    result = []
    for execution in task.executions:
        result.append(execution.to_dict())
    return SUCCESS(data=result)


@api.route('/etl/<string:name>', methods=['GET'])
def execute_etl_task(name):
    req_args = request.args.to_dict()
    log.info(f'get req_args: {req_args}')
    params = strutil.hocon_loads(req_args['params']) if 'params' in req_args else {}
    delay = True
    log.info(f'execute task {name} with delay from etlsystem: {delay}, params: {params}')
    if 'etl_date' in req_args:
        params['current'] = req_args.get('etl_date')
    execution_id = Task.execute(name, params, delay=delay)
    return SUCCESS(data={'execution_id': execution_id})


@api.route('/create', methods=['POST'])
@params_required('whoami', 'engine', 'name', 'type', 'source_configs', 'sink_configs', 'project')
def create_task(whoami, engine, name, type, source_configs, sink_configs, project):
    project_name = project
    project = Project.get_one(name=project_name)
    if not project:
        log.info(f'project {project_name} not found, auto create')
        project = Project.new(name=project_name, desc='auto create in quick task create api', admins=SYS_ADMIN,
                              create_user='quick_create_api', update_user='quick_create_api')
        project.save()
    return create_task_quick(whoami, engine, name, type, source_configs, sink_configs, project.name)


@api.route('/tmp', methods=['POST'])
@params_required('whoami', 'engine', 'name', 'type', 'source_configs', 'sink_configs')
def create_tmp_task(whoami, engine, name, type, source_configs, sink_configs):
    return create_task_quick(whoami, engine, name, type, source_configs, sink_configs, project='tmp')


def create_task_quick(whoami, engine, name, type, source_configs, sink_configs, project):
    task_type = TaskType.get_one(name=type)
    engine = Engine.get_one(name=engine)
    from queqiao.core.execute.task import Task
    if not task_type:
        return RESOURCE_NOT_FOUND(detail=f'任务类型 {type}')
    if not engine:
        return RESOURCE_NOT_FOUND(detail=f'执行引擎 {engine}')
    if 'dsn' not in source_configs or 'dsn' not in sink_configs:
        return MISSING_PARAMS(detail=f'source_configs/sink_configs 中的 dsn 信息')

    project_name = project
    project = Project.get_one(name=project_name)
    if not project:
        return RESOURCE_NOT_FOUND(detail=f'项目 {project_name}')
    if Task.exists(name=name, project_id=project.id):
        return RESOURCE_EXISTS(detail=f'任务 {project.name}.{name}')
    task_params = {
        'create_user': whoami,
        'update_user': whoami,
        'name': name,
        'trans_type': TransType.SINGLE.value,
        'status': TaskStatus.SUCCESS.value,
        'apply_id': -1,
        'task_type_id': task_type.id,
        'engine_id': engine.id,
        'project_id': project.id,
        'alarm_receivers': SystemConfig.read('SYS_ADMIN')

    }
    queue = get_request_param(request, 'queue')
    if queue:
        task_params['queue'] = queue
    task = Task.new(**task_params)
    task.save()
    task_permission = TaskPermission.new(task_id=task.id, user_id=SystemConfig.read('SYS_ADMIN'),
                                         permission=LinuxPermission.READ_WRITE_EXECUTE.value,
                                         status=PermissionStatus.ENTABLE.value)
    task_permission.save()

    for key in source_configs.keys():
        value = source_configs[key]
        if key == 'dsn':
            dsn = Dsn.get_one(name=value)
            if not dsn:
                return RESOURCE_NOT_FOUND(detail=f'dsn {value}')
            value = dsn.id
        task_config = TaskConfig.new(create_user=whoami, update_user=whoami, task_id=task.id,
                                     cid=task_type.source_id, key=key, value=value)
        task_config.save()
    for key in sink_configs.keys():
        value = sink_configs[key]
        if key == 'dsn':
            dsn = Dsn.get_one(name=value)
            if not dsn:
                return RESOURCE_NOT_FOUND(detail=f'dsn {value}')
            value = dsn.id
        task_config = TaskConfig.new(create_user=whoami, update_user=whoami, task_id=task.id,
                                     cid=task_type.sink_id, key=key, value=value)
        task_config.save()
    return SUCCESS(data=task.to_dict())
