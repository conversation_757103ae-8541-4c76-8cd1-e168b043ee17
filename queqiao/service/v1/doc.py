"""
Author: xiaohei
Date: 2025/1/20
Email: <EMAIL>
Host: xiaohei.info
"""
import os
import markdown
from flask import render_template_string

from queqiao.conf.ApiResponse import SUCCESS, RESOURCE_NOT_FOUND
from queqiao.log import LogFactory
from queqiao.service.v1 import regist_api

api = regist_api(__name__)
log = LogFactory.get_logger()

# HTML模板,包含代码高亮样式和导航样式
HTML_TEMPLATE = '''
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Queqiao API Documentation</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/github-markdown-css/5.2.0/github-markdown.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.7.0/styles/github.min.css">
    <script src="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.7.0/highlight.min.js"></script>
    <style>
        body {
            background-color: #ffffff;
        }
        .markdown-body {
            box-sizing: border-box;
            min-width: 200px;
            max-width: 980px;
            margin: 0 auto;
            padding: 45px;
            background-color: #ffffff;
        }
        @media (max-width: 767px) {
            .markdown-body {
                padding: 15px;
            }
        }
        /* 代码块样式 */
        .markdown-body pre {
            background-color: #f6f8fa;
            border-radius: 6px;
            padding: 16px;
            overflow: auto;
        }
        .markdown-body pre code {
            background-color: transparent;
            color: #24292e;
            font-family: SFMono-Regular, Consolas, "Liberation Mono", Menlo, monospace;
            font-size: 14px;
            line-height: 1.5;
        }
        /* JSON语法高亮 */
        .hljs-string { color: #032f62; }
        .hljs-number { color: #005cc5; }
        .hljs-literal { color: #005cc5; }
        .hljs-keyword { color: #d73a49; }
        .hljs-attr { color: #6f42c1; }
        /* 内联代码样式 */
        .markdown-body code:not(pre code) {
            background-color: rgba(27,31,35,0.05);
            border-radius: 3px;
            font-size: 85%;
            margin: 0;
            padding: 0.2em 0.4em;
        }
        .api-category {
            margin: 10px 0;
            padding: 10px;
            border: 1px solid #e1e4e8;
            border-radius: 6px;
            transition: background-color 0.2s;
        }
        .api-category:hover {
            background-color: #f6f8fa;
        }
        .api-category a {
            color: #0366d6;
            text-decoration: none;
            font-size: 16px;
            font-weight: 600;
        }
        .api-category p {
            margin: 5px 0 0 0;
            color: #586069;
        }
        .back-link {
            margin-bottom: 20px;
            display: inline-block;
            color: #0366d6;
            text-decoration: none;
        }
        .back-link:hover {
            text-decoration: underline;
        }
        /* 优化表格样式 */
        .markdown-body table {
            display: table;
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
            font-size: 14px;
            text-align: left;
        }
        .markdown-body table th {
            background-color: #f6f8fa;
            font-weight: 600;
            padding: 12px 8px;
            border: 1px solid #dfe2e5;
        }
        .markdown-body table td {
            padding: 12px 8px;
            border: 1px solid #dfe2e5;
            word-break: break-word;
        }
        .markdown-body table tr:nth-child(2n) {
            background-color: #fafbfc;
        }
        .markdown-body table tr:hover {
            background-color: #f6f8fa;
        }
        /* 设置特定列的宽度 */
        .markdown-body table th:first-child,
        .markdown-body table td:first-child {
            width: 20%;
        }
        .markdown-body table th:nth-child(2),
        .markdown-body table td:nth-child(2) {
            width: 15%;
        }
        .markdown-body table th:nth-child(3),
        .markdown-body table td:nth-child(3) {
            width: 10%;
        }
        .markdown-body table th:last-child,
        .markdown-body table td:last-child {
            width: auto;
        }
    </style>
</head>
<body>
    <article class="markdown-body">
        {% if show_back_link %}
        <a href="/" class="back-link">← 返回接口总览</a>
        {% endif %}
        {{ content|safe }}
    </article>
    <script>hljs.highlightAll();</script>
</body>
</html>
'''

# API分类信息
API_CATEGORIES = [
    {
        "id": "task",
        "name": "任务管理",
        "description": "任务的创建、查询、执行等相关接口"
    },
    {
        "id": "project",
        "name": "项目管理",
        "description": "项目的创建、更新、删除等相关接口"
    },
    {
        "id": "org",
        "name": "组织管理",
        "description": "组织的创建、查询等相关接口"
    },
    {
        "id": "dsn",
        "name": "数据源管理",
        "description": "数据源的配置、查询等相关接口"
    },
    {
        "id": "component",
        "name": "组件管理",
        "description": "组件的创建、配置等相关接口"
    },
    {
        "id": "component_config",
        "name": "组件配置",
        "description": "组件配置的管理接口"
    },
    {
        "id": "task_type",
        "name": "任务类型",
        "description": "任务类型的管理接口"
    },
    {
        "id": "task_permission",
        "name": "任务权限",
        "description": "任务权限的管理接口"
    },
    {
        "id": "apply",
        "name": "申请管理",
        "description": "任务申请的创建、审批等接口"
    },
    {
        "id": "execution",
        "name": "执行记录",
        "description": "任务执行记录的查询接口"
    },
    {
        "id": "history",
        "name": "历史记录",
        "description": "FTP文件历史记录接口"
    },
    {
        "id": "ftp",
        "name": "FTP服务",
        "description": "FTP服务相关接口"
    },
    {
        "id": "health",
        "name": "健康检查",
        "description": "系统健康检查相关接口"
    },
    {
        "id": "alarm",
        "name": "告警管理",
        "description": "系统告警相关接口"
    },
    {
        "id": "sso",
        "name": "单点登录",
        "description": "SSO认证相关接口"
    }
]

# 插件分类信息
PLUGIN_CATEGORIES = [
    {
        "id": "ftplink/source",
        "name": "数据源组件",
        "description": "数据源组件配置说明",
        "components": [
            {"id": "ftp", "name": "FTP源组件", "description": "FTP/SFTP远程文件服务源组件"},
            {"id": "wtp", "name": "WTP源组件", "description": "WTP远程文件服务源组件"},
            {"id": "mysql", "name": "MySQL源组件", "description": "MySQL数据库源组件"},
            {"id": "talos", "name": "Talos源组件", "description": "Talos消息队列源组件"},
            {"id": "talosctas", "name": "TalosCTAS源组件", "description": "Talos到Hive的数据同步组件"},
            {"id": "file", "name": "File源组件", "description": "本地文件源组件"},
            {"id": "fatetask", "name": "FateTask源组件", "description": "FATE任务源组件"},
            {"id": "http", "name": "HTTP源组件", "description": "HTTP/HTTPS数据源组件"},
            {"id": "hivesql", "name": "HiveSQL源组件", "description": "Hive SQL查询组件"},
            {"id": "fate", "name": "Fate源组件", "description": "FATE系统源组件"},
            {"id": "hive", "name": "Hive源组件", "description": "Hive数据仓库源组件"},
            {"id": "basehive", "name": "BaseHive源组件", "description": "基础Hive源组件"}
        ]
    }
]

def generate_overview_content():
    """生成API总览页面的内容"""
    content = "# Queqiao API 接口文档\n\n"
    content += "欢迎使用Queqiao API接口文档。请选择以下分类查看详细接口说明：\n\n"
    
    for category in API_CATEGORIES:
        content += f"## [{category['name']}](/api/v1/doc/category/{category['id']})\n"
        content += f"{category['description']}\n\n"
    
    return content

def generate_plugin_overview_content():
    """生成插件文档总览页面的内容"""
    content = "# Queqiao 插件配置文档\n\n"
    content += "欢迎使用Queqiao插件配置文档。请选择以下分类查看详细配置说明：\n\n"
    
    for category in PLUGIN_CATEGORIES:
        content += f"## {category['name']}\n"
        content += f"{category['description']}\n\n"
        
        for component in category['components']:
            content += f"### [{component['name']}](/api/v1/doc/plugin/{category['id']}/{component['id']})\n"
            content += f"{component['description']}\n\n"
    
    return content

def render_markdown_doc(doc_file, show_back_link=False):
    """渲染Markdown文档"""
    # 获取文档文件路径
    doc_path = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(__file__)))), 'docs', doc_file)
    
    # 检查文件是否存在
    if not os.path.exists(doc_path):
        return RESOURCE_NOT_FOUND(detail=f'Documentation file {doc_file} not found')
    
    try:
        # 读取markdown文件
        with open(doc_path, 'r', encoding='utf-8') as f:
            md_content = f.read()
        
        # 转换markdown为HTML
        html_content = markdown.markdown(
            md_content,
            extensions=[
                'markdown.extensions.fenced_code',  # 支持代码块
                'markdown.extensions.tables',       # 支持表格
                'markdown.extensions.toc',          # 支持目录
                'markdown.extensions.codehilite'    # 支持代码高亮
            ]
        )
        
        # 渲染HTML模板
        return render_template_string(HTML_TEMPLATE, content=html_content, show_back_link=show_back_link)
    
    except Exception as e:
        log.error(f'Error rendering documentation {doc_file}: {str(e)}')
        return RESOURCE_NOT_FOUND(detail=f'Error rendering documentation {doc_file}: {str(e)}')

@api.route('/', methods=['GET'])
def show_api_overview():
    """显示API总览页面"""
    content = generate_overview_content()
    return render_template_string(HTML_TEMPLATE, content=markdown.markdown(content, extensions=['markdown.extensions.tables']), show_back_link=False)

@api.route('/category/<category_id>', methods=['GET'])
def show_category_doc(category_id):
    """显示指定分类的API文档"""
    doc_path = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(__file__)))), 'docs', f'api/{category_id}.md')
    
    # 检查文件是否存在
    if not os.path.exists(doc_path):
        return RESOURCE_NOT_FOUND(detail=f'Documentation file api/{category_id}.md not found')
    
    try:
        # 读取markdown文件
        with open(doc_path, 'r', encoding='utf-8') as f:
            md_content = f.read()
        
        # 转换markdown为HTML,确保启用表格扩展
        html_content = markdown.markdown(
            md_content,
            extensions=[
                'markdown.extensions.fenced_code',  # 支持代码块
                'markdown.extensions.tables',       # 支持表格
                'markdown.extensions.toc',          # 支持目录
                'markdown.extensions.codehilite'    # 支持代码高亮
            ]
        )
        
        # 渲染HTML模板
        return render_template_string(HTML_TEMPLATE, content=html_content, show_back_link=True)
    
    except Exception as e:
        log.error(f'Error rendering documentation api/{category_id}.md: {str(e)}')
        return RESOURCE_NOT_FOUND(detail=f'Error rendering documentation api/{category_id}.md: {str(e)}')

@api.route('/plugins', methods=['GET'])
def show_plugin_doc():
    """显示插件配置文档总览"""
    content = generate_plugin_overview_content()
    return render_template_string(HTML_TEMPLATE, content=markdown.markdown(content, extensions=['markdown.extensions.tables']), show_back_link=True)

@api.route('/plugin/<path:plugin_path>', methods=['GET'])
def show_plugin_detail_doc(plugin_path):
    """显示指定插件的配置文档"""
    return render_markdown_doc(f'plugin/{plugin_path}.md', show_back_link=True)
