"""
Author: xiaohei
Date: 2022/6/13
Email: <EMAIL>
Host: xiaohei.info
"""
from queqiao.conf.ApiResponse import RESOURCE_NOT_FOUND, SUCCESS
from queqiao.dba.models import Alarm
from queqiao.log import LogFactory
from queqiao.service.v1 import regist_api
from queqiao.util.comm.requtil import params_required

api = regist_api(__name__)

log = LogFactory.get_logger()


@api.route('/keep_quiet/<int:id>', methods=['GET', 'POST'])
@params_required('keep_quiet_n_mins')
def keep_quiet(id, keep_quiet_n_mins):
    alarm = Alarm.get(id=id)
    if not alarm:
        return RESOURCE_NOT_FOUND(detail=f'alarm id: {id}')
    log.info(f' set alarm {id} keep quiet {keep_quiet_n_mins} mins')
    alarm.keep_quiet = keep_quiet_n_mins
    alarm.save()
    return SUCCESS()
