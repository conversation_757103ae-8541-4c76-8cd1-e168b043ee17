"""
Author: xiaohei
Date: 2022/6/13
Email: <EMAIL>
Host: xiaohei.info
"""
from instance.default import USER_AUTH_TYPE
from queqiao.conf.ApiResponse import SUCCESS
from queqiao.core import auth
from queqiao.core.execute.apply import Apply
from queqiao.log import LogFactory
from queqiao.service.v1 import regist_api
from queqiao.util.comm import objutil
from queqiao.util.comm.requtil import params_required

api = regist_api(__name__)

log = LogFactory.get_logger()
auth_module = f'{auth.__package__}.{USER_AUTH_TYPE.lower()}'
log.info(f'get auth_module: {auth_module}')
auth_module_obj = objutil.new_mdl_instance(auth_module)


@api.route('/login', methods=['GET', 'POST'])
def login():
    return getattr(auth_module_obj, 'login')()


@api.route('/logincallback', methods=['GET', 'POST'])
def logincallback():
    return getattr(auth_module_obj, 'logincallback')()


@api.route('/logout', methods=['GET', 'POST'])
def logout():
    return getattr(auth_module_obj, 'logout')()


# prod http://************:8090/api/v1/sso/auditcallback
# test http://10.199.132.129:8090/api/v1/sso/auditcallback
@api.route('/auditcallback', methods=['POST'])
@params_required('sponsor', 'status', 'values')
def audit_callback(sponsor, status, values):
    log.info(f'receive callback request from XmApproval, sponsor: {sponsor}, status: {status}, value: {values}')
    for value in values:
        if value['seq'] == 1:
            apply_id = int(value['num'])
    log.info(f'get apply_id: {apply_id}')
    '''
0初始状态
1审批中
2撤回
3通过
4拒绝
    '''
    if status == 3:
        Apply.agree(apply_id, sponsor)
    elif status == 4:
        Apply.reject(apply_id, sponsor, comment='请在大象快审上查看审批拒绝原因！')
    else:
        log.info('apply does not approved! ignore current callback')
    return SUCCESS(data={'status': 0})
