"""
Author: xia<PERSON><PERSON>
Date: 2022/8/17
Email: <EMAIL>
Host: xiaohei.info
"""
from flask import request

from queqiao.conf.ApiResponse import SUCCESS, NO_PRIVILEGE, RESOURCE_EXISTS
from queqiao.conf.enums import ProjectRoleType
from queqiao.core.auth.entity import login_required
from queqiao.dba.models import Project, ProjectUserRelation
from queqiao.log import LogFactory
from queqiao.service import update_db_obj
from queqiao.service.v1 import regist_api
from queqiao.util.comm.requtil import params_required, get_request_param, params_optional_none

api = regist_api(__name__)

log = LogFactory.get_logger()

required_fileds, optional_fields = Project.get_required_and_optional_fields()


# 所有人都可以查看、添加、删除项目(检验权限)，可以申请加入项目，管路员可以添加或者移除成员
@api.route('/search', methods=['POST'])
@login_required
@params_optional_none('page', 'query')
def search_projects(user, page, query):
    log.info(f'page: {page}, query: {query}')
    project_pages = Project.get_paginate(**page, **query)
    log.info(f'user {user.uid} get {project_pages["limit"]} ')
    return SUCCESS(data=project_pages)


# @api.route('/<int:id>', methods=['GET'])
# @login_required
# def get_project(user, id):
#     return SUCCESS(data=Project.get(id=id))


@api.route('/add', methods=['POST'])
@login_required
@params_required(*required_fileds)
def add_project(user, **params):
    # return add_db_obj(request, user, Project, params, optional_fields)
    required_params = params
    for key in optional_fields:
        value = get_request_param(request, key)
        if value is not None:
            required_params[key] = value
    if Project.exists(name=required_params['name']):
        return RESOURCE_EXISTS(detail=f'项目名 {required_params["name"]}')
    log.info(f'user {user.uid} add project with params: {required_params}')
    required_params['create_user'] = user.uid
    required_params['update_user'] = user.uid
    project = Project.new(**required_params)
    project.save()
    if project.admins:
        for a in project.admins.split(','):
            ur = ProjectUserRelation.new(project_id=project.id, user_id=a, role_type=ProjectRoleType.ADMIN.value)
            ur.save()
    return SUCCESS(data={'id': project.id, 'admins': project.admin_list})


@api.route('/<int:id>', methods=['POST'])
@login_required
def update_project(user, id):
    project = Project.get(id=id)
    if not project.is_admin(user.uid):
        return NO_PRIVILEGE()
    return update_db_obj(request, user, project, required_fileds, optional_fields, f'project {id}')


@api.route('/<int:id>', methods=['DELETE'])
@login_required
def delete_project(user, id):
    project = Project.get(id=id)
    if not project.is_admin(user.uid):
        return NO_PRIVILEGE()
    project.safety_delete()
    log.info(f'user {user.uid} delete project {project.name}')
    return SUCCESS()


def join_project(user, id):
    project = Project.get(id=id)
    return SUCCESS()


def exit_project(user, id):
    project = Project.get(id=id)
    return SUCCESS()


def add_user(user, id, uid):
    project = Project.get(id=id)
    return SUCCESS()


def del_user(user, id, uid):
    project = Project.get(id=id)
    return SUCCESS()
