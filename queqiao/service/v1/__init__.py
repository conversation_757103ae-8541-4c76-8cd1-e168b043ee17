"""
Author: xiaohei
Date: 2022/5/16
Email: <EMAIL>
Host: xiaohei.info
"""
from functools import wraps

from flask import Blueprint, request

from queqiao.conf.ApiResponse import RESOURCE_NOT_FOUND, NO_PRIVILEGE, MISSING_PARAMS, ILLEGAL_PARAMS
from queqiao.dba.models import Project
from queqiao.log import LogFactory

log = LogFactory.get_logger()

URL_PREFIX = '/api/v1'


def regist_api(mdl_name):
    mdl_sname = mdl_name.replace(f'{__name__}.', '')
    mdlp_sname_p = mdl_sname.replace('_', '/')
    api_name = f'{URL_PREFIX}/{mdlp_sname_p}'.replace("/", "_")
    log.info(f'register api: {api_name}, url_prefix: {URL_PREFIX}/{mdlp_sname_p}, from module: {mdl_name}')
    return Blueprint(api_name, mdl_sname, url_prefix=f'{URL_PREFIX}/{mdlp_sname_p}')


def check_task_permission(permission):
    def decorate(f):
        @wraps(f)
        def wrapper(*args, **kwargs):
            from queqiao.dba.extend_model import Task
            user = request.user
            print(f'args: {args}, kwargs: {kwargs}')

            if 'id' not in kwargs and 'name' not in kwargs:
                return MISSING_PARAMS(detail=f'task id or name')
            if 'id' in kwargs:
                task_key = {'id': kwargs['id']}
            else:
                name_arr = kwargs['name'].split('.')
                if len(name_arr) != 2:
                    return ILLEGAL_PARAMS(detail=f'name must be $project.$name, current is {kwargs["name"]}')
                project = Project.get_one(name=name_arr[0])
                if not project:
                    return RESOURCE_NOT_FOUND(detail=f'can not found project by name: {name_arr[0]}')
                task_key = {'name': name_arr[1], 'project_id': project.id}

            task = Task.get_one(**task_key)
            if not task:
                return RESOURCE_NOT_FOUND(detail=f'task {task_key}')
            if not task.check_permission(user, permission):
                return NO_PRIVILEGE()
            kwargs['task'] = task
            return f(*args, **kwargs)

        return wrapper

    return decorate
