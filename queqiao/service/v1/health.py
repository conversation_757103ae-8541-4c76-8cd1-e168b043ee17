"""
Author: xiaohei
Date: 2022/8/5
Email: <EMAIL>
Host: xiaohei.info
"""

# from celery.result import AsyncResult

from queqiao.conf.ApiResponse import SUCCESS
# from queqiao.dba.models import Project
from queqiao.log import LogFactory
from queqiao.service.v1 import regist_api

from queqiao.util.comm.requtil import params_required
from queqiao.util.mt.talos import TalosClient

api = regist_api(__name__)

log = LogFactory.get_logger()


@api.route('/check', methods=['GET'])
def health_check():
    return SUCCESS()


@api.route('/alert', methods=['GET', 'POST'])
@params_required('msg', 'receivers')
def alarm_msg(msg, receivers):
    from queqiao.lib.push import Pusher
    pusher = Pusher.get_pusher()
    pusher.push(msg, receivers)
    return SUCCESS()


@api.route('/talos/table/schema', methods=['GET', 'POST'])
@params_required('name')
def talos_table_schema(name):
    client = TalosClient(logger=log)
    client.open()
    schema = client.schema(name)
    client.close()
    return SUCCESS(data=schema)

# @api.route('/async/submit', methods=['GET'])
# @params_required('project_id', 'project_name', 'sleep_time', 'wait')
# def async_submit(project_id, project_name, sleep_time, wait):
#     wait = int(wait) == 1
#     result = _async_do_task.delay(project_id, project_name, sleep_time)
#     if not wait:
#         return SUCCESS(data={'id': result.id})
#     while not result.ready():
#         logger.info(f'task {result.id} not ready, current state: {result.state}')
#         time.sleep(1)
#     if result.failed():
#         return EXECUTE_FAILED(data=f'task {result.id} execute failed, detail: {result.traceback}')
#     if result.successful():
#         return SUCCESS(data={'id': result.id, 'result': result.get(timeout=1, propagate=False)})


# @api.route('/async/kill', methods=['GET'])
# @params_required('task_id')
# def async_kill(task_id):
#     result = AsyncResult(task_id)
#     result.revoke(terminate=True)
#     return SUCCESS()
#
#
# @queue.task(name='queqiao.service.v1.health._async_do_task')
# def _async_do_task(project_id, project_name, sleep_time):
#     logger.info(f'async task start...')
#     project = Project.get(id=project_id)
#     if not project:
#         raise Exception(f'project {project_id} not found!')
#     logger.info(f'get project before sleep: {project.to_dict()}')
#     logger.info(f'sleep {sleep_time}...')
#     time.sleep(int(sleep_time))
#     logger.info(f'sleep done, change project name to {project_name}')
#     project.name = project_name
#     project.save()
#     logger.info(f'async task finished!')
#     return project.to_dict()
