"""
Author: xiaohei
Date: 2022/5/16
Email: <EMAIL>
Host: xiaohei.info
"""
from flask import request

from queqiao.conf.ApiResponse import SERVER_NOT_FOUND, SUCCESS
from queqiao.core.auth.entity import login_required
from queqiao.dba.models import Component, TaskType, Engine
from queqiao.log import LogFactory
from queqiao.service import update_db_obj, add_db_obj
from queqiao.service.v1 import regist_api
from queqiao.util.comm.requtil import get_request_param, params_required

api = regist_api(__name__)

log = LogFactory.get_logger()

required_fileds, optional_fields = Component.get_required_and_optional_fields()


@api.route('/', methods=['GET'])
@login_required
def get_components(user):
    operator = get_request_param(request, 'operator')
    datasource = get_request_param(request, 'datasource')
    params = {}
    if operator:
        params['operator'] = operator
    if datasource:
        params['datasource'] = datasource
    components = Component.get(**params)
    if not components:
        return SERVER_NOT_FOUND(detail=f'{operator} & {datasource} 组件')
    log.info(f'user {user.uid} get {len(components)} components')
    return SUCCESS(data=components)


@api.route('/', methods=['POST'])
@login_required
@params_required(*required_fileds)
def add_component(user, **params):
    operator = params['operator']
    datasource = params['datasource']
    return add_db_obj(request, user, Component, params, optional_fields, f'{operator} & {datasource} 组件')


@api.route('/<int:id>', methods=['POST'])
@login_required
def update_component(user, id):
    component = Component.get(id=id)
    return update_db_obj(request, user, component, required_fileds, optional_fields, f'{id} 组件')


@api.route('/match/sources', methods=['GET'])
@params_required('sink_id')
def match_source_components(sink_id):
    task_types = TaskType.get(sink_id=sink_id)
    if task_types:
        ret = []
        for task_type in task_types:
            obj = {}
            obj.update(task_type.to_dict(includes=['code', 'name']))
            obj.update(task_type.source_component.to_dict(includes=['operator', 'datasource']))
            engines = []
            for engine_name in task_type.engines.split(','):
                engine = Engine.get(name=engine_name, only_one=1)
                engines.append(engine.to_dict(includes=['id', 'name']))
            obj['engines'] = engines
            obj['cid'] = task_type.source_component.id
            ret.append(obj)
        return SUCCESS(data=ret)
    else:
        return SERVER_NOT_FOUND(detail=f'{sink_id} 对应的source组件')


@api.route('/match/sinks', methods=['GET'])
@params_required('source_id')
def match_sink_components(source_id):
    task_types = TaskType.get(source_id=source_id)
    if task_types:
        ret = []
        for task_type in task_types:
            obj = {}
            obj.update(task_type.to_dict(includes=['code', 'name']))
            obj.update(task_type.sink_component.to_dict(includes=['operator', 'datasource']))
            engines = []
            for engine_name in task_type.engines.split(','):
                engine = Engine.get(name=engine_name, only_one=1)
                engines.append(engine.to_dict(includes=['id', 'name']))
            obj['engines'] = engines
            obj['cid'] = task_type.sink_component.id
            ret.append(obj)
        return SUCCESS(data=ret)
    else:
        return SERVER_NOT_FOUND(detail=f'{source_id} 对应的sink组件')
