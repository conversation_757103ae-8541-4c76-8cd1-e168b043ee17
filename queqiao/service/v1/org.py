"""
Author: xiaohei
Date: 2023/1/14
Email: <EMAIL>
Host: xiaohei.info
"""
from flask import request

from queqiao.conf.ApiResponse import SUCCESS, RESOURCE_NOT_FOUND
from queqiao.core.auth.entity import login_required
from queqiao.dba.models import Org
from queqiao.log import LogFactory
from queqiao.service import update_db_obj, add_db_obj
from queqiao.service.v1 import regist_api
from queqiao.util.comm.requtil import params_required

api = regist_api(__name__)

log = LogFactory.get_logger()

required_fileds, optional_fields = Org.get_required_and_optional_fields()


@api.route('/', methods=['GET'])
@login_required
def get_all_orgs(user):
    orgs = Org.get()
    log.info(f'user {user.uid} get {len(orgs)} orgs')
    return SUCCESS(data=orgs)


@api.route('/<int:id>', methods=['GET'])
@login_required
def get_org(user, id):
    org = Org.get(id=id)
    if not org:
        return RESOURCE_NOT_FOUND(detail=f'org id {id}')
    return SUCCESS(data=org)


@api.route('/<string:name>', methods=['GET'])
def get_org_by_name(name):
    org = Org.get_one(name=name)
    if not org:
        return RESOURCE_NOT_FOUND(detail=f'org {name}')
    return SUCCESS(data=org)


@api.route('/', methods=['POST'])
@login_required
@params_required(*required_fileds)
def add_org(user, **params):
    return add_db_obj(request, user, Org, params, optional_fields, f'{params["name"]} 机构名')


@api.route('/<int:id>', methods=['POST'])
@login_required
def update_org(user, id):
    org = Org.get(id=id)
    return update_db_obj(request, user, org, required_fileds, optional_fields, f'{id} 机构')
