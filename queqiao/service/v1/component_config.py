"""
Author: xiaohei
Date: 2022/5/16
Email: <EMAIL>
Host: xiaohei.info
"""
from flask import request

from queqiao.conf.ApiResponse import SUCCESS
from queqiao.core.auth.entity import login_required
from queqiao.dba.models import ComponentConfig
from queqiao.log import LogFactory
from queqiao.service import add_db_obj, update_db_obj, delete_db_obj
from queqiao.service.v1 import regist_api
from queqiao.util.comm.requtil import params_required

api = regist_api(__name__)

log = LogFactory.get_logger()

required_fileds, optional_fields = ComponentConfig.get_required_and_optional_fields()


@api.route('/<int:cid>', methods=['GET'])
@login_required
def get_config(user, cid):
    component_configs = ComponentConfig.get(cid=cid)
    cnt = len(component_configs)
    log.info(f'user {user.uid} get {cnt} components')
    return SUCCESS(data=component_configs if cnt > 0 else [])


@api.route('/', methods=['POST'])
@login_required
@params_required(*required_fileds)
def add_config(user, **params):
    return add_db_obj(request, user, ComponentConfig, params, optional_fields, f'{params["name"]} 组件配置')


@api.route('/<int:id>', methods=['POST'])
@login_required
def update_config(user, id):
    component_config = ComponentConfig.get(id=id)
    return update_db_obj(request, user, component_config, required_fileds, optional_fields, f'{id} 组件配置')


@api.route('/<int:id>', methods=['DELETE'])
@login_required
def delete_config(user, id):
    component_config = ComponentConfig.get(id=id)
    return delete_db_obj(user, component_config, f'{id} 组件配置')
