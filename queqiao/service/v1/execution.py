"""
Author: xiaohei
Date: 2022/6/13
Email: <EMAIL>
Host: xiaohei.info
"""

import signal

from celery.result import AsyncResult

from instance.default import EXE_LOG_PATH
from queqiao.conf.ApiResponse import SERVER_NOT_FOUND, SUCCESS, NO_PRIVILEGE, INTERVAL_ERROR, RESOURCE_NOT_FOUND
from queqiao.conf.enums import ExecutionStatus
from queqiao.core.auth.entity import login_required
from queqiao.core.execute.execution import Execution
from queqiao.log import LogFactory
from queqiao.service.v1 import regist_api
from queqiao.util.comm import osutil
from queqiao.util.comm.requtil import params_optional_none

api = regist_api(__name__)

log = LogFactory.get_logger()

required_fileds, optional_fields = Execution.get_required_and_optional_fields()


@api.route('/<int:id>', methods=['GET'])
def get_execution(id):
    return SUCCESS(Execution.get(id=id))


@api.route('/log/<int:id>', methods=['GET'])
def get_execution_log(id):
    execution = Execution.get(id=id)
    log_name = LogFactory.pure_logger_name(execution.execution_name)
    log_file = f'{EXE_LOG_PATH}/{log_name}.log'
    if not osutil.exists(log_file):
        return SERVER_NOT_FOUND(detail=f'log file {log_file} not found!')
    with open(log_file, 'r') as f:
        from flask import Response
        return Response(f.readlines(), mimetype='text/plain')


# 无需认证，etl使用
@api.route('/status/<int:id>', methods=['GET'])
def get_execution_status(id):
    execution = Execution.get(id=id)
    if not execution:
        return RESOURCE_NOT_FOUND(detail=f'execution id:{id}')
    return SUCCESS(
        data={'status': ExecutionStatus(execution.status).name,
              'message': execution.message,
              'start_time': execution.start_time,
              'update_time': execution.update_time,
              'end_time': execution.end_time})


@api.route('/kill/<int:id>', methods=['GET'])
@login_required
def kill_execution(user, id):
    execution = Execution.get(id=id)
    if not execution:
        return RESOURCE_NOT_FOUND(detail=f'execution id:{id}')
    if execution.status in [ExecutionStatus.KILLED.value, ExecutionStatus.SUCCESS.value, ExecutionStatus.FAILED.value]:
        log.info(f'execution {execution.id} already in {execution.status} finished status, skip kill action')
        return SUCCESS()
    managers = execution.task.project.admin_list
    managers.append(execution.create_user)
    if user.uid not in managers:
        return NO_PRIVILEGE()
    result = AsyncResult(execution.qid)
    try:
        result.revoke(terminate=True, signal=signal.SIGKILL)
    except Exception as why:
        log.exception(why)
        return INTERVAL_ERROR(detail=str(why))
    log.info(f'user {user.uid} terminate task: {execution.qid}')
    execution.status = ExecutionStatus.KILLED.value
    execution.save()
    return SUCCESS()


@api.route('/exec/<int:id>', methods=['GET'])
@login_required
@params_optional_none('async')
def exec_execution(user, id, **params):
    delay = int(params['async']) if 'async' in params else 1
    delay = delay == 1
    log.info(f'user {user.uid} execute execution {id} with delay: {delay}')
    try:
        Execution.execute(id, delay=delay)
    except Exception as why:
        return INTERVAL_ERROR(detail=why)
    return SUCCESS()
