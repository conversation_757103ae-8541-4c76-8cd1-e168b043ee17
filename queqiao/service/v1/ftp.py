"""
Author: xiaohei
Date: 2025/01/20
Email: <EMAIL>
Host: xiaohei.info
"""
from flask import request

from queqiao.conf.ApiResponse import SUCCESS, RESOURCE_NOT_FOUND, ILLEGAL_PARAMS
from queqiao.core.auth.entity import login_required
from queqiao.dba.models import Dsn
from queqiao.log import LogFactory
from queqiao.service.v1 import regist_api
from queqiao.util.comm.requtil import params_required
from queqiao.util.conn.ftp import RemoteFileServer
import json

api = regist_api(__name__)
log = LogFactory.get_logger()

@api.route('/test/<string:name>', methods=['GET'])
@login_required
def test_dsn_connection(user, name):
    """测试指定DSN名称的FTP连接"""
    dsn = Dsn.get_one(name=name)
    if not dsn:
        return RESOURCE_NOT_FOUND(detail=f'dsn {name} not found')
    
    try:
        connect_info = json.loads(dsn.connect)
        if not connect_info.get('protocol'):
            return ILLEGAL_PARAMS(detail=f'DSN {name} does not have protocol specified')
    except json.JSONDecodeError:
        return ILLEGAL_PARAMS(detail=f'Invalid DSN connection info format')
    
    is_connected, error_msg = RemoteFileServer.test_connection(connect_info)
    log.info(f'user {user.uid} test connection for dsn {name} - {"Success" if is_connected else f"Failed: {error_msg}"}')
    return SUCCESS(data={
        'connected': is_connected,
        'error': error_msg
    })

@api.route('/test/id/<int:id>', methods=['GET'])
@login_required
def test_dsn_connection_by_id(user, id):
    """测试指定DSN ID的FTP连接"""
    dsn = Dsn.get(id=id)
    if not dsn:
        return RESOURCE_NOT_FOUND(detail=f'dsn id {id} not found')
    
    try:
        connect_info = json.loads(dsn.connect)
        if not connect_info.get('protocol'):
            return ILLEGAL_PARAMS(detail=f'DSN id {id} does not have protocol specified')
    except json.JSONDecodeError:
        return ILLEGAL_PARAMS(detail=f'Invalid DSN connection info format')
    
    is_connected, error_msg = RemoteFileServer.test_connection(connect_info)
    log.info(f'user {user.uid} test connection for dsn id {id} - {"Success" if is_connected else f"Failed: {error_msg}"}')
    return SUCCESS(data={
        'connected': is_connected,
        'error': error_msg
    })

@api.route('/test', methods=['POST'])
@login_required
@params_required('protocol', 'ip', 'port', 'username', 'password', 'work_dir')
def test_connection(user, **params):
    """测试直接提供的FTP连接信息"""
    try:
        params['port'] = int(params['port'])
    except ValueError:
        return ILLEGAL_PARAMS(detail='Invalid port number')
    
    is_connected, error_msg = RemoteFileServer.test_connection(params)
    log.info(f'user {user.uid} test direct connection to {params["ip"]}:{params["port"]} - {"Success" if is_connected else f"Failed: {error_msg}"}')
    return SUCCESS(data={
        'connected': is_connected,
        'error': error_msg
    })

@api.route('/list/id/<int:id>', methods=['GET'])
@login_required
@params_required('ftp_path')
def list_ftp_content(user, id, **params):
    """获取指定DSN ID的FTP服务器上某个路径下的所有内容

    Args:
        id: DSN ID
        ftp_path: FTP服务器上的路径

    Returns:
        文件列表，每个文件包含名称和类型信息
    """
    dsn = Dsn.get(id=id)
    if not dsn:
        return RESOURCE_NOT_FOUND(detail=f'dsn id {id} not found')
    
    try:
        connect_info = json.loads(dsn.connect)
        if not connect_info.get('protocol'):
            return ILLEGAL_PARAMS(detail=f'DSN id {id} does not have protocol specified')
    except json.JSONDecodeError:
        return ILLEGAL_PARAMS(detail=f'Invalid DSN connection info format')
    
    ftp_client = None
    try:
        ftp_client = RemoteFileServer.get_connect(connect_info)
        ftp_client.open()
        file_list = ftp_client.list_dir_info(params['ftp_path'])
        return SUCCESS(data={
            'files': file_list
        })
    except Exception as e:
        log.error(f'Failed to list directory {params["ftp_path"]} on FTP server: {str(e)}')
        return ILLEGAL_PARAMS(detail=str(e))
    finally:
        if ftp_client:
            try:
                ftp_client.close()
            except Exception as e:
                log.error(f'Failed to close FTP connection: {str(e)}')

@api.route('/list/<string:name>', methods=['GET'])
@login_required
@params_required('ftp_path')
def list_ftp_content_by_name(user, name, **params):
    """获取指定DSN名称的FTP服务器上某个路径下的所有内容

    Args:
        name: DSN名称
        ftp_path: FTP服务器上的路径

    Returns:
        文件列表，每个文件包含名称和类型信息
    """
    dsn = Dsn.get_one(name=name)
    if not dsn:
        return RESOURCE_NOT_FOUND(detail=f'dsn {name} not found')
    
    try:
        connect_info = json.loads(dsn.connect)
        if not connect_info.get('protocol'):
            return ILLEGAL_PARAMS(detail=f'DSN {name} does not have protocol specified')
    except json.JSONDecodeError:
        return ILLEGAL_PARAMS(detail=f'Invalid DSN connection info format')
    
    ftp_client = None
    try:
        ftp_client = RemoteFileServer.get_connect(connect_info)
        ftp_client.open()
        file_list = ftp_client.list_dir_info(params['ftp_path'])
        return SUCCESS(data={
            'files': file_list
        })
    except Exception as e:
        log.error(f'Failed to list directory {params["ftp_path"]} on FTP server: {str(e)}')
        return ILLEGAL_PARAMS(detail=str(e))
    finally:
        if ftp_client:
            try:
                ftp_client.close()
            except Exception as e:
                log.error(f'Failed to close FTP connection: {str(e)}')

@api.route('/list/direct', methods=['GET'])
@login_required
@params_required('protocol', 'ip', 'port', 'username', 'password', 'ftp_path')
def list_ftp_content_direct(user, **params):
    """通过直接提供的连接信息获取FTP服务器上某个路径下的所有内容

    Args:
        protocol: 协议类型 (通过URL参数传递)
        ip: FTP服务器IP (通过URL参数传递)
        port: FTP服务器端口 (通过URL参数传递)
        username: 用户名 (通过URL参数传递)
        password: 密码 (通过URL参数传递)
        ftp_path: FTP服务器上的路径 (通过URL参数传递)

    Returns:
        文件列表，每个文件包含名称和类型信息
    """
    try:
        params['port'] = int(params['port'])
    except ValueError:
        return ILLEGAL_PARAMS(detail='Invalid port number')

    ftp_client = None
    try:
        ftp_client = RemoteFileServer.get_connect(params)
        ftp_client.open()
        file_list = ftp_client.list_dir_info(params['ftp_path'])
        return SUCCESS(data={
            'files': file_list
        })
    except Exception as e:
        log.error(f'Failed to list directory {params["ftp_path"]} on FTP server: {str(e)}')
        return ILLEGAL_PARAMS(detail=str(e))
    finally:
        if ftp_client:
            try:
                ftp_client.close()
            except Exception as e:
                log.error(f'Failed to close FTP connection: {str(e)}')

@api.route('/wc/id/<int:id>', methods=['GET'])
@login_required
@params_required('file_path')
def wc_file_by_id(user, id, **params):
    """通过DSN ID获取指定文件的总行数

    Args:
        id: DSN ID
        file_path: 文件路径

    Returns:
        文件总行数
    """
    log.info(f'[WC_BY_ID] Request started - user: {user.uid}, dsn_id: {id}, file_path: {params.get("file_path")}')

    # 查找DSN
    log.debug(f'[WC_BY_ID] Looking up DSN with id: {id}')
    dsn = Dsn.get(id=id)
    if not dsn:
        log.warning(f'[WC_BY_ID] DSN not found - id: {id}')
        return RESOURCE_NOT_FOUND(detail=f'dsn id {id} not found')

    log.debug(f'[WC_BY_ID] DSN found - name: {dsn.name}, protocol: {dsn.connect}')

    # 解析连接信息
    try:
        log.debug(f'[WC_BY_ID] Parsing DSN connection info')
        connect_info = json.loads(dsn.connect)
        if not connect_info.get('protocol'):
            log.error(f'[WC_BY_ID] DSN missing protocol - id: {id}')
            return ILLEGAL_PARAMS(detail=f'DSN id {id} does not have protocol specified')
        log.debug(f'[WC_BY_ID] Connection info parsed - protocol: {connect_info.get("protocol")}, ip: {connect_info.get("ip")}')
    except json.JSONDecodeError as e:
        log.error(f'[WC_BY_ID] Invalid DSN connection format - id: {id}, error: {str(e)}')
        return ILLEGAL_PARAMS(detail=f'Invalid DSN connection info format')

    ftp_client = None
    try:
        # 建立连接
        log.debug(f'[WC_BY_ID] Creating FTP connection - protocol: {connect_info.get("protocol")}')
        ftp_client = RemoteFileServer.get_connect(connect_info)
        log.debug(f'[WC_BY_ID] FTP client created, opening connection')
        ftp_client.open()
        log.info(f'[WC_BY_ID] FTP connection established successfully')

        # 检查文件是否存在
        log.debug(f'[WC_BY_ID] Checking if file exists: {params["file_path"]}')
        if not ftp_client.exists(params['file_path']):
            log.warning(f'[WC_BY_ID] File not found: {params["file_path"]}')
            return RESOURCE_NOT_FOUND(detail=f'File {params["file_path"]} not found')
        log.debug(f'[WC_BY_ID] File exists, starting line count')

        # 执行行数统计
        log.info(f'[WC_BY_ID] Starting wc operation on file: {params["file_path"]}')
        line_count = ftp_client.wc(params['file_path'])
        log.info(f'[WC_BY_ID] wc operation completed - user: {user.uid}, file: {params["file_path"]}, dsn_id: {id}, lines: {line_count}')

        return SUCCESS(data={
            'file_path': params['file_path'],
            'line_count': line_count
        })
    except Exception as e:
        log.error(f'[WC_BY_ID] Operation failed - file: {params["file_path"]}, error: {str(e)}', exc_info=True)
        return ILLEGAL_PARAMS(detail=str(e))
    finally:
        if ftp_client:
            try:
                log.debug(f'[WC_BY_ID] Closing FTP connection')
                ftp_client.close()
                log.debug(f'[WC_BY_ID] FTP connection closed successfully')
            except Exception as e:
                log.error(f'[WC_BY_ID] Failed to close FTP connection: {str(e)}')

@api.route('/wc/<string:name>', methods=['GET'])
@login_required
@params_required('file_path')
def wc_file_by_name(user, name, **params):
    """通过DSN名称获取指定文件的总行数

    Args:
        name: DSN名称
        file_path: 文件路径

    Returns:
        文件总行数
    """
    log.info(f'[WC_BY_NAME] Request started - user: {user.uid}, dsn_name: {name}, file_path: {params.get("file_path")}')

    # 查找DSN
    log.debug(f'[WC_BY_NAME] Looking up DSN with name: {name}')
    dsn = Dsn.get_one(name=name)
    if not dsn:
        log.warning(f'[WC_BY_NAME] DSN not found - name: {name}')
        return RESOURCE_NOT_FOUND(detail=f'dsn {name} not found')

    log.debug(f'[WC_BY_NAME] DSN found - id: {dsn.id}, protocol: {dsn.connect}')

    # 解析连接信息
    try:
        log.debug(f'[WC_BY_NAME] Parsing DSN connection info')
        connect_info = json.loads(dsn.connect)
        if not connect_info.get('protocol'):
            log.error(f'[WC_BY_NAME] DSN missing protocol - name: {name}')
            return ILLEGAL_PARAMS(detail=f'DSN {name} does not have protocol specified')
        log.debug(f'[WC_BY_NAME] Connection info parsed - protocol: {connect_info.get("protocol")}, ip: {connect_info.get("ip")}')
    except json.JSONDecodeError as e:
        log.error(f'[WC_BY_NAME] Invalid DSN connection format - name: {name}, error: {str(e)}')
        return ILLEGAL_PARAMS(detail=f'Invalid DSN connection info format')

    ftp_client = None
    try:
        # 建立连接
        log.debug(f'[WC_BY_NAME] Creating FTP connection - protocol: {connect_info.get("protocol")}')
        ftp_client = RemoteFileServer.get_connect(connect_info)
        log.debug(f'[WC_BY_NAME] FTP client created, opening connection')
        ftp_client.open()
        log.info(f'[WC_BY_NAME] FTP connection established successfully')

        # 检查文件是否存在
        log.debug(f'[WC_BY_NAME] Checking if file exists: {params["file_path"]}')
        if not ftp_client.exists(params['file_path']):
            log.warning(f'[WC_BY_NAME] File not found: {params["file_path"]}')
            return RESOURCE_NOT_FOUND(detail=f'File {params["file_path"]} not found')
        log.debug(f'[WC_BY_NAME] File exists, starting line count')

        # 执行行数统计
        log.info(f'[WC_BY_NAME] Starting wc operation on file: {params["file_path"]}')
        line_count = ftp_client.wc(params['file_path'])
        log.info(f'[WC_BY_NAME] wc operation completed - user: {user.uid}, file: {params["file_path"]}, dsn_name: {name}, lines: {line_count}')

        return SUCCESS(data={
            'file_path': params['file_path'],
            'line_count': line_count
        })
    except Exception as e:
        log.error(f'[WC_BY_NAME] Operation failed - file: {params["file_path"]}, error: {str(e)}', exc_info=True)
        return ILLEGAL_PARAMS(detail=str(e))
    finally:
        if ftp_client:
            try:
                log.debug(f'[WC_BY_NAME] Closing FTP connection')
                ftp_client.close()
                log.debug(f'[WC_BY_NAME] FTP connection closed successfully')
            except Exception as e:
                log.error(f'[WC_BY_NAME] Failed to close FTP connection: {str(e)}')

@api.route('/wc/direct', methods=['GET'])
@login_required
@params_required('protocol', 'ip', 'port', 'username', 'password', 'file_path')
def wc_file_direct(user, **params):
    """通过直接提供的连接信息获取指定文件的总行数

    Args:
        protocol: 协议类型 (通过URL参数传递)
        ip: FTP服务器IP (通过URL参数传递)
        port: FTP服务器端口 (通过URL参数传递)
        username: 用户名 (通过URL参数传递)
        password: 密码 (通过URL参数传递)
        file_path: 文件路径 (通过URL参数传递)

    Returns:
        文件总行数
    """
    log.info(f'[WC_DIRECT] Request started - user: {user.uid}, protocol: {params.get("protocol")}, ip: {params.get("ip")}, file_path: {params.get("file_path")}')

    # 验证端口号
    try:
        log.debug(f'[WC_DIRECT] Validating port number: {params.get("port")}')
        params['port'] = int(params['port'])
        log.debug(f'[WC_DIRECT] Port number validated: {params["port"]}')
    except ValueError as e:
        log.error(f'[WC_DIRECT] Invalid port number: {params.get("port")}, error: {str(e)}')
        return ILLEGAL_PARAMS(detail='Invalid port number')

    ftp_client = None
    try:
        # 建立连接
        log.debug(f'[WC_DIRECT] Creating FTP connection - protocol: {params["protocol"]}, ip: {params["ip"]}, port: {params["port"]}')
        ftp_client = RemoteFileServer.get_connect(params)
        log.debug(f'[WC_DIRECT] FTP client created, opening connection')
        ftp_client.open()
        log.info(f'[WC_DIRECT] FTP connection established successfully')

        # 检查文件是否存在
        log.debug(f'[WC_DIRECT] Checking if file exists: {params["file_path"]}')
        if not ftp_client.exists(params['file_path']):
            log.warning(f'[WC_DIRECT] File not found: {params["file_path"]}')
            return RESOURCE_NOT_FOUND(detail=f'File {params["file_path"]} not found')
        log.debug(f'[WC_DIRECT] File exists, starting line count')

        # 执行行数统计
        log.info(f'[WC_DIRECT] Starting wc operation on file: {params["file_path"]}')
        line_count = ftp_client.wc(params['file_path'])
        log.info(f'[WC_DIRECT] wc operation completed - user: {user.uid}, file: {params["file_path"]}, lines: {line_count}')

        return SUCCESS(data={
            'file_path': params['file_path'],
            'line_count': line_count
        })
    except Exception as e:
        log.error(f'[WC_DIRECT] Operation failed - file: {params["file_path"]}, error: {str(e)}', exc_info=True)
        return ILLEGAL_PARAMS(detail=str(e))
    finally:
        if ftp_client:
            try:
                log.debug(f'[WC_DIRECT] Closing FTP connection')
                ftp_client.close()
                log.debug(f'[WC_DIRECT] FTP connection closed successfully')
            except Exception as e:
                log.error(f'[WC_DIRECT] Failed to close FTP connection: {str(e)}')

@api.route('/headn/id/<int:id>', methods=['GET'])
@login_required
@params_required('file_path')
def headn_file_by_id(user, id, **params):
    """通过DSN ID获取指定文件的前n行

    Args:
        id: DSN ID
        file_path: 文件路径
        n: 要获取的行数，默认100行 (可选参数)

    Returns:
        文件前n行内容
    """
    log.info(f'[HEADN_BY_ID] Request started - user: {user.uid}, dsn_id: {id}, file_path: {params.get("file_path")}')

    # 查找DSN
    log.debug(f'[HEADN_BY_ID] Looking up DSN with id: {id}')
    dsn = Dsn.get(id=id)
    if not dsn:
        log.warning(f'[HEADN_BY_ID] DSN not found - id: {id}')
        return RESOURCE_NOT_FOUND(detail=f'dsn id {id} not found')

    log.debug(f'[HEADN_BY_ID] DSN found - name: {dsn.name}')

    # 解析连接信息
    try:
        log.debug(f'[HEADN_BY_ID] Parsing DSN connection info')
        connect_info = json.loads(dsn.connect)
        if not connect_info.get('protocol'):
            log.error(f'[HEADN_BY_ID] DSN missing protocol - id: {id}')
            return ILLEGAL_PARAMS(detail=f'DSN id {id} does not have protocol specified')
        log.debug(f'[HEADN_BY_ID] Connection info parsed - protocol: {connect_info.get("protocol")}')
    except json.JSONDecodeError as e:
        log.error(f'[HEADN_BY_ID] Invalid DSN connection format - id: {id}, error: {str(e)}')
        return ILLEGAL_PARAMS(detail=f'Invalid DSN connection info format')

    # 获取可选的行数参数
    n = request.args.get('n', 100)
    log.debug(f'[HEADN_BY_ID] Processing n parameter: {n}')
    try:
        n = int(n)
        if n <= 0:
            log.error(f'[HEADN_BY_ID] Invalid n parameter (must be positive): {n}')
            return ILLEGAL_PARAMS(detail='Parameter n must be a positive integer')
        log.debug(f'[HEADN_BY_ID] n parameter validated: {n}')
    except ValueError as e:
        log.error(f'[HEADN_BY_ID] Invalid n parameter format: {n}, error: {str(e)}')
        return ILLEGAL_PARAMS(detail='Parameter n must be a valid integer')

    ftp_client = None
    try:
        # 建立连接
        log.debug(f'[HEADN_BY_ID] Creating FTP connection - protocol: {connect_info.get("protocol")}')
        ftp_client = RemoteFileServer.get_connect(connect_info)
        log.debug(f'[HEADN_BY_ID] FTP client created, opening connection')
        ftp_client.open()
        log.info(f'[HEADN_BY_ID] FTP connection established successfully')

        # 检查文件是否存在
        log.debug(f'[HEADN_BY_ID] Checking if file exists: {params["file_path"]}')
        if not ftp_client.exists(params['file_path']):
            log.warning(f'[HEADN_BY_ID] File not found: {params["file_path"]}')
            return RESOURCE_NOT_FOUND(detail=f'File {params["file_path"]} not found')
        log.debug(f'[HEADN_BY_ID] File exists, starting headn operation')

        # 执行前n行读取
        log.info(f'[HEADN_BY_ID] Starting headn operation - file: {params["file_path"]}, lines: {n}')
        content = ftp_client.headn(params['file_path'], n=n)
        log.info(f'[HEADN_BY_ID] headn operation completed - user: {user.uid}, file: {params["file_path"]}, dsn_id: {id}, lines: {n}')

        return SUCCESS(data={
            'file_path': params['file_path'],
            'lines_requested': n,
            'content': content
        })
    except Exception as e:
        log.error(f'[HEADN_BY_ID] Operation failed - file: {params["file_path"]}, lines: {n}, error: {str(e)}', exc_info=True)
        return ILLEGAL_PARAMS(detail=str(e))
    finally:
        if ftp_client:
            try:
                log.debug(f'[HEADN_BY_ID] Closing FTP connection')
                ftp_client.close()
                log.debug(f'[HEADN_BY_ID] FTP connection closed successfully')
            except Exception as e:
                log.error(f'[HEADN_BY_ID] Failed to close FTP connection: {str(e)}')

@api.route('/headn/<string:name>', methods=['GET'])
@login_required
@params_required('file_path')
def headn_file_by_name(user, name, **params):
    """通过DSN名称获取指定文件的前n行

    Args:
        name: DSN名称
        file_path: 文件路径
        n: 要获取的行数，默认100行 (可选参数)

    Returns:
        文件前n行内容
    """
    log.info(f'[HEADN_BY_NAME] Request started - user: {user.uid}, dsn_name: {name}, file_path: {params.get("file_path")}')

    # 查找DSN
    log.debug(f'[HEADN_BY_NAME] Looking up DSN with name: {name}')
    dsn = Dsn.get_one(name=name)
    if not dsn:
        log.warning(f'[HEADN_BY_NAME] DSN not found - name: {name}')
        return RESOURCE_NOT_FOUND(detail=f'dsn {name} not found')

    log.debug(f'[HEADN_BY_NAME] DSN found - id: {dsn.id}')

    # 解析连接信息
    try:
        log.debug(f'[HEADN_BY_NAME] Parsing DSN connection info')
        connect_info = json.loads(dsn.connect)
        if not connect_info.get('protocol'):
            log.error(f'[HEADN_BY_NAME] DSN missing protocol - name: {name}')
            return ILLEGAL_PARAMS(detail=f'DSN {name} does not have protocol specified')
        log.debug(f'[HEADN_BY_NAME] Connection info parsed - protocol: {connect_info.get("protocol")}')
    except json.JSONDecodeError as e:
        log.error(f'[HEADN_BY_NAME] Invalid DSN connection format - name: {name}, error: {str(e)}')
        return ILLEGAL_PARAMS(detail=f'Invalid DSN connection info format')

    # 获取可选的行数参数
    n = request.args.get('n', 100)
    log.debug(f'[HEADN_BY_NAME] Processing n parameter: {n}')
    try:
        n = int(n)
        if n <= 0:
            log.error(f'[HEADN_BY_NAME] Invalid n parameter (must be positive): {n}')
            return ILLEGAL_PARAMS(detail='Parameter n must be a positive integer')
        log.debug(f'[HEADN_BY_NAME] n parameter validated: {n}')
    except ValueError as e:
        log.error(f'[HEADN_BY_NAME] Invalid n parameter format: {n}, error: {str(e)}')
        return ILLEGAL_PARAMS(detail='Parameter n must be a valid integer')

    ftp_client = None
    try:
        # 建立连接
        log.debug(f'[HEADN_BY_NAME] Creating FTP connection - protocol: {connect_info.get("protocol")}')
        ftp_client = RemoteFileServer.get_connect(connect_info)
        log.debug(f'[HEADN_BY_NAME] FTP client created, opening connection')
        ftp_client.open()
        log.info(f'[HEADN_BY_NAME] FTP connection established successfully')

        # 检查文件是否存在
        log.debug(f'[HEADN_BY_NAME] Checking if file exists: {params["file_path"]}')
        if not ftp_client.exists(params['file_path']):
            log.warning(f'[HEADN_BY_NAME] File not found: {params["file_path"]}')
            return RESOURCE_NOT_FOUND(detail=f'File {params["file_path"]} not found')
        log.debug(f'[HEADN_BY_NAME] File exists, starting headn operation')

        # 执行前n行读取
        log.info(f'[HEADN_BY_NAME] Starting headn operation - file: {params["file_path"]}, lines: {n}')
        content = ftp_client.headn(params['file_path'], n=n)
        log.info(f'[HEADN_BY_NAME] headn operation completed - user: {user.uid}, file: {params["file_path"]}, dsn_name: {name}, lines: {n}')

        return SUCCESS(data={
            'file_path': params['file_path'],
            'lines_requested': n,
            'content': content
        })
    except Exception as e:
        log.error(f'[HEADN_BY_NAME] Operation failed - file: {params["file_path"]}, lines: {n}, error: {str(e)}', exc_info=True)
        return ILLEGAL_PARAMS(detail=str(e))
    finally:
        if ftp_client:
            try:
                log.debug(f'[HEADN_BY_NAME] Closing FTP connection')
                ftp_client.close()
                log.debug(f'[HEADN_BY_NAME] FTP connection closed successfully')
            except Exception as e:
                log.error(f'[HEADN_BY_NAME] Failed to close FTP connection: {str(e)}')

@api.route('/headn/direct', methods=['GET'])
@login_required
@params_required('protocol', 'ip', 'port', 'username', 'password', 'file_path')
def headn_file_direct(user, **params):
    """通过直接提供的连接信息获取指定文件的前n行

    Args:
        protocol: 协议类型 (通过URL参数传递)
        ip: FTP服务器IP (通过URL参数传递)
        port: FTP服务器端口 (通过URL参数传递)
        username: 用户名 (通过URL参数传递)
        password: 密码 (通过URL参数传递)
        file_path: 文件路径 (通过URL参数传递)
        n: 要获取的行数，默认100行 (可选参数)

    Returns:
        文件前n行内容
    """
    log.info(f'[HEADN_DIRECT] Request started - user: {user.uid}, protocol: {params.get("protocol")}, ip: {params.get("ip")}, file_path: {params.get("file_path")}')

    # 验证端口号
    try:
        log.debug(f'[HEADN_DIRECT] Validating port number: {params.get("port")}')
        params['port'] = int(params['port'])
        log.debug(f'[HEADN_DIRECT] Port number validated: {params["port"]}')
    except ValueError as e:
        log.error(f'[HEADN_DIRECT] Invalid port number: {params.get("port")}, error: {str(e)}')
        return ILLEGAL_PARAMS(detail='Invalid port number')

    # 获取可选的行数参数
    n = request.args.get('n', 100)
    log.debug(f'[HEADN_DIRECT] Processing n parameter: {n}')
    try:
        n = int(n)
        if n <= 0:
            log.error(f'[HEADN_DIRECT] Invalid n parameter (must be positive): {n}')
            return ILLEGAL_PARAMS(detail='Parameter n must be a positive integer')
        log.debug(f'[HEADN_DIRECT] n parameter validated: {n}')
    except ValueError as e:
        log.error(f'[HEADN_DIRECT] Invalid n parameter format: {n}, error: {str(e)}')
        return ILLEGAL_PARAMS(detail='Parameter n must be a valid integer')

    ftp_client = None
    try:
        # 建立连接
        log.debug(f'[HEADN_DIRECT] Creating FTP connection - protocol: {params["protocol"]}, ip: {params["ip"]}, port: {params["port"]}')
        ftp_client = RemoteFileServer.get_connect(params)
        log.debug(f'[HEADN_DIRECT] FTP client created, opening connection')
        ftp_client.open()
        log.info(f'[HEADN_DIRECT] FTP connection established successfully')

        # 检查文件是否存在
        log.debug(f'[HEADN_DIRECT] Checking if file exists: {params["file_path"]}')
        if not ftp_client.exists(params['file_path']):
            log.warning(f'[HEADN_DIRECT] File not found: {params["file_path"]}')
            return RESOURCE_NOT_FOUND(detail=f'File {params["file_path"]} not found')
        log.debug(f'[HEADN_DIRECT] File exists, starting headn operation')

        # 执行前n行读取
        log.info(f'[HEADN_DIRECT] Starting headn operation - file: {params["file_path"]}, lines: {n}')
        content = ftp_client.headn(params['file_path'], n=n)
        log.info(f'[HEADN_DIRECT] headn operation completed - user: {user.uid}, file: {params["file_path"]}, lines: {n}')

        return SUCCESS(data={
            'file_path': params['file_path'],
            'lines_requested': n,
            'content': content
        })
    except Exception as e:
        log.error(f'[HEADN_DIRECT] Operation failed - file: {params["file_path"]}, lines: {n}, error: {str(e)}', exc_info=True)
        return ILLEGAL_PARAMS(detail=str(e))
    finally:
        if ftp_client:
            try:
                log.debug(f'[HEADN_DIRECT] Closing FTP connection')
                ftp_client.close()
                log.debug(f'[HEADN_DIRECT] FTP connection closed successfully')
            except Exception as e:
                log.error(f'[HEADN_DIRECT] Failed to close FTP connection: {str(e)}')
