"""
Author: xiaohei
Date: 2025/01/20
Email: <EMAIL>
Host: xiaohei.info
"""
from flask import request

from queqiao.conf.ApiResponse import SUCCESS, RESOURCE_NOT_FOUND, ILLEGAL_PARAMS
from queqiao.core.auth.entity import login_required
from queqiao.dba.models import Dsn
from queqiao.log import LogFactory
from queqiao.service.v1 import regist_api
from queqiao.util.comm.requtil import params_required
from queqiao.util.conn.ftp import RemoteFileServer
import json

api = regist_api(__name__)
log = LogFactory.get_logger()

@api.route('/test/<string:name>', methods=['GET'])
@login_required
def test_dsn_connection(user, name):
    """测试指定DSN名称的FTP连接"""
    dsn = Dsn.get_one(name=name)
    if not dsn:
        return RESOURCE_NOT_FOUND(detail=f'dsn {name} not found')
    
    try:
        connect_info = json.loads(dsn.connect)
        if not connect_info.get('protocol'):
            return ILLEGAL_PARAMS(detail=f'DSN {name} does not have protocol specified')
    except json.JSONDecodeError:
        return ILLEGAL_PARAMS(detail=f'Invalid DSN connection info format')
    
    is_connected, error_msg = RemoteFileServer.test_connection(connect_info)
    log.info(f'user {user.uid} test connection for dsn {name} - {"Success" if is_connected else f"Failed: {error_msg}"}')
    return SUCCESS(data={
        'connected': is_connected,
        'error': error_msg
    })

@api.route('/test/id/<int:id>', methods=['GET'])
@login_required
def test_dsn_connection_by_id(user, id):
    """测试指定DSN ID的FTP连接"""
    dsn = Dsn.get(id=id)
    if not dsn:
        return RESOURCE_NOT_FOUND(detail=f'dsn id {id} not found')
    
    try:
        connect_info = json.loads(dsn.connect)
        if not connect_info.get('protocol'):
            return ILLEGAL_PARAMS(detail=f'DSN id {id} does not have protocol specified')
    except json.JSONDecodeError:
        return ILLEGAL_PARAMS(detail=f'Invalid DSN connection info format')
    
    is_connected, error_msg = RemoteFileServer.test_connection(connect_info)
    log.info(f'user {user.uid} test connection for dsn id {id} - {"Success" if is_connected else f"Failed: {error_msg}"}')
    return SUCCESS(data={
        'connected': is_connected,
        'error': error_msg
    })

@api.route('/test', methods=['POST'])
@login_required
@params_required('protocol', 'ip', 'port', 'username', 'password', 'work_dir')
def test_connection(user, **params):
    """测试直接提供的FTP连接信息"""
    try:
        params['port'] = int(params['port'])
    except ValueError:
        return ILLEGAL_PARAMS(detail='Invalid port number')
    
    is_connected, error_msg = RemoteFileServer.test_connection(params)
    log.info(f'user {user.uid} test direct connection to {params["ip"]}:{params["port"]} - {"Success" if is_connected else f"Failed: {error_msg}"}')
    return SUCCESS(data={
        'connected': is_connected,
        'error': error_msg
    })

@api.route('/list/id/<int:id>', methods=['GET'])
@login_required
@params_required('ftp_path')
def list_ftp_content(user, id, **params):
    """获取指定DSN ID的FTP服务器上某个路径下的所有内容

    Args:
        id: DSN ID
        ftp_path: FTP服务器上的路径

    Returns:
        文件列表，每个文件包含名称和类型信息
    """
    dsn = Dsn.get(id=id)
    if not dsn:
        return RESOURCE_NOT_FOUND(detail=f'dsn id {id} not found')
    
    try:
        connect_info = json.loads(dsn.connect)
        if not connect_info.get('protocol'):
            return ILLEGAL_PARAMS(detail=f'DSN id {id} does not have protocol specified')
    except json.JSONDecodeError:
        return ILLEGAL_PARAMS(detail=f'Invalid DSN connection info format')
    
    ftp_client = None
    try:
        ftp_client = RemoteFileServer.get_connect(connect_info)
        ftp_client.open()
        file_list = ftp_client.list_dir_info(params['ftp_path'])
        return SUCCESS(data={
            'files': file_list
        })
    except Exception as e:
        log.error(f'Failed to list directory {params["ftp_path"]} on FTP server: {str(e)}')
        return ILLEGAL_PARAMS(detail=str(e))
    finally:
        if ftp_client:
            try:
                ftp_client.close()
            except Exception as e:
                log.error(f'Failed to close FTP connection: {str(e)}')

@api.route('/list/<string:name>', methods=['GET'])
@login_required
@params_required('ftp_path')
def list_ftp_content_by_name(user, name, **params):
    """获取指定DSN名称的FTP服务器上某个路径下的所有内容

    Args:
        name: DSN名称
        ftp_path: FTP服务器上的路径

    Returns:
        文件列表，每个文件包含名称和类型信息
    """
    dsn = Dsn.get_one(name=name)
    if not dsn:
        return RESOURCE_NOT_FOUND(detail=f'dsn {name} not found')
    
    try:
        connect_info = json.loads(dsn.connect)
        if not connect_info.get('protocol'):
            return ILLEGAL_PARAMS(detail=f'DSN {name} does not have protocol specified')
    except json.JSONDecodeError:
        return ILLEGAL_PARAMS(detail=f'Invalid DSN connection info format')
    
    ftp_client = None
    try:
        ftp_client = RemoteFileServer.get_connect(connect_info)
        ftp_client.open()
        file_list = ftp_client.list_dir_info(params['ftp_path'])
        return SUCCESS(data={
            'files': file_list
        })
    except Exception as e:
        log.error(f'Failed to list directory {params["ftp_path"]} on FTP server: {str(e)}')
        return ILLEGAL_PARAMS(detail=str(e))
    finally:
        if ftp_client:
            try:
                ftp_client.close()
            except Exception as e:
                log.error(f'Failed to close FTP connection: {str(e)}')

@api.route('/list/direct', methods=['GET'])
@login_required
@params_required('protocol', 'ip', 'port', 'username', 'password', 'ftp_path')
def list_ftp_content_direct(user, **params):
    """通过直接提供的连接信息获取FTP服务器上某个路径下的所有内容

    Args:
        protocol: 协议类型 (通过URL参数传递)
        ip: FTP服务器IP (通过URL参数传递)
        port: FTP服务器端口 (通过URL参数传递)
        username: 用户名 (通过URL参数传递)
        password: 密码 (通过URL参数传递)
        ftp_path: FTP服务器上的路径 (通过URL参数传递)

    Returns:
        文件列表，每个文件包含名称和类型信息
    """
    try:
        params['port'] = int(params['port'])
    except ValueError:
        return ILLEGAL_PARAMS(detail='Invalid port number')

    ftp_client = None
    try:
        ftp_client = RemoteFileServer.get_connect(params)
        ftp_client.open()
        file_list = ftp_client.list_dir_info(params['ftp_path'])
        return SUCCESS(data={
            'files': file_list
        })
    except Exception as e:
        log.error(f'Failed to list directory {params["ftp_path"]} on FTP server: {str(e)}')
        return ILLEGAL_PARAMS(detail=str(e))
    finally:
        if ftp_client:
            try:
                ftp_client.close()
            except Exception as e:
                log.error(f'Failed to close FTP connection: {str(e)}')

@api.route('/wc/id/<int:id>', methods=['GET'])
@login_required
@params_required('file_path')
def wc_file_by_id(user, id, **params):
    """通过DSN ID获取指定文件的总行数

    Args:
        id: DSN ID
        file_path: 文件路径

    Returns:
        文件总行数
    """
    dsn = Dsn.get(id=id)
    if not dsn:
        return RESOURCE_NOT_FOUND(detail=f'dsn id {id} not found')

    try:
        connect_info = json.loads(dsn.connect)
        if not connect_info.get('protocol'):
            return ILLEGAL_PARAMS(detail=f'DSN id {id} does not have protocol specified')
    except json.JSONDecodeError:
        return ILLEGAL_PARAMS(detail=f'Invalid DSN connection info format')

    ftp_client = None
    try:
        ftp_client = RemoteFileServer.get_connect(connect_info)
        ftp_client.open()

        # 检查文件是否存在
        if not ftp_client.exists(params['file_path']):
            return RESOURCE_NOT_FOUND(detail=f'File {params["file_path"]} not found')

        line_count = ftp_client.wc(params['file_path'])
        log.info(f'user {user.uid} counted lines in file {params["file_path"]} via dsn id {id}: {line_count} lines')
        return SUCCESS(data={
            'file_path': params['file_path'],
            'line_count': line_count
        })
    except Exception as e:
        log.error(f'Failed to count lines in file {params["file_path"]} on FTP server: {str(e)}')
        return ILLEGAL_PARAMS(detail=str(e))
    finally:
        if ftp_client:
            try:
                ftp_client.close()
            except Exception as e:
                log.error(f'Failed to close FTP connection: {str(e)}')

@api.route('/wc/<string:name>', methods=['GET'])
@login_required
@params_required('file_path')
def wc_file_by_name(user, name, **params):
    """通过DSN名称获取指定文件的总行数

    Args:
        name: DSN名称
        file_path: 文件路径

    Returns:
        文件总行数
    """
    dsn = Dsn.get_one(name=name)
    if not dsn:
        return RESOURCE_NOT_FOUND(detail=f'dsn {name} not found')

    try:
        connect_info = json.loads(dsn.connect)
        if not connect_info.get('protocol'):
            return ILLEGAL_PARAMS(detail=f'DSN {name} does not have protocol specified')
    except json.JSONDecodeError:
        return ILLEGAL_PARAMS(detail=f'Invalid DSN connection info format')

    ftp_client = None
    try:
        ftp_client = RemoteFileServer.get_connect(connect_info)
        ftp_client.open()

        # 检查文件是否存在
        if not ftp_client.exists(params['file_path']):
            return RESOURCE_NOT_FOUND(detail=f'File {params["file_path"]} not found')

        line_count = ftp_client.wc(params['file_path'])
        log.info(f'user {user.uid} counted lines in file {params["file_path"]} via dsn {name}: {line_count} lines')
        return SUCCESS(data={
            'file_path': params['file_path'],
            'line_count': line_count
        })
    except Exception as e:
        log.error(f'Failed to count lines in file {params["file_path"]} on FTP server: {str(e)}')
        return ILLEGAL_PARAMS(detail=str(e))
    finally:
        if ftp_client:
            try:
                ftp_client.close()
            except Exception as e:
                log.error(f'Failed to close FTP connection: {str(e)}')

@api.route('/wc/direct', methods=['GET'])
@login_required
@params_required('protocol', 'ip', 'port', 'username', 'password', 'file_path')
def wc_file_direct(user, **params):
    """通过直接提供的连接信息获取指定文件的总行数

    Args:
        protocol: 协议类型 (通过URL参数传递)
        ip: FTP服务器IP (通过URL参数传递)
        port: FTP服务器端口 (通过URL参数传递)
        username: 用户名 (通过URL参数传递)
        password: 密码 (通过URL参数传递)
        file_path: 文件路径 (通过URL参数传递)

    Returns:
        文件总行数
    """
    try:
        params['port'] = int(params['port'])
    except ValueError:
        return ILLEGAL_PARAMS(detail='Invalid port number')

    ftp_client = None
    try:
        ftp_client = RemoteFileServer.get_connect(params)
        ftp_client.open()

        # 检查文件是否存在
        if not ftp_client.exists(params['file_path']):
            return RESOURCE_NOT_FOUND(detail=f'File {params["file_path"]} not found')

        line_count = ftp_client.wc(params['file_path'])
        log.info(f'user {user.uid} counted lines in file {params["file_path"]} via direct connection: {line_count} lines')
        return SUCCESS(data={
            'file_path': params['file_path'],
            'line_count': line_count
        })
    except Exception as e:
        log.error(f'Failed to count lines in file {params["file_path"]} on FTP server: {str(e)}')
        return ILLEGAL_PARAMS(detail=str(e))
    finally:
        if ftp_client:
            try:
                ftp_client.close()
            except Exception as e:
                log.error(f'Failed to close FTP connection: {str(e)}')

@api.route('/headn/id/<int:id>', methods=['GET'])
@login_required
@params_required('file_path')
def headn_file_by_id(user, id, **params):
    """通过DSN ID获取指定文件的前n行

    Args:
        id: DSN ID
        file_path: 文件路径
        n: 要获取的行数，默认100行 (可选参数)

    Returns:
        文件前n行内容
    """
    dsn = Dsn.get(id=id)
    if not dsn:
        return RESOURCE_NOT_FOUND(detail=f'dsn id {id} not found')

    try:
        connect_info = json.loads(dsn.connect)
        if not connect_info.get('protocol'):
            return ILLEGAL_PARAMS(detail=f'DSN id {id} does not have protocol specified')
    except json.JSONDecodeError:
        return ILLEGAL_PARAMS(detail=f'Invalid DSN connection info format')

    # 获取可选的行数参数
    n = request.args.get('n', 100)
    try:
        n = int(n)
        if n <= 0:
            return ILLEGAL_PARAMS(detail='Parameter n must be a positive integer')
    except ValueError:
        return ILLEGAL_PARAMS(detail='Parameter n must be a valid integer')

    ftp_client = None
    try:
        ftp_client = RemoteFileServer.get_connect(connect_info)
        ftp_client.open()

        # 检查文件是否存在
        if not ftp_client.exists(params['file_path']):
            return RESOURCE_NOT_FOUND(detail=f'File {params["file_path"]} not found')

        content = ftp_client.headn(params['file_path'], n=n)
        log.info(f'user {user.uid} read first {n} lines from file {params["file_path"]} via dsn id {id}')
        return SUCCESS(data={
            'file_path': params['file_path'],
            'lines_requested': n,
            'content': content
        })
    except Exception as e:
        log.error(f'Failed to read first {n} lines from file {params["file_path"]} on FTP server: {str(e)}')
        return ILLEGAL_PARAMS(detail=str(e))
    finally:
        if ftp_client:
            try:
                ftp_client.close()
            except Exception as e:
                log.error(f'Failed to close FTP connection: {str(e)}')

@api.route('/headn/<string:name>', methods=['GET'])
@login_required
@params_required('file_path')
def headn_file_by_name(user, name, **params):
    """通过DSN名称获取指定文件的前n行

    Args:
        name: DSN名称
        file_path: 文件路径
        n: 要获取的行数，默认100行 (可选参数)

    Returns:
        文件前n行内容
    """
    dsn = Dsn.get_one(name=name)
    if not dsn:
        return RESOURCE_NOT_FOUND(detail=f'dsn {name} not found')

    try:
        connect_info = json.loads(dsn.connect)
        if not connect_info.get('protocol'):
            return ILLEGAL_PARAMS(detail=f'DSN {name} does not have protocol specified')
    except json.JSONDecodeError:
        return ILLEGAL_PARAMS(detail=f'Invalid DSN connection info format')

    # 获取可选的行数参数
    n = request.args.get('n', 100)
    try:
        n = int(n)
        if n <= 0:
            return ILLEGAL_PARAMS(detail='Parameter n must be a positive integer')
    except ValueError:
        return ILLEGAL_PARAMS(detail='Parameter n must be a valid integer')

    ftp_client = None
    try:
        ftp_client = RemoteFileServer.get_connect(connect_info)
        ftp_client.open()

        # 检查文件是否存在
        if not ftp_client.exists(params['file_path']):
            return RESOURCE_NOT_FOUND(detail=f'File {params["file_path"]} not found')

        content = ftp_client.headn(params['file_path'], n=n)
        log.info(f'user {user.uid} read first {n} lines from file {params["file_path"]} via dsn {name}')
        return SUCCESS(data={
            'file_path': params['file_path'],
            'lines_requested': n,
            'content': content
        })
    except Exception as e:
        log.error(f'Failed to read first {n} lines from file {params["file_path"]} on FTP server: {str(e)}')
        return ILLEGAL_PARAMS(detail=str(e))
    finally:
        if ftp_client:
            try:
                ftp_client.close()
            except Exception as e:
                log.error(f'Failed to close FTP connection: {str(e)}')

@api.route('/headn/direct', methods=['GET'])
@login_required
@params_required('protocol', 'ip', 'port', 'username', 'password', 'file_path')
def headn_file_direct(user, **params):
    """通过直接提供的连接信息获取指定文件的前n行

    Args:
        protocol: 协议类型 (通过URL参数传递)
        ip: FTP服务器IP (通过URL参数传递)
        port: FTP服务器端口 (通过URL参数传递)
        username: 用户名 (通过URL参数传递)
        password: 密码 (通过URL参数传递)
        file_path: 文件路径 (通过URL参数传递)
        n: 要获取的行数，默认100行 (可选参数)

    Returns:
        文件前n行内容
    """
    try:
        params['port'] = int(params['port'])
    except ValueError:
        return ILLEGAL_PARAMS(detail='Invalid port number')

    # 获取可选的行数参数
    n = request.args.get('n', 100)
    try:
        n = int(n)
        if n <= 0:
            return ILLEGAL_PARAMS(detail='Parameter n must be a positive integer')
    except ValueError:
        return ILLEGAL_PARAMS(detail='Parameter n must be a valid integer')

    ftp_client = None
    try:
        ftp_client = RemoteFileServer.get_connect(params)
        ftp_client.open()

        # 检查文件是否存在
        if not ftp_client.exists(params['file_path']):
            return RESOURCE_NOT_FOUND(detail=f'File {params["file_path"]} not found')

        content = ftp_client.headn(params['file_path'], n=n)
        log.info(f'user {user.uid} read first {n} lines from file {params["file_path"]} via direct connection')
        return SUCCESS(data={
            'file_path': params['file_path'],
            'lines_requested': n,
            'content': content
        })
    except Exception as e:
        log.error(f'Failed to read first {n} lines from file {params["file_path"]} on FTP server: {str(e)}')
        return ILLEGAL_PARAMS(detail=str(e))
    finally:
        if ftp_client:
            try:
                ftp_client.close()
            except Exception as e:
                log.error(f'Failed to close FTP connection: {str(e)}')
