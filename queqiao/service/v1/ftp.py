"""
Author: xiaohei
Date: 2025/01/20
Email: <EMAIL>
Host: xiaohei.info
"""
from flask import request

from queqiao.conf.ApiResponse import SUCCESS, RESOURCE_NOT_FOUND, ILLEGAL_PARAMS
from queqiao.core.auth.entity import login_required
from queqiao.dba.models import Dsn
from queqiao.log import LogFactory
from queqiao.service.v1 import regist_api
from queqiao.util.comm.requtil import params_required
from queqiao.util.conn.ftp import RemoteFileServer
import json

api = regist_api(__name__)
log = LogFactory.get_logger()

@api.route('/test/<string:name>', methods=['GET'])
@login_required
def test_dsn_connection(user, name):
    """测试指定DSN名称的FTP连接"""
    dsn = Dsn.get_one(name=name)
    if not dsn:
        return RESOURCE_NOT_FOUND(detail=f'dsn {name} not found')
    
    try:
        connect_info = json.loads(dsn.connect)
        if not connect_info.get('protocol'):
            return ILLEGAL_PARAMS(detail=f'DSN {name} does not have protocol specified')
    except json.JSONDecodeError:
        return ILLEGAL_PARAMS(detail=f'Invalid DSN connection info format')
    
    is_connected, error_msg = RemoteFileServer.test_connection(connect_info)
    log.info(f'user {user.uid} test connection for dsn {name} - {"Success" if is_connected else f"Failed: {error_msg}"}')
    return SUCCESS(data={
        'connected': is_connected,
        'error': error_msg
    })

@api.route('/test/id/<int:id>', methods=['GET'])
@login_required
def test_dsn_connection_by_id(user, id):
    """测试指定DSN ID的FTP连接"""
    dsn = Dsn.get(id=id)
    if not dsn:
        return RESOURCE_NOT_FOUND(detail=f'dsn id {id} not found')
    
    try:
        connect_info = json.loads(dsn.connect)
        if not connect_info.get('protocol'):
            return ILLEGAL_PARAMS(detail=f'DSN id {id} does not have protocol specified')
    except json.JSONDecodeError:
        return ILLEGAL_PARAMS(detail=f'Invalid DSN connection info format')
    
    is_connected, error_msg = RemoteFileServer.test_connection(connect_info)
    log.info(f'user {user.uid} test connection for dsn id {id} - {"Success" if is_connected else f"Failed: {error_msg}"}')
    return SUCCESS(data={
        'connected': is_connected,
        'error': error_msg
    })

@api.route('/test', methods=['POST'])
@login_required
@params_required('protocol', 'ip', 'port', 'username', 'password', 'work_dir')
def test_connection(user, **params):
    """测试直接提供的FTP连接信息"""
    try:
        params['port'] = int(params['port'])
    except ValueError:
        return ILLEGAL_PARAMS(detail='Invalid port number')
    
    is_connected, error_msg = RemoteFileServer.test_connection(params)
    log.info(f'user {user.uid} test direct connection to {params["ip"]}:{params["port"]} - {"Success" if is_connected else f"Failed: {error_msg}"}')
    return SUCCESS(data={
        'connected': is_connected,
        'error': error_msg
    })

@api.route('/list/id/<int:id>', methods=['GET'])
@login_required
@params_required('ftp_path')
def list_ftp_content(user, id, **params):
    """获取指定DSN ID的FTP服务器上某个路径下的所有内容

    Args:
        id: DSN ID
        ftp_path: FTP服务器上的路径

    Returns:
        文件列表，每个文件包含名称和类型信息
    """
    dsn = Dsn.get(id=id)
    if not dsn:
        return RESOURCE_NOT_FOUND(detail=f'dsn id {id} not found')
    
    try:
        connect_info = json.loads(dsn.connect)
        if not connect_info.get('protocol'):
            return ILLEGAL_PARAMS(detail=f'DSN id {id} does not have protocol specified')
    except json.JSONDecodeError:
        return ILLEGAL_PARAMS(detail=f'Invalid DSN connection info format')
    
    ftp_client = None
    try:
        ftp_client = RemoteFileServer.get_connect(connect_info)
        ftp_client.open()
        file_list = ftp_client.list_dir_info(params['ftp_path'])
        return SUCCESS(data={
            'files': file_list
        })
    except Exception as e:
        log.error(f'Failed to list directory {params["ftp_path"]} on FTP server: {str(e)}')
        return ILLEGAL_PARAMS(detail=str(e))
    finally:
        if ftp_client:
            try:
                ftp_client.close()
            except Exception as e:
                log.error(f'Failed to close FTP connection: {str(e)}')

@api.route('/list/<string:name>', methods=['GET'])
@login_required
@params_required('ftp_path')
def list_ftp_content_by_name(user, name, **params):
    """获取指定DSN名称的FTP服务器上某个路径下的所有内容

    Args:
        name: DSN名称
        ftp_path: FTP服务器上的路径

    Returns:
        文件列表，每个文件包含名称和类型信息
    """
    dsn = Dsn.get_one(name=name)
    if not dsn:
        return RESOURCE_NOT_FOUND(detail=f'dsn {name} not found')
    
    try:
        connect_info = json.loads(dsn.connect)
        if not connect_info.get('protocol'):
            return ILLEGAL_PARAMS(detail=f'DSN {name} does not have protocol specified')
    except json.JSONDecodeError:
        return ILLEGAL_PARAMS(detail=f'Invalid DSN connection info format')
    
    ftp_client = None
    try:
        ftp_client = RemoteFileServer.get_connect(connect_info)
        ftp_client.open()
        file_list = ftp_client.list_dir_info(params['ftp_path'])
        return SUCCESS(data={
            'files': file_list
        })
    except Exception as e:
        log.error(f'Failed to list directory {params["ftp_path"]} on FTP server: {str(e)}')
        return ILLEGAL_PARAMS(detail=str(e))
    finally:
        if ftp_client:
            try:
                ftp_client.close()
            except Exception as e:
                log.error(f'Failed to close FTP connection: {str(e)}')

@api.route('/list/direct', methods=['GET'])
@login_required
@params_required('protocol', 'ip', 'port', 'username', 'password', 'ftp_path')
def list_ftp_content_direct(user, **params):
    """通过直接提供的连接信息获取FTP服务器上某个路径下的所有内容

    Args:
        protocol: 协议类型 (通过URL参数传递)
        ip: FTP服务器IP (通过URL参数传递)
        port: FTP服务器端口 (通过URL参数传递)
        username: 用户名 (通过URL参数传递)
        password: 密码 (通过URL参数传递)
        ftp_path: FTP服务器上的路径 (通过URL参数传递)

    Returns:
        文件列表，每个文件包含名称和类型信息
    """
    try:
        params['port'] = int(params['port'])
    except ValueError:
        return ILLEGAL_PARAMS(detail='Invalid port number')
    
    ftp_client = None
    try:
        ftp_client = RemoteFileServer.get_connect(params)
        ftp_client.open()
        file_list = ftp_client.list_dir_info(params['ftp_path'])
        return SUCCESS(data={
            'files': file_list
        })
    except Exception as e:
        log.error(f'Failed to list directory {params["ftp_path"]} on FTP server: {str(e)}')
        return ILLEGAL_PARAMS(detail=str(e))
    finally:
        if ftp_client:
            try:
                ftp_client.close()
            except Exception as e:
                log.error(f'Failed to close FTP connection: {str(e)}')
