"""
Author: xiaohei
Date: 2022/6/13
Email: <EMAIL>
Host: xiaohei.info
"""
from queqiao.conf.ApiResponse import SUCCESS, RESOURCE_EXISTS, NO_PRIVILEGE, RESOURCE_NOT_FOUND
from queqiao.conf.enums import PermissionStatus, LinuxPermission
from queqiao.core.auth.entity import login_required
from queqiao.dba.extend_model import TaskPermission, Task
from queqiao.log import LogFactory
from queqiao.service.v1 import regist_api
from queqiao.util.comm.requtil import params_required

api = regist_api(__name__)

log = LogFactory.get_logger()

required_fileds, optional_fields = TaskPermission.get_required_and_optional_fields()


@api.route('/', methods=['GET'])
@login_required
@params_required('task_id')
def get_permissions(user, task_id):
    task_permissions = TaskPermission.get(task_id=task_id)
    permissions = []
    for permission in task_permissions:
        pdict = permission.to_dict()
        pdict.pop('task_id')
        permissions.append(pdict)
    return SUCCESS(data={'task_id': task_id, 'permissions': permissions})


@api.route('/', methods=['POST'])
@login_required
@params_required('task_id', 'permission')
def add_permission(user, task_id, permission):
    exists_permissions = TaskPermission.get(task_id=task_id, user_id=user.uid)
    if len(exists_permissions) > 0:
        return RESOURCE_EXISTS(detail=f'user {user.uid} in task {task_id} already had a permission!')
    task = Task.get(id=task_id)
    if not task:
        return RESOURCE_NOT_FOUND(detail=f'task_id {task_id}')
    managers = task.project.admin_list
    managers.append(task.create_user)
    managers = list(set(managers))
    status = PermissionStatus.APPROVING if user.uid not in managers else PermissionStatus.ENTABLE
    task_permission = TaskPermission.new(user_id=user.uid, task_id=task_id,
                                         permission=TaskPermission.encode_permission(**permission),
                                         status=status.value)
    task_permission.save()
    log.info(f'user {user.uid} add a new permission for task {task_id}, permission: {permission}')
    if user.uid not in managers:
        task_permission.send_approve()
    return SUCCESS(data={'id': task_permission.id})


@api.route('/<int:id>', methods=['GET'])
@login_required
def get_permission(user, id):
    task_permission = TaskPermission.get(id=id)
    if not task_permission:
        raise RESOURCE_NOT_FOUND(detail=f'task permission: {id}')
    return SUCCESS(data=task_permission)


@api.route('/<int:id>', methods=['POST'])
@login_required
@params_required('permission')
def mod_permission(user, id, permission):
    task_permission = TaskPermission.get(id=id)
    if not task_permission:
        raise RESOURCE_NOT_FOUND(detail=f'task permission: {id}')
    old_permission = task_permission.permission
    status = PermissionStatus.APPROVING if user.uid not in task_permission.managers else PermissionStatus.ENTABLE

    task_permission.permission = TaskPermission.encode_permission(**permission)
    task_permission.status = status.value
    task_permission.save()
    new_permission = task_permission.permission
    log.info(
        f'user {user.uid} update task permission(user_id={task_permission.user_id},'
        f'task_id={task_permission.task_id}) from {LinuxPermission(old_permission)} to {LinuxPermission(new_permission)}')
    if user.uid not in task_permission.managers:
        task_permission.send_approve()
    return SUCCESS()


@api.route('/approve/<int:id>', methods=['POST'])
@login_required
@params_required('action')
def approve(user, id, action):
    action = int(action)
    task_permission = TaskPermission.get(id=id)
    if not task_permission:
        raise RESOURCE_NOT_FOUND(detail=f'task permission: {id}')
    if user.uid not in task_permission.managers:
        return NO_PRIVILEGE()
    old_status = PermissionStatus(task_permission.status)
    new_status = PermissionStatus.ENTABLE if PermissionStatus(
        action) == PermissionStatus.ENTABLE else PermissionStatus.REJECT
    task_permission.status = new_status.value
    task_permission.save()
    log.info(f'user {user.uid} set task permission {id} status from {old_status} to {new_status}')
    task_permission.send_approve_result(user.uid)
    return SUCCESS()


@api.route('/<int:id>', methods=['DELETE'])
@login_required
def del_permission(user, id):
    task_permission = TaskPermission.get(id=id)
    if not task_permission:
        raise RESOURCE_NOT_FOUND(detail=f'task permission: {id}')
    if user.uid not in task_permission.managers:
        return NO_PRIVILEGE()
    task_permission.safety_delete()
    log.info(f'user {user.uid} delete task permission {id}')
    return SUCCESS()
