"""
Author: xiaohei
Date: 2022/5/16
Email: <EMAIL>
Host: xiaohei.info
"""
from flask import request

from queqiao.conf.ApiResponse import SUCCESS
from queqiao.core.auth.entity import login_required
from queqiao.dba.models import TaskType, Engine
from queqiao.log import LogFactory
from queqiao.service import update_db_obj, add_db_obj
from queqiao.service.v1 import regist_api
from queqiao.util.comm.requtil import params_required

api = regist_api(__name__)

log = LogFactory.get_logger()

required_fileds, optional_fields = TaskType.get_required_and_optional_fields()


@api.route('/', methods=['GET'])
@login_required
def get_all_task_types(user):
    task_types = TaskType.get()
    log.info(f'user {user.uid} get {len(task_types)} task types')
    return SUCCESS(data=task_types)


@api.route('/<int:id>', methods=['GET'])
@login_required
def get_task_type(user, id):
    task_type = TaskType.get(id=id)
    engines = []
    for engine_name in task_type.engines.split(','):
        engine = Engine.get_one(name=engine_name)
        engines.append(engine.to_dict(includes=['id', 'name']))
    data = task_type.to_dict()
    data['engines'] = engines
    return SUCCESS(data=data)


@api.route('/', methods=['POST'])
@login_required
@params_required(*required_fileds)
def add_task_type(user, **params):
    return add_db_obj(request, user, TaskType, params, optional_fields, f'{params["name"]} 任务类型')


@api.route('/<int:id>', methods=['POST'])
@login_required
def update_task_type(user, id):
    task_type = TaskType.get(id=id)
    return update_db_obj(request, user, task_type, required_fileds, optional_fields, f'{id} 任务类型')
