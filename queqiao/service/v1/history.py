"""FTP历史文件记录接口

Author: xiaohei
Date: 2025/01/20
Email: <EMAIL>
Host: xiaohei.info
"""
from flask import request

from queqiao.conf.ApiResponse import SUCCESS, RESOURCE_NOT_FOUND, INTERVAL_ERROR
from queqiao.core.auth.entity import login_required
from queqiao.dba.models import FtplinkHistoryFile
from queqiao.service.v1 import regist_api
from queqiao.log import LogFactory

api = regist_api(__name__)
log = LogFactory.get_logger()
@api.route('/file/<int:file_id>', methods=['GET'])
@login_required
def get_history_file(user, file_id):
    """获取单个FTP历史文件记录
    
    Args:
        user: 当前登录用户
        file_id: 文件ID
        
    Returns:
        单个文件记录详情
    """
    try:
        record = FtplinkHistoryFile.get(id=file_id)
        if not record:
            log.warning(f'user {user.uid} tried to access non-existent history file {file_id}')
            return RESOURCE_NOT_FOUND(detail=f'文件记录 {file_id} 不存在')
        
        log.info(f'user {user.uid} accessed history file {file_id}')
        return SUCCESS(data=record.to_dict())
    except Exception as e:
        log.error(f'Error when getting history file {file_id}: {str(e)}')
        return INTERVAL_ERROR(detail=str(e))

@api.route('/file', methods=['GET'])
@login_required
def list_history_files(user):
    """获取FTP历史文件记录列表
    
    Args:
        user: 当前登录用户
        
    Returns:
        文件记录列表，支持分页和文件名搜索
    """
    try:
        page = request.args.get('page', 1, type=int)
        per_page = request.args.get('per_page', 10, type=int)
        query = FtplinkHistoryFile.query
        
        filename = request.args.get('filename')
        if filename:
            query = query.filter(FtplinkHistoryFile.filename.like(f'%{filename}%'))
            
        pagination = query.paginate(page=page, per_page=per_page)
        log.info(f'user {user.uid} listed history files, page={page}, per_page={per_page}, filename_filter={filename}')
        
        return SUCCESS(data={
            'items': [item.to_dict() for item in pagination.items],
            'total': pagination.total,
            'page': page,
            'per_page': per_page
        })
    except Exception as e:
        log.error(f'Error when listing history files: {str(e)}')
        return INTERVAL_ERROR(detail=str(e))

@api.route('/file/execution/<int:execution_id>', methods=['GET'])
@login_required
def get_history_files_by_execution(user, execution_id):
    """通过execution_id获取FTP历史文件记录列表
    
    Args:
        user: 当前登录用户
        execution_id: 执行ID
        
    Returns:
        与该execution_id关联的所有文件记录列表
    """
    try:
        records = FtplinkHistoryFile.query.filter_by(execution_id=execution_id).all()
        if not records:
            log.warning(f'user {user.uid} tried to access history files with non-existent execution_id {execution_id}')
            return RESOURCE_NOT_FOUND(detail=f'未找到execution_id为 {execution_id} 的文件记录')
        
        log.info(f'user {user.uid} accessed history files for execution_id {execution_id}')
        return SUCCESS(data=[record.to_dict() for record in records])
    except Exception as e:
        log.error(f'Error when getting history files for execution_id {execution_id}: {str(e)}')
        return INTERVAL_ERROR(detail=str(e))