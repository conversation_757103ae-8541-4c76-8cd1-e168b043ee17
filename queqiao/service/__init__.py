"""
Author: xiaohei
Date: 2022/4/19
Email: <EMAIL>
Host: xiaohei.info
"""
import traceback

from queqiao.conf.ApiResponse import SERVER_NOT_FOUND, SUCCESS, RESOURCE_EXISTS
from queqiao.log import LogFactory
from queqiao.util.comm.requtil import get_request_param

log = LogFactory.get_logger()


def add_db_obj(request, user, cls, required_params, optional_fields, msg=None):
    try:
        obj = cls.new(create_user=user.uid, update_user=user.uid)
        for key in optional_fields:
            value = get_request_param(request, key)
            if value is not None:
                required_params[key] = value
        log.debug(f'get add_db_obj params: {required_params}')
        for key in required_params.keys():
            value = required_params[key]
            setattr(obj, key, value)
        obj.save()
        return SUCCESS(data={'id': obj.id})
    except Exception as why:
        log.error(traceback.format_exc())

        return RESOURCE_EXISTS(detail=why)


def update_db_obj(request, user, obj, required_fileds, optional_fields, msg=None):
    if not obj:
        return SERVER_NOT_FOUND(msg)

    required_fileds.extend(optional_fields)
    params = {}
    for f in required_fileds:
        v = get_request_param(request, f, None)
        if v is not None:
            params[f] = v
    log.debug(f'get update_db_obj params: {params}')
    for key in params.keys():
        value = params[key]
        setattr(obj, key, value)
    obj.update_user = user.uid
    obj.save()
    return SUCCESS(data={'id': obj.id})


def delete_db_obj(user, obj, msg):
    if not obj:
        return SERVER_NOT_FOUND(detail=msg)
    obj.update_user = user.uid
    obj.safety_delete()
    return SUCCESS(data={'id': obj.id})
