# coding=utf-8
# type: ignore
import os
from logging.config import dictConfig

from flask import Flask
from flask.logging import default_handler

from instance.default import TMP_FOLDER, API_VERSION, system_env, LOG_PATH, EXE_LOG_PATH, CONF_PATH
from queqiao.conf.env import curr_queqiao_app
from queqiao.conf.hooks import Hook
from queqiao.conf.system import SystemConfig
from queqiao.log import LogFactory
from queqiao.util.comm import objutil, osutil

# db = RouteSQLAlchemy(
#     query_class=CustomQuery, session_options={'expire_on_commit': False}
# )

file_storage = None
# redis = None

log = LogFactory.get_logger()


class FlaskAppFactory:

    def __init__(self, queqiao_app=None):
        self.app = Flask(__name__, instance_relative_config=True)
        self.env = system_env
        self.qapp = queqiao_app if queqiao_app else curr_queqiao_app
        log.info(f'init flask app with env: {self.env}, queqiao app is: {self.qapp}')
        need_dirs = [TMP_FOLDER, LOG_PATH, EXE_LOG_PATH]
        for d in need_dirs:
            if not os.path.exists(d):
                os.mkdir(d)

    def __init_conf(self):
        self.app.config.from_pyfile('default.py')
        log.info(f'load default configs from default.py')
        self.app.config.from_pyfile(f'env/{self.env}.py')
        log.info(f'load env configs from env/{self.env}.py')
        os.environ['CELERY_BROKER_URL'] = self.app.config.get('CELERY_BROKER_URL')

    def __init_log(self):
        '''
        https://flask.palletsprojects.com/en/1.0.x/logging/

        When you want to configure logging for your project, you
        should do it as soon as possible when the program starts.
        '''
        dictConfig(self.app.config.get('LOGGING_SETTINGS', {}))  # type: ignore
        self.app.logger.removeHandler(default_handler)

    def __init_db(self):
        '''
        初始化数据库相关配置，并且所有model应该在这里被导入，
        不然理论上不会生效。
        '''
        from queqiao.dba import db
        db.init_app(self.app)
        self.app.app_context().push()

    def __init_blueprint(self):
        from queqiao import service
        api_package = objutil.new_mdl_instance(f'{service.__package__}.{API_VERSION}')
        log.info(f'search api blueprints in {api_package.__path__}, {api_package.__package__}')
        api_modules = objutil.find_mdl_in_pkg(api_package.__path__, api_package.__package__)
        for mdl in api_modules.keys():
            api = getattr(api_modules[mdl], 'api')
            self.app.register_blueprint(api)

    def __init_hook(self):
        Hook(self.app).hookup()

    def __init_debugtool(self):
        if self.app.config.get('DEBUG', False):
            self.app.debug = True
            self.app.config['SECRET_KEY'] = 'QUEQIAO'
            from flask_debugtoolbar import DebugToolbarExtension
            toolbar = DebugToolbarExtension()
            toolbar.init_app(self.app)

    def __init(self, need_inits):
        total_steps = len(need_inits)
        curr_step = 1
        for func in need_inits:
            func()
            log.info(f'#####init progress[{curr_step}/{total_steps}]##### {func}')
            curr_step += 1

    def __call__(self):
        need_inits = [
            self.__init_conf,
            self.__init_log,
            self.__init_db,
        ]
        if self.qapp in ['api', 'console']:
            log.info(f'current queqiao app {self.qapp} use for web, add blueprints to init func')
            need_inits.extend([
                self.__init_blueprint,
                # self.__init_cli,
                self.__init_hook,
                self.__init_debugtool
            ])
        self.__init(need_inits)
        return self.app
