# coding=utf-8
import json

from flask import request, jsonify

from queqiao.conf.ApiResponse import DO_NOTHING, SUCCESS, LOGIN_FAILED
from queqiao.core.auth.entity import User
from queqiao.log import LogFactory

log = LogFactory.get_logger()


def login():
    try:
        username = request.json.get('username')
        password = request.json.get('password')
        log.debug(f'login ldap with username: {username}, password: {password}')
        user = User(username=username, password=password)
        if user:
            data = {
                'uid': user.uid,
                'gid': user.gid,
                'aid': user.aid,
                'token': user.token,
                'projects': json.dumps(user.projects),
                'groups': json.dumps(user.groups)
            }
            return SUCCESS(data=data)
        else:
            return LOGIN_FAILED()
    except Exception as _:
        log.exception('login exception!')
        return LOGIN_FAILED()


def logincallback():
    return DO_NOTHING()


def logout():
    return DO_NOTHING()
