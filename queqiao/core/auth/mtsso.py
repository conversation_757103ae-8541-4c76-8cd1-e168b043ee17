# coding=utf-8

import json
import time
from urllib.parse import urlencode

import requests
from flask import request, redirect, url_for, abort, make_response

from instance.default import SSO_ACCESS_TOKEN_URL, SSO_API_TOKEN, SSO_LOGIN_URL, SSO_LOGOUT_URL
from instance.default import API_VERSION
from queqiao.core.auth import sso_api_auth
from queqiao.core.auth.entity import User
from queqiao.log import LogFactory

log = LogFactory.get_logger()


# todo: 线上测试
def login():
    data = dict(
        t=int(time.time() * 1000),
        redirect_uri='http://%s%s' % (request.host, url_for(f'_api_{API_VERSION}_sso.login')),
        client_id=SSO_API_TOKEN['client_id']
    )
    login_url = "%s?%s" % (SSO_LOGIN_URL, urlencode(data))
    log.debug(f'login mtsso with url: {SSO_LOGIN_URL}, data: {data}')
    return redirect(login_url)


def logincallback():
    log.debug('mtsso login callback')
    data = dict(
        t=int(time.time() * 1000),
        code=request.args.get('code')
    )
    get_access_token_url = "%s?%s" % (SSO_ACCESS_TOKEN_URL, urlencode(data))
    headers = {'content-type': 'application/json'}
    r = requests.get(get_access_token_url, headers=headers, auth=sso_api_auth).json()
    if r.get('code', 0) == 200:
        sid = r.get('data', {}).get('accessToken', 'accessToken not exist')
        redirect_path = request.cookies.get('queqiao_redirect_path') or '/'
        resp = make_response(redirect(redirect_path))
        user = User(sid=sid)
        resp.set_cookie('sid', sid)
        resp.set_cookie('uid', user.uid)
        resp.set_cookie('gid', user.gid)
        resp.set_cookie('aid', user.aid)
        resp.set_cookie('token', user.token)
        resp.set_cookie('projects', user.projects)
        resp.set_cookie('groups', user.groups)
        return resp
    elif r.get('code', 0) == 10008:
        log.info(f'response code is 10008, redirect to login method: _api_{API_VERSION}_sso.login')
        resp = make_response(redirect(url_for(f'_api_{API_VERSION}_sso.login')))
        return resp
    else:
        raise abort(401)  # Unauthorized


def logout():
    if request.user.authorized:
        log.info(f'user {request.user.uid} logout')
        headers = {'content-type': 'application/json'}
        data = json.dumps({'accessToken': request.user.sid})
        r = requests.post(SSO_LOGOUT_URL, data=data, headers=headers, auth=sso_api_auth)
        success = (r.json().get('code', '') == 200)
        if not success:
            raise abort(500)
    return redirect(url_for(f'_api_{API_VERSION}_sso.login'))
