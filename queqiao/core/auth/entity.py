"""
Author: xia<PERSON>ei
Date: 2022/4/19
Email: <EMAIL>
Host: xiaohei.info
"""
from dataclasses import dataclass
from functools import wraps

from flask import request

from queqiao.conf.ApiResponse import NO_PRIVILEGE
from queqiao.conf.errors import AuthFailedException, NotFoundException
from queqiao.core.auth import Certifier


# SSO增加BA认证, https://km.sankuai.com/page/58995631


# user中不能使用logegr打印日志，logger format中使用了user.login 该属性需要init操作，会执行到当前代码中，如果当前的代码再使用logger打印日志则会进入无限循环直到栈溢出
@dataclass
class User(object):
    uid: str
    gid: str
    aid: str
    token: str
    projects: list
    groups: list

    def __init__(self, **config):
        self.__inited = False
        self.__author = Certifier.get_author(config)
        if not self.__author.verify_certificate():
            raise AuthFailedException(f'user auth failed')
        self.__author.init_user_properties(self)
        self.__set_aid()
        self.__set_token()
        self.__set_projects()
        self.__inited = True

    def __set_token(self):
        if not self.aid:
            self.__set_aid()
        self.token = Certifier.get_user_token(self.uid, self.gid, self.aid)

    def __set_aid(self):
        self.aid = Certifier.get_user_aid(self.groups)

    def __set_projects(self):
        # 有cookie时以cookie内容为准
        if not hasattr(self, 'projects'):
            from queqiao.dba.models import ProjectUserRelation
            project_relations = ProjectUserRelation.get(user_id=self.uid)
            projects = []
            for r in project_relations:
                projects.append({
                    'id': r.project_id,
                    'name': r.project.name,
                    'selected': r.is_default
                })
            self.projects = projects

    @property
    def unauthorized_resp(self):
        return self.__author.not_auth_resp()

    @property
    def authorized(self):
        return self.__inited

    @property
    def is_datard(self):
        return True if 'queqiao_user_rd' in self.groups else False

    @property
    def is_sysrd(self):
        return True if 'queqiao_user_sys' in self.groups else False

    @property
    def is_admin(self):
        return True if self.aid == '1' else False

    @property
    def project_id(self):
        selected_project = None
        for project in self.projects:
            if project['selected'] > 0:
                selected_project = project['id']
                break
        if not selected_project:
            raise NotFoundException(f'default project not found!')
        return selected_project


def datard_required(f):
    @wraps(f)
    def wrapper(*args, **kwargs):
        user = request.user
        if user.is_datard:
            return f(*args, **kwargs)
        else:
            return NO_PRIVILEGE()

    return wrapper


def sysrd_required(f):
    @wraps(f)
    def wrapper(*args, **kwargs):
        user = request.user
        if user.is_sysrd:
            return f(*args, **kwargs)
        else:
            return NO_PRIVILEGE()

    return wrapper


def admin_required(f):
    @wraps(f)
    def wrapper(*args, **kwargs):
        user = request.user
        if user.is_admin:
            return f(*args, **kwargs)
        else:
            return NO_PRIVILEGE()

    return wrapper


def login_required(f):
    """
    用于校验前端页面内的ajax请求 的校验
    :param f:
    :return:
    """

    @wraps(f)
    def wrapper(*args, **kwargs):
        user = User()
        request.user = user
        if not user.authorized:
            return user.unauthorized_resp
        return f(user, *args, **kwargs)

    return wrapper
