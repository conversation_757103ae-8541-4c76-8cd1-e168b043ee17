import abc
import json
from urllib import parse

import requests
import six
from flask import request, make_response, url_for, redirect

from instance.default import SSO_API_TOKEN, SSO_USER_INFO_API, API_VERSION
from queqiao.conf.ApiResponse import LOGIN_FAILED
from queqiao.conf.errors import InternalException, IllegalParamsException, AuthFailedException
from queqiao.conf.system import SystemConfig
from queqiao.log import LogFactory
from queqiao.util.comm import objutil, strutil
from queqiao.util.conn.ldap import LdapAuth
from queqiao.util.mt.mwsauth import MWSAuth
from queqiao.util.mt.org import OrgClient

sso_api_auth = MWSAuth(client_id=SSO_API_TOKEN['client_id'],
                       client_secret=SSO_API_TOKEN['client_secret'])
log = LogFactory.get_logger()


@six.add_metaclass(abc.ABCMeta)
class Certifier:
    def __init__(self, config):
        miss_configs = []
        for c in self._get_required_configs():
            if c not in config:
                miss_configs.append(c)
        if len(miss_configs) > 0:
            raise InternalException(f'config {miss_configs} is missing')
        self._config = config
        log.debug(f'init Certifier with config: {self._config}')

    @classmethod
    def get_author(cls, config, author_type=None):
        # 没有参数时从cookie中认证
        tcls = ('Cookie' if not config else (
            'MTSSO' if 'sid' in config else SystemConfig.read('USER_AUTH_TYPE'))) if not author_type else author_type
        tcls = f'{tcls}{cls.__name__}'
        log.debug(f'new instance for {tcls}')
        return objutil.new_instance(__name__, tcls, config)

    @classmethod
    def get_user_token(cls, uid, gid, aid):
        # gid: uid_number, aid: queqiao_admin in entry.groups, salt: ARD15Xtf
        salt = SystemConfig.read('COOKIE_SALT')
        mix = uid + str(gid) + aid + salt
        return strutil.md5(mix)

    @classmethod
    def get_user_aid(cls, user_groups):
        return '1' if SystemConfig.read('ADMIN_GROUP') in user_groups else '0'

    @abc.abstractmethod
    def verify_certificate(self):
        pass

    @abc.abstractmethod
    def init_user_properties(self, user):
        pass

    def not_auth_resp(self):
        return LOGIN_FAILED()

    @abc.abstractmethod
    def _get_required_configs(self):
        pass


class LDAPCertifier(Certifier):
    def __init__(self, config):
        super(LDAPCertifier, self).__init__(config)
        ldap_config = json.loads(SystemConfig.read('LDAP_REQUIRED_CONFIGS'))
        log.debug(f'get ldap config: {ldap_config}, init ldap connector')
        try:
            self.__ldap = LdapAuth(**ldap_config)
        except Exception as why:
            raise IllegalParamsException(str(why))

    def _get_required_configs(self):
        return ['username', 'password']

    def verify_certificate(self):
        try:
            auth, entry = self.__ldap.auth_user(self._config['username'], self._config['password'])
            self.__entry = entry
            log.debug(f'ldap verify result: {auth}, {entry}')
        except Exception as why:
            raise AuthFailedException(str(why))
        return auth

    def init_user_properties(self, user):
        log.debug(f'ldap entry attrs: {dir(self.__entry)}, set attrs to user obj')
        if self.__entry:
            for a in dir(self.__entry):
                if not a.startswith('__'):
                    setattr(user, a, getattr(self.__entry, a))


class MTSSOCertifier(Certifier):
    def _get_required_configs(self):
        return ['sid']

    def verify_certificate(self):
        data = json.dumps({'accessToken': self._config['sid']})
        headers = {'content-type': 'application/json'}
        log.debug(f'start post request to {SSO_USER_INFO_API} with data: {data}')
        resp = requests.post(SSO_USER_INFO_API, data=data, headers=headers, auth=sso_api_auth)
        r = resp.json()
        self.debug = r.get('data', {})
        log.info(f'get response: {self.__data}')
        return r.get('code', 0) == 200

    def init_user_properties(self, user):
        org_client = OrgClient.from_flask_config()
        user.uid = self.__data.get('loginName')
        user.gid = self.__data.get('uid')
        _, dep = org_client.get_emp_info(user.uid)
        mt_department_groups = json.loads(SystemConfig.read('MT_DEPARTMENT_GROUPS'))
        user.groups = mt_department_groups.get(dep, ['queqiao_user_bs'])
        if user.uid in SystemConfig.read('SYS_ADMIN'):
            user.groups.append(SystemConfig.read('ADMIN_GROUP'))

    def not_auth_resp(self):
        resp = make_response(redirect(url_for(f'_api_{API_VERSION}_sso.login') + "?call_back_path=%s" % request.path))
        resp.set_cookie('queqiao_redirect_path', request.url, max_age=300)
        return resp


class CookieCertifier(Certifier):
    def _get_required_configs(self):
        return []

    def verify_certificate(self):
        # 用户信息发生变化的需要退出重新登录，或者等待cookie失效
        cookie_token = request.cookies.get('token')
        log.info(f'get token: {cookie_token} from cookie')
        if not cookie_token:
            return False
        # todo: 漏洞 cookie信息自洽即可，需要再认证一次
        user_token = Certifier.get_user_token(request.cookies.get('uid'), request.cookies.get('gid'),
                                              request.cookies.get('aid'))
        return cookie_token == user_token

    def init_user_properties(self, user):
        for k in request.cookies:
            # uid,gid,aid,token,projects,groups
            if k in ['groups', 'projects']:
                try:
                    cookie_value = request.cookies.get(k)
                    value = json.loads(parse.unquote(cookie_value))
                except Exception as _:
                    log.exception(f'{k} load to json failed, value is: {request.cookies.get(k)}')
                    value = None
            else:
                value = request.cookies.get(k)
            setattr(user, k, value)
