"""
Author: xiaohei
Date: 2022/4/29
Email: <EMAIL>
Host: xiaohei.info
"""

import json

from queqiao.conf.system import SystemConfig
from queqiao.core.engine.base import BaseEngine
from queqiao.util.comm import osutil


class DataXEngine(BaseEngine):

    def name(self):
        return 'DataX'

    def _before_exe(self):
        uid = self._execution.qid
        self.logger.debug(f'get uuid: {uid}')
        datax_home = SystemConfig.read('DATAX_HOME')
        self.config_file = f'{datax_home}/script/datax_{uid}.json'
        self.log_file = f'{datax_home}/log/datax_{uid}.log'
        self.logger.debug(f'datax job config file: {self.config_file}, log file: {self.log_file}')

        speed_parallelism = SystemConfig.read('datax_default_parallelism')
        if hasattr(self._config.source, 'speed_parallelism'):
            speed_parallelism = self._config.source.speed_parallelism
        if hasattr(self._config.sink, 'speed_parallelism'):
            speed_parallelism = self._config.sink.speed_parallelism

        error_percentage = SystemConfig.read('datax_default_error_percentage')
        if hasattr(self._config.source, 'error_percentage'):
            error_percentage = self._config.source.error_percentage
        if hasattr(self._config.sink, 'error_percentage'):
            error_percentage = self._config.sink.error_percentage

        self.datax_config = {
            "job": {
                "setting": {
                    "speed": {
                        "channel": speed_parallelism
                    },
                    "errorLimit": {
                        "record": 0,
                        "percentage": error_percentage
                    }
                },
                "content": [
                    {
                        "reader": None,
                        "writer": None
                    }
                ]
            }
        }
        self.logger.info(f'datax job config: {self.datax_config}')

    def _exe_piped(self):
        self.datax_config['job']['content'][0]['reader'] = self._channel.get_pipeline_param('reader')
        self.datax_config['job']['content'][0]['writer'] = self._channel.get_pipeline_param('writer')
        self.logger.info(f'save datax job config to local file {self.config_file}')
        with open(self.config_file, 'w') as conf:
            conf.write(json.dumps(self.datax_config))
        self._channel.set_engine_param(config_file=self.config_file, log_file=self.log_file)

    def _exe_failed(self):
        super()._exe_failed()
        self.logger.error(f'datax execute failed, please check config file: {self.config_file}')
        with open(f'{self.log_file}', 'r') as log:
            content = log.read()
            self.logger.error(content)

    def _after_exe(self):
        self.logger.info('clear config and log file')
        osutil.rm(self.log_file)
        osutil.rm(self.config_file)


class FtplinkEngine(BaseEngine):

    # todo: 返回给前端时判断如果sink配置在source中已有则隐藏，后端接受时判断如果sink配置缺失则默认到source中补充
    # def _get_dict_config(self):
    #     self.logger.info(f'merge source and sink config')
    #     params_dict = super()._get_dict_config()
    #     params_dict['source'].update(params_dict['source'])
    #     params_dict['sink'].update(params_dict['sink'])
    #     self.logger.info(f'merged config: {params_dict}')
    #     return params_dict

    def _before_exe(self):
        self._source.before_read(self._channel)

    def _after_exe(self):
        self._sink.after_write(self._channel)

    def name(self):
        return 'Ftplink'
