from queqiao.conf.enums import ExecutionStatus
from queqiao.conf.errors import NotFoundException
from queqiao.core.engine.base import BaseEngine
from queqiao.dba.models import Execution
from queqiao.exe import queue
from queqiao.log import LogFactory
from queqiao.util.comm import objutil

log = LogFactory.get_logger()

engine_pool = {}
module_classes = objutil.find_cls_in_pkg(__path__, __package__, excludes=['base'], self_pkg_only=True)
for module in module_classes.keys():
    classes = module_classes[module]
    log.debug(f'module:{module}, classes:{classes}')
    for engine_class in classes:
        engine_obj = engine_class()
        log.debug(f'generate instance for {engine_class}, engine name is: {engine_obj.name()}')
        engine_pool[engine_obj.name()] = engine_class


class EngineProxy:

    @classmethod
    def get_engine(cls, id):
        execution = Execution.get(id=id)
        if not execution:
            raise NotFoundException(f'execution {id} not found')
        engine = execution.engine
        if engine.name not in engine_pool:
            raise NotFoundException(f'engine {engine.name} not in engine pool: {engine_pool.keys()}')
        return engine_pool.get(engine.name)(execution_id=id)

    @staticmethod
    @queue.task(bind=True)
    def submit(self, id, retry_times, retry_interval):
        engine = EngineProxy.get_engine(id)
        # https://www.celerycn.io/yong-hu-zhi-nan/ren-wu-tasks/ren-wu-qing-qiu-task-request
        engine.update_execution_status(ExecutionStatus.QUEUED, qid=self.request.id)
        try:
            engine.run()
        except Exception as why:
            engine.logger.exception(why)
            status = ExecutionStatus.FAILED if retry_times == 0 or self.request.retries == retry_times else ExecutionStatus.RETRY
            engine.logger.info(
                f'retry times: {retry_times}, has been retried: {self.request.retries}, execution status will be set to {status}')
            engine.update_execution_status(status)
            if retry_times > 0:
                self.retry(exc=why, countdown=retry_interval, max_retries=retry_times)
                engine.logger.info(f'current execution will retry in {retry_interval} seconds')
