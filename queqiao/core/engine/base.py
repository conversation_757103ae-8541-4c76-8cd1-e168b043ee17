"""
Author: xiaohei
Date: 2022/7/25
Email: <EMAIL>
Host: xiaohei.info
"""
import abc
import json
import socket
from abc import abstractmethod, ABCMeta

import six

from queqiao.conf import Config
from queqiao.conf.enums import ExecutionStatus
from queqiao.conf.errors import NotFoundException, IllegalParamsException, SuccessFinishException, \
    SuccessAndClearExecutionException, AlarmException, NotAvaliableException
from queqiao.dba.extend_model import Alarm
from queqiao.dba.models import Execution
from queqiao.log import LogFactory
from queqiao.util.comm import osutil, objutil
from queqiao.util.comm.dtutil import timer

log = LogFactory.get_logger()


@six.add_metaclass(abc.ABCMeta)
class BaseEngine:
    def __init__(self, execution_id=None):
        # 初始化匹配需要，获取name
        if not execution_id:
            return
        self.__init_execution(execution_id)
        self.logger.info(f'init engine with executon: {self._execution}')
        self.__init_engine()
        self.logger.info(f'get engine {self.__engine.name}')
        self.__init_config()
        self.logger.debug(f'get config: {self._config}')
        try:
            self.__init_plugins()
            self.logger.info(f'get source plugin: {self._source}, sink plugin: {self._sink}')
        except Exception as why:
            self.logger.exception(why)

    def __init_execution(self, execution_id):
        self._execution = Execution.get(id=execution_id)
        self.logger = LogFactory.get_logger(self._execution.execution_name)
        self.update_execution_status(ExecutionStatus.INIT, start_time=timer.now().datetime)

    def __init_engine(self):
        self.__engine = self._execution.engine

    def __init_config(self):
        # 子节点: source/sink/execution(execution的db信息)/其他
        self._config = Config(self._get_dict_config())

    def __init_plugins(self):
        self._channel = Channel()
        from queqiao.plugin import plugins
        # 每个plugin.py文件只能有一个实现类，文件名必须与插件名一致
        source_plugin = f'queqiao.plugin.{self.__engine.name.lower()}.source.{self._config.source.name}'
        sink_plugin = f'queqiao.plugin.{self.__engine.name.lower()}.sink.{self._config.sink.name}'
        if source_plugin not in plugins or sink_plugin not in plugins:
            raise NotFoundException(
                f'source_plugin: {source_plugin} or sink_plugin: {sink_plugin} not found in plugins: {plugins.keys()} ')
        self._source = plugins.get(source_plugin)[0](self._config.source)
        self._sink = plugins.get(sink_plugin)[0](self._config.sink)

    def _get_dict_config(self):
        try:
            params_dict = json.loads(self._execution.params)
        except Exception as why:
            raise IllegalParamsException(
                f'execution params is not a json string: {self._execution.params}, parse json exception: {str(why)}')
        if 'source' not in params_dict or 'sink' not in params_dict:
            raise NotFoundException(f'source or sink not found in params_dict: {params_dict.keys()}')
        engine_params = json.loads(self.__engine.params if self.__engine.params else '{}')
        params_dict.update(engine_params)
        params_dict['execution'] = self._execution.to_dict(excludes=['params'])
        params_dict['source']['execution'] = params_dict['execution']
        params_dict['source']['current'] = params_dict['current']
        params_dict['sink']['execution'] = params_dict['execution']
        params_dict['sink']['current'] = params_dict['current']
        return params_dict

    def run(self):
        try:
            self.logger.info(f'#####before execute[1/5]#####')
            self.update_execution_status(ExecutionStatus.RUNNING_PRE, worker=socket.gethostname())
            self._before_exe()
            status = self._exe()
            self.logger.info(f'execute finished, status: {status}')
            self.update_execution_status(ExecutionStatus.RUNNING_POST)
            self.logger.info(f'#####after execute[5/5]#####')
            self._after_exe()
        except AlarmException as alarm:
            Alarm.failed(self._execution.id)
            status = ExecutionStatus.FAILED
            self.logger.exception(alarm)
        except SuccessFinishException as suc:
            status = ExecutionStatus.SUCCESS
            if isinstance(suc, SuccessAndClearExecutionException):
                self._execution.delete()

        end_time = timer.now()
        start_time = timer.fromdt(self._execution.start_time)
        elapsed = (end_time - start_time).seconds
        self.logger.info(f'execute elapsed {elapsed} seconds')
        self.update_execution_status(status, end_time=end_time.datetime, elapsed=elapsed)

    def _before_exe(self):
        '''
        正式之前前的前置准备动作，承载一部分引擎的个性初始化操作
        :return:
        '''
        pass

    def _exe(self):
        self.logger.info(f'#####read source[2/5]#####')
        self.update_execution_status(ExecutionStatus.READING)
        self._source.read(self._channel)
        self.logger.info(f'#####write sink[3/5]#####')
        self.update_execution_status(ExecutionStatus.WRITING)
        self._sink.write(self._channel)
        self.logger.info(f'#####do after pipeline[4/5]#####')
        self._exe_piped()
        ret_code = 0

        if self.__engine.cmd:
            cmd = self.__engine.cmd.format(**self._channel.get_engine_params())
            self.logger.info(f'generate engine execute cmd: {cmd}')
            ret_code = osutil.call(cmd)

        if ret_code == 0:
            self._exe_success()
            return ExecutionStatus.SUCCESS
        else:
            self._exe_failed()
            return ExecutionStatus.FAILED

    def _exe_piped(self):
        '''
        source/sink的pipeline流程结束时调用
        :return:
        '''
        pass

    def _exe_success(self):
        self.logger.info('engine execute success')

    def _exe_failed(self):
        self.logger.info('engine execute failed')

    def _after_exe(self):
        pass

    def update_execution_status(self, status, **kwargs):
        old_status = ExecutionStatus(self._execution.status)
        self._execution.status = status.value
        for k in kwargs.keys():
            setattr(self._execution, k, kwargs[k])
        try:
            self._execution.save()
        except Exception as _:
            # self.logger.exception(f'update execution status error')
            self.logger.error(f'update execution status error')
            self.logger.warn(f'reget execution from db with id: {self._execution.id}')
            execution = Execution.get(id=self._execution.id)
            execution.status = status.value
            for k in kwargs.keys():
                setattr(execution, k, kwargs[k])
            self._execution = execution
            self.logger.warn('save execution obj to db and reset to engine')
            self._execution.save()

        self.logger.debug(
            f'execution {self._execution.id} change status from {old_status} to {status} with params: {kwargs}')

    @abstractmethod
    def name(self):
        '''
        引擎名称，与任务配置匹配
        :return:
        '''
        pass


class Plugin(metaclass=ABCMeta):
    def __init__(self, config):
        self.logger = LogFactory.get_logger(config.execution.execution_name)
        self.config = config
        self.__check_config()

    def __check_config(self):
        plugin = self.__module__
        mdl_obj = objutil.new_mdl_instance(plugin)
        if not hasattr(mdl_obj, 'component') or 'configs' not in getattr(mdl_obj, 'component'):
            raise NotAvaliableException(f'plugin {plugin} dose not have a component config, exit!')
        component_config = getattr(mdl_obj, 'component')['configs']
        self.logger.debug(f'get {plugin} component config: {component_config}')
        for key in component_config.keys():
            config = component_config[key]
            if not hasattr(self.config, key):
                setattr(self.config, key, config['default'])
            if config['required'] == 1:
                if getattr(self.config, key) is None:
                    raise IllegalParamsException(
                        f'{key} in {plugin} is required but current config does not contains it or value is None')


class Source(Plugin):
    def __init__(self, config):
        super().__init__(config)
        self.logger.debug(f'init source with config: {config}')

    def before_read(self, channel):
        pass

    @abstractmethod
    def read(self, channel):
        '''
        从数据源读取数据并写入中间态
        :return: 中间态信息
        '''
        pass


class Sink(Plugin):
    def __init__(self, config):
        super().__init__(config)
        self.logger.debug(f'init sink with config: {config}')

    @abstractmethod
    def write(self, channel):
        '''
        从中间态读取数据并写入目标源
        '''
        pass

    def after_write(self, channel):
        pass


class Channel:
    def __init__(self):
        self.__pipeline_params = {}
        self.__engine_params = {}

    def set_pipeline_param(self, **kwargs):
        for k in kwargs.keys():
            self.__pipeline_params[k] = kwargs[k]

    def get_pipeline_keys(self):
        return self.__pipeline_params.keys()

    def get_pipeline_param(self, key):
        return None if key not in self.__pipeline_params else self.__pipeline_params[key]

    def get_pipeline_params(self):
        return self.__pipeline_params

    def set_engine_param(self, **kwargs):
        for k in kwargs.keys():
            self.__engine_params[k] = kwargs[k]

    def get_engine_params(self):
        return self.__engine_params
