"""
Author: xiaohei
Date: 2022/8/18
Email: <EMAIL>
Host: xiaohei.info
"""
from queqiao.conf.enums import ApplyStatus
from queqiao.conf.errors import NotAvaliableException, NoPrivilegeException, NotFoundException
from queqiao.core.execute.task import Task
from queqiao.dba import extend_model
from queqiao.lib.push import Pusher
from queqiao.util.comm import strutil


class Apply(extend_model.Apply):
    @classmethod
    def _get_and_check_apply(cls, id, approver):
        apply = cls.get(id=id)
        if not apply:
            raise NotFoundException(f'apply {id} not found!')
        # 大象快审返回int uid，暂不处理
        if isinstance(approver, str) and approver not in apply.project.admins:
            raise NoPrivilegeException()
        if ApplyStatus(apply.status) != ApplyStatus.APPROVING:
            raise NotAvaliableException(f'申请单 {apply.id} 不处于审批状态，当前申请单状态：{ApplyStatus(apply.status)}')
        return apply

    @classmethod
    def agree(cls, id, approver):
        apply = cls._get_and_check_apply(id, approver)
        apply.status = ApplyStatus.AGREE.value
        apply.save()

        inf = strutil.hocon_loads(apply.information)
        if 'exec_immediately_tasks' in inf:
            Task.execute_batch(inf['exec_immediately_tasks'])

        pusher = Pusher.get_pusher()
        # todo: 确认前端页面地址
        msg = f'您的申请单 {apply.id} 已审批通过，审批人 {approver}，点击查看任务列表： '
        pusher.push(msg, apply.create_user)

    @classmethod
    def reject(cls, id, approver, comment=None):
        apply = cls._get_and_check_apply(id, approver)
        apply.status = ApplyStatus.REJECT.value
        apply.save()
        pusher = Pusher.get_pusher()
        comment = f'（{comment}）' if comment else ''
        msg = f'您的申请单 {apply.id} 已被驳回{comment}，审批人 {approver}，请按照要求重新申请或与审批人沟通！'
        pusher.push(msg, apply.create_user)
