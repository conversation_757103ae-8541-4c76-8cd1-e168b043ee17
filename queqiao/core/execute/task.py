"""
Author: xiaohei
Date: 2022/6/7
Email: <EMAIL>
Host: xiaohei.info
"""
import json
import traceback

from queqiao.conf.enums import ExecutionStatus, TaskStatus
from queqiao.conf.errors import NotFoundException, IllegalParamsException, NotAvaliableException
from queqiao.core.execute.execution import Execution
from queqiao.dba import models, extend_model
from queqiao.dba.models import Project
from queqiao.log import LogFactory
from queqiao.util.comm import strutil
from queqiao.util.comm.dtutil import timer

log = LogFactory.get_logger()


class Task(extend_model.Task):
    @classmethod
    def execute_batch(cls, ids):
        if not isinstance(ids, str) and not isinstance(ids, list):
            raise IllegalParamsException(f'id list must be str or list!')
        ids = ids if isinstance(ids, list) else ids.split(',')
        res = []
        for i in ids:
            res.append(cls.execute(i))
        return res

    @classmethod
    def execute(cls, id, params=None, delay=True):
        if not isinstance(id, int) and not isinstance(id, str):
            raise IllegalParamsException(f'id must be int or str(for name)! current is {type(id)}')
        if isinstance(id, int):
            task_key = {'id': id}
        else:
            name_arr = id.split('.')
            if len(name_arr) != 2:
                raise IllegalParamsException(f'name must be $project.$name, current is {id}')
            project = Project.get_one(name=name_arr[0])
            if not project:
                raise NotFoundException(f'can not found project by name: {name_arr[0]}')
            task_key = {'name': name_arr[1], 'project_id': project.id}

        task = cls.get_one(**task_key)
        if not task:
            raise NotFoundException(f'task {id} not found')
        if not task.runable:
            raise NotAvaliableException(f'task status({TaskStatus(task.status)}) is not runable or project is offline')
        log.info(f'submit task {task} with params: {params}')
        source_configs = {}
        sink_configs = {}
        now = timer.now()
        if params and 'current' in params:
            try:
                now = timer.fromdt(params['current'])
                log.info(f'reload now from current: {params["current"]}')
            except Exception as _:
                raise IllegalParamsException(f'key [current] value [{params["current"]}] '
                                             f'can not format to datetime! error: {traceback.format_exc()}')
        log.info(f'init etl base time: {now}')
        for config in task.configs:
            # todo: 前端操作保存参数时提前校验$变量是否缺失
            valued = strutil.render_vars(config.value,
                                         now=now) if config.value and '$' in config.value else config.value
            if config.cid == task.task_type.source_id:
                source_configs[config.key] = valued
            if config.cid == task.task_type.sink_id:
                sink_configs[config.key] = valued
        source_configs['name'] = task.task_type.source_component.datasource
        sink_configs['name'] = task.task_type.sink_component.datasource
        log.debug(f'init source config: {source_configs}, sink config: {sink_configs}')
        source_dsn = models.Dsn.get(id=int(source_configs['dsn']))
        source_configs['dsn'] = source_dsn.to_dict()
        sink_dsn = models.Dsn.get(id=int(sink_configs['dsn']))
        sink_configs['dsn'] = sink_dsn.to_dict()

        log.debug(f'get source dsn: {source_dsn}, sink dsn: {sink_dsn}')
        source_configs['org'] = source_dsn.org.to_dict()
        sink_configs['org'] = sink_dsn.org.to_dict()
        execution_params = {'source': source_configs, 'sink': sink_configs, 'current': now.date}
        execution = Execution.new(create_user=task.update_user,
                                  update_user=task.update_user,
                                  status=ExecutionStatus.INIT.value,
                                  qid=strutil.uid(),
                                  task_id=task.id,
                                  project_id=task.project_id,
                                  source_org_id=source_dsn.org_id,
                                  sink_org_id=sink_dsn.org_id,
                                  engine_id=task.engine_id)
        if source_dsn.org.params:
            execution_params.update(json.loads(source_dsn.org.params))
        if sink_dsn.org.params:
            execution_params.update(json.loads(sink_dsn.org.params))
        if task.params:
            execution_params.update(json.loads(task.params))
        if params:
            execution_params.update(params)
        execution.params = json.dumps(execution_params)
        execution.fid = strutil.md5(execution.params)
        execution.save()
        log.debug(f'saved execution :{json.dumps(execution.to_dict())}')
        Execution.execute(execution.id, delay, execution_params)
        return execution.id


