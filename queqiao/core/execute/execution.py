"""
Author: xiaohei
Date: 2022/8/18
Email: <EMAIL>
Host: xiaohei.info
"""
from queqiao.conf.enums import TaskPriority
from queqiao.conf.env import EnvConfig
from queqiao.dba import models
from queqiao.log import LogFactory

log = LogFactory.get_logger()


class Execution(models.Execution):
    @classmethod
    def execute(cls, id, delay=True, params=None):
        if params is None:
            params = {}
        from queqiao.core.engine import EngineProxy
        if delay:
            retry_times = params.get('retry_times', EnvConfig.get('CELERY_RETRY_DEFAULT_TIMES', 0))
            retry_interval = params.get('retry_interval', EnvConfig.get('CELERY_RETRY_DEFAULT_INTERVAL', 5 * 60))
            queue = params.get('queue', 'normal')
            priority = params.get('priority', TaskPriority.DEFAULT.value)
            log.info(f'submit execution {id} with retry_times: {retry_times}, retry_interval: {retry_interval}, '
                     f'queue: {queue}, priority: {priority}')
            EngineProxy.submit.apply_async(
                kwargs={'id': id, 'retry_times': retry_times, 'retry_interval': retry_interval},
                queue=queue,
                priority=priority,
                retry=True,
                retry_policy={
                    'max_retries': retry_times,
                    'interval_start': retry_interval,
                    'interval_step': 10,
                    'interval_max': 10 * 60,
                })
        else:
            engine = EngineProxy.get_engine(id)
            log.info(
                f'get engine: {engine}, start execute see execution log for more information')
            engine.run()
