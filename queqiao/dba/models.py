# coding: utf-8
import json

from sqlalchemy import Column, DateTime, Index, String, Text, text
from sqlalchemy.dialects.mysql import BIGINT, INTEGER, TINYTEXT, DOUBLE
from sqlalchemy.orm import relationship

from instance.default import API_HOST, API_PORT, API_VERSION
from queqiao.conf.enums import PermissionStatus, TransType, ProjectRoleType, TaskStatus
from queqiao.conf.errors import NoPrivilegeException
from queqiao.dba.base_model import BaseModel
# 对象关系定义参考：https://segmentfault.com/a/1190000018006031
from queqiao.util.comm import strutil

pkgname = 'queqiao.dba.models'


class Alarm(BaseModel):
    __tablename__ = 'alarm'

    id = Column(INTEGER(11), primary_key=True, unique=True)
    receivers = Column(String(512, 'utf8_bin'), nullable=False, comment='告警接收人列表')
    content = Column(Text(collation='utf8_bin'), comment='告警内容')
    keep_quiet = Column(DOUBLE, nullable=False, server_default=text("'-1'"), comment='告警静默时间（分钟）')
    update_user = Column(String(100, 'utf8_bin'), comment='更新人')
    # execution_id = Column(INTEGER(11), ForeignKey('execution.id'), nullable=False, index=True, comment='执行id')
    execution_id = Column(INTEGER(11), nullable=False, index=True, comment='执行id')


class TaskPermission(BaseModel):
    __tablename__ = 'task_permission'
    __table_args__ = (
        Index('ix_uniq_task_user', 'task_id', 'user_id', unique=True),
    )

    id = Column(BIGINT(20), primary_key=True, unique=True)
    # task_id = Column(INTEGER(11), ForeignKey('task.id'), nullable=False, index=True, comment='任务id')
    task_id = Column(INTEGER(11), nullable=False, index=True, comment='任务id')
    user_id = Column(String(100, 'utf8_bin'), nullable=False, index=True, comment='用户id')
    permission = Column(INTEGER(11), nullable=False, server_default=text("'4'"), comment='权限421模型，每个任务一条记录')
    status = Column(INTEGER(11), nullable=False, server_default=text("'1'"), comment='权限状态：1启用、0待审批、-1拒绝')


class Task(BaseModel):
    __tablename__ = 'task'
    __table_args__ = (
        Index('ix_uniq_pid_name', 'project_id', 'name', unique=True),
    )

    id = Column(INTEGER(11), primary_key=True, comment='自增id')
    create_user = Column(String(100), nullable=False, comment='创建人')
    update_user = Column(String(100), nullable=False, comment='修改人')
    is_delete = Column(INTEGER(11), nullable=False, server_default=text("'0'"), comment='删除标识位')
    name = Column(String(256), nullable=False, index=True,
                  comment='任务名，提供api请求的索引，格式为project_name.task_name')
    trans_type = Column(INTEGER(11), nullable=False, server_default=text("'0'"), comment='传输类型，0单次1调度')
    alarm_receivers = Column(String(256), comment='异常告警人')
    retry_times = Column(INTEGER(11), nullable=True, server_default=text("'0'"), comment='失败重试次数')
    retry_interval = Column(INTEGER(11), nullable=True, server_default=text("'300'"), comment='失败重试间隔（默认5分钟）')
    queue = Column(String(64), nullable=True, server_default=text("'normal'"), comment='任务执行队列，默认normal')
    priority = Column(INTEGER(11), nullable=True, server_default=text("'9'"), comment='任务执行优先级，0最大/默认为9')
    params = Column(Text, comment='任务执行参数、SQL变量、ITSM单号、FTPLINK老链路参数等')
    status = Column(INTEGER(11), nullable=False, index=True, comment='任务状态')
    apply_id = Column(INTEGER(11), nullable=False, index=True, comment='申请单号')
    task_type_id = Column(INTEGER(11), nullable=False, index=True, comment='任务类型id')
    engine_id = Column(INTEGER(11), nullable=False, index=True, comment='任务执行引擎id')
    project_id = Column(INTEGER(11), nullable=False, index=True, comment='项目组id')
    executions = relationship(f'{pkgname}.Execution',
                              primaryjoin=f'{pkgname}.Task.id == {pkgname}.Execution.task_id',
                              foreign_keys=f'{pkgname}.Execution.task_id',
                              backref='task')
    history_files = relationship(f'{pkgname}.FtplinkHistoryFile',
                                 primaryjoin=f'{pkgname}.Task.id == {pkgname}.FtplinkHistoryFile.task_id',
                                 foreign_keys=f'{pkgname}.FtplinkHistoryFile.task_id',
                                 backref='task')
    configs = relationship(f'{pkgname}.TaskConfig',
                           primaryjoin=f'{pkgname}.Task.id == {pkgname}.TaskConfig.task_id',
                           foreign_keys=f'{pkgname}.TaskConfig.task_id',
                           backref='task')
    permissions = relationship(f'{pkgname}.TaskPermission',
                               primaryjoin=f'{pkgname}.Task.id == {pkgname}.TaskPermission.task_id',
                               foreign_keys=f'{pkgname}.TaskPermission.task_id',
                               backref='task')

    def to_dict(self,
                includes=None,
                excludes=None,
                component_configs=False,
                task_permissions=False):
        result = super(Task, self).to_dict(includes, excludes)
        source = self.task_type.source_component
        sink = self.task_type.sink_component
        task_configs = self.task_configs if not component_configs else self.task_component_configs

        result[
            'where2where'] = f'{source.datasource}({self.source_org.name}) -> {sink.datasource}({self.sink_org.name})'
        result['engine'] = self.engine.name
        result['source'] = {'cid': source.id, 'configs': task_configs['source']}
        result['sink'] = {'cid': sink.id, 'configs': task_configs['sink']}
        result['task_name'] = self.name
        params = json.loads(self.params) if self.params else {}
        result['trans_type'] = TransType(self.trans_type).name
        result['result_size'] = params.get('result_size', 0)
        result['task_type'] = self.task_type.name
        result['params'] = strutil.hocon_dumps(json.loads(result['params'])) if result['params'] else ''
        if task_permissions:
            result['permissions'] = [permission.to_dict() for permission in self.permissions]
        return result

    def check_permission(self, user, permission):
        project = Project.get(id=user.project_id)
        permission_users = self.permission_users
        if permission not in permission_users:
            raise NoPrivilegeException(f'{permission} not in current permission list {permission_users.keys()}')
        if not user.is_admin and user.uid not in project.admins and user.uid not in permission_users[permission]:
            return False
        return True

    @classmethod
    def get_tasks_by_user(cls, user):
        if user.is_admin:
            tasks = Task.get()
        else:
            project = Project.get(id=user.project_id)
            tasks = Task.get(project_id=user.project_id)
            if user.uid not in project.admins:
                tasks = [task for task in tasks if user.uid in task.readable_users]
        return tasks

    @classmethod
    def get_auto_import_tasks(cls):
        import_types = TaskType.get_import_types()
        import_tasks = []
        for t in import_types:
            for task in t.tasks:
                auto_import = TaskConfig.get_one(task_id=task.id, key='auto_import')
                if auto_import and auto_import.value == 1:
                    import_tasks.append(task)
        return import_tasks

    @property
    def task_configs(self):
        source_configs = {}
        sink_configs = {}
        for config in self.configs:
            append_configs = source_configs if config.cid == self.task_type.source_id else sink_configs
            append_configs[config.key] = config.value
        return {'source': source_configs, 'sink': sink_configs}

    @property
    def task_component_configs(self):
        source_configs = []
        sink_configs = []
        for config in self.configs:
            if config.key == 'dsn':
                continue
            append_configs = source_configs if config.cid == self.task_type.source_id else sink_configs
            component_config = ComponentConfig.get(cid=config.cid, name=config.key, only_one=1).to_dict()
            component_config['value'] = config.value
            append_configs.append(component_config)
        return {'source': source_configs, 'sink': sink_configs}

    @property
    def source_org(self):
        dsn = Dsn.get(id=self.task_configs['source']['dsn'])
        return dsn.org

    @property
    def sink_org(self):
        dsn = Dsn.get(id=self.task_configs['sink']['dsn'])
        return dsn.org

    @property
    def source_org_id(self):
        return self.source_org.id

    @property
    def sink_org_id(self):
        return self.sink_org.id

    @property
    def permission_users(self):
        readable_users = []
        writable_users = []
        executable_users = []
        from queqiao.dba import extend_model
        for permission in self.permissions:
            if PermissionStatus(permission.status) == PermissionStatus.ENTABLE:
                decode = extend_model.TaskPermission.decode_permission(permission.permission)
                if decode['read']:
                    readable_users.append(permission.user_id)
                if decode['write']:
                    writable_users.append(permission.user_id)
                if decode['execute']:
                    executable_users.append(permission.user_id)
        return {'read': readable_users, 'write': writable_users, 'execute': executable_users}

    @property
    def readable_users(self):
        return self.permission_users['read']

    @property
    def writable_users(self):
        return self.permission_users['write']

    @property
    def executable_users(self):
        return self.permission_users['execute']

    @property
    def submit_params(self):
        return {'retry_times': self.retry_times, 'retry_interval': self.retry_interval, 'queue': self.queue,
                'priority': self.priority}

    @property
    def runable(self):
        if self.status <= TaskStatus.APPROVING.value or self.project.is_delete == 1:
            return False
        return True

    @property
    def etl_name(self):
        return f'{self.project.name}.{self.name}'


class Component(BaseModel):
    __tablename__ = 'component'
    __table_args__ = (
        Index('ix_uniq_ope_ds', 'operator', 'datasource', unique=True),
    )

    id = Column(INTEGER(11), primary_key=True, comment='自增id')
    operator = Column(String(10), nullable=False, index=True, comment='组件算子，source/sink')
    datasource = Column(String(50, 'utf8_bin'), nullable=False, index=True, comment='算子操作的数据源')
    create_user = Column(String(50, 'utf8_bin'), nullable=False)
    update_user = Column(String(50, 'utf8_bin'))
    comment = Column(String(256, 'utf8_bin'), comment='备注')
    is_delete = Column(INTEGER(11), nullable=False, server_default=text("'0'"))
    configs = relationship(f'{pkgname}.ComponentConfig',
                           primaryjoin=f'{pkgname}.Component.id == {pkgname}.ComponentConfig.cid',
                           foreign_keys=f'{pkgname}.ComponentConfig.cid',
                           backref='component')
    task_configs = relationship(f'{pkgname}.TaskConfig',
                                primaryjoin=f'{pkgname}.Component.id == {pkgname}.TaskConfig.cid',
                                foreign_keys=f'{pkgname}.TaskConfig.cid',
                                backref='component')
    source_types = relationship(f'{pkgname}.TaskType',
                                primaryjoin=f'{pkgname}.Component.id == {pkgname}.TaskType.source_id',
                                backref='source_component',
                                foreign_keys=f'{pkgname}.TaskType.source_id')
    sink_types = relationship(f'{pkgname}.TaskType', backref='sink_component',
                              primaryjoin=f'{pkgname}.Component.id == {pkgname}.TaskType.sink_id',
                              foreign_keys=f'{pkgname}.TaskType.sink_id')


class TaskType(BaseModel):
    __tablename__ = 'task_type'
    __table_args__ = (
        Index('ix_uniq_reader_writer', 'source_id', 'sink_id', unique=True),
    )

    id = Column(INTEGER(11), primary_key=True, unique=True)
    code = Column(INTEGER(11), nullable=False, index=True, unique=True,
                  comment='任务类型编码,100-199外部文件交互,200-299cdc同步,300-399异构数据同步')
    name = Column(String(50, 'utf8_bin'), nullable=False, comment='任务类型名')
    # source_id = Column(INTEGER(11), ForeignKey('component.id'), index=True, nullable=False, comment='source组件id')
    source_id = Column(INTEGER(11), index=True, nullable=False, comment='source组件id')
    # sink_id = Column(INTEGER(11), ForeignKey('component.id'), index=True, nullable=False, comment='sink组件id')
    sink_id = Column(INTEGER(11), index=True, nullable=False, comment='sink组件id')
    engines = Column(String(128, 'utf8_bin'), nullable=False, comment='任务类型支持的引擎集合')
    create_user = Column(String(50, 'utf8_bin'), nullable=False)
    update_user = Column(String(50, 'utf8_bin'))
    comment = Column(String(256, 'utf8_bin'), comment='备注')
    is_delete = Column(INTEGER(11), nullable=False, server_default=text("'0'"))
    tasks = relationship(f'{pkgname}.Task',
                         primaryjoin=f'{pkgname}.TaskType.id == {pkgname}.Task.task_type_id',
                         foreign_keys=f'{pkgname}.Task.task_type_id',
                         backref='task_type')

    @classmethod
    def get_import_types(cls):
        types = cls.get_with(cls.code >= 111100, cls.code < 111200)
        return types


class Apply(BaseModel):
    __tablename__ = 'apply'

    id = Column(INTEGER(11), primary_key=True, comment='自增id')
    create_user = Column(String(100), nullable=False, comment='创建人')
    update_user = Column(String(100), nullable=False, comment='修改人')
    scenario = Column(String(1000), nullable=False, comment='业务场景')
    describe = Column(String(1000), nullable=False, comment='数据内容描述')
    security_level = Column(INTEGER(11), nullable=False, server_default=text("'3'"), comment='数据密级')
    information = Column(Text, comment='保存的其他申请参数，如大象快审单号、ITSM单号等')
    status = Column(INTEGER(11), nullable=False, comment='申请状态：0（正常）、<=机构负责人（审批中）、-1（拒绝）')
    # project_id = Column(INTEGER(11), ForeignKey('project.id'), nullable=False, index=True, comment='项目id')
    project_id = Column(INTEGER(11), nullable=False, index=True, comment='项目id')
    tasks = relationship(f'{pkgname}.Task',
                         primaryjoin=f'{pkgname}.Apply.id == {pkgname}.Task.apply_id',
                         foreign_keys=f'{pkgname}.Task.apply_id',
                         backref='apply')

    @property
    def uri(self):
        # todo: 确认前端地址
        return f'http://{API_HOST}:{API_PORT}/api/{API_VERSION}/apply/{self.id}'

    def rollback(self):
        for task in self.tasks:
            TaskPermission.delete_by(task_id=task.id)
            TaskConfig.delete_by(task_id=task.id)
        Task.delete_by(apply_id=self.id)

    def to_dict(
            self,
            includes=None,
            excludes=None,
            with_tasks=True
    ):
        result = super().to_dict(includes, excludes)
        if with_tasks:
            task_results = [task.to_dict() for task in self.tasks]
            result['tasks'] = task_results
        result['security_level'] = f'C{self.security_level}'
        result['project'] = self.project.name
        result['apply_uri'] = self.uri
        result['information'] = strutil.hocon_dumps(json.loads(result['information'])) if result['information'] else ''
        return result


class ComponentConfig(BaseModel):
    __tablename__ = 'component_config'
    __table_args__ = (
        Index('ix_uniq_cid_name', 'cid', 'name', unique=True),
    )

    id = Column(BIGINT(20), primary_key=True, unique=True)
    name = Column(String(128, 'utf8_bin'), nullable=False, index=True, comment='配置名')
    name_cn = Column(String(256, 'utf8_bin'), nullable=False, comment='中文名')
    type = Column(String(32, 'utf8_bin'), comment='配置类型')
    default = Column(String(256, 'utf8_bin'), comment='配置默认值')
    required = Column(INTEGER(11), nullable=False, comment='是否必填')
    demo = Column(String(256, 'utf8_bin'), comment='示例值')
    comment = Column(String(256, 'utf8_bin'), comment='配置备注说明')
    # cid = Column(INTEGER(11), ForeignKey('component.id'), nullable=False, index=True, comment='组件id')
    cid = Column(INTEGER(11), nullable=False, index=True, comment='组件id')
    is_delete = Column(INTEGER(11), nullable=False, server_default=text("'0'"))
    create_user = Column(String(50, 'utf8_bin'))
    update_user = Column(String(50, 'utf8_bin'))


class Engine(BaseModel):
    __tablename__ = 'engine'

    id = Column(INTEGER(11), primary_key=True)
    create_user = Column(String(100, 'utf8_bin'), nullable=False, comment='创建人')
    update_user = Column(String(100, 'utf8_bin'), nullable=False, comment='修改人')
    name = Column(String(32, 'utf8_bin'), nullable=False, comment='引擎名')
    comment = Column(String(256, 'utf8_bin'), comment='引擎介绍')
    enable = Column(INTEGER(11), nullable=False, server_default=text("'1'"), comment='是否启用')
    cmd = Column(TINYTEXT, nullable=True, comment='引擎执行命令')
    params = Column(Text(collation='utf8_bin'), nullable=True, comment='引擎执行参数，json格式')
    is_delete = Column(INTEGER(11), nullable=False, server_default=text("'0'"))
    tasks = relationship(f'{pkgname}.Task',
                         primaryjoin=f'{pkgname}.Engine.id == {pkgname}.Task.engine_id',
                         foreign_keys=f'{pkgname}.Task.engine_id',
                         backref='engine')
    executions = relationship(f'{pkgname}.Execution',
                              primaryjoin=f'{pkgname}.Engine.id == {pkgname}.Execution.engine_id',
                              foreign_keys=f'{pkgname}.Execution.engine_id',
                              backref='engine')


class Execution(BaseModel):
    __tablename__ = 'execution'

    id = Column(INTEGER(11), primary_key=True, comment='自增id')
    create_user = Column(String(100), nullable=False, comment='创建人')
    update_user = Column(String(100), nullable=False, comment='修改人')
    status = Column(INTEGER(11), nullable=False, index=True, comment='任务状态')
    qid = Column(String(100), nullable=False, index=True, comment='任务标识id')
    fid = Column(String(256), nullable=True, index=True, comment='系列执行实例的共同标识符')
    message = Column(Text, comment='任务执行信息')
    start_time = Column(DateTime, nullable=True, comment='执行开始时间')
    end_time = Column(DateTime, nullable=True, comment='执行结束时间')
    elapsed = Column(BIGINT(20), nullable=False, server_default=text("'0'"), comment='执行耗时')
    failed_retries = Column(INTEGER(11), nullable=False, server_default=text("'0'"), comment='失败重试次数')
    worker = Column(String(64), nullable=True, comment='任务执行节点')
    params = Column(Text, comment='执行参数')
    # task_id = Column(INTEGER(11), ForeignKey('task.id'), nullable=False, index=True, comment='任务id')
    task_id = Column(INTEGER(11), nullable=False, index=True, comment='任务id')
    # project_id = Column(INTEGER(11), ForeignKey('project.id'), nullable=False, index=True, comment='项目id')
    project_id = Column(INTEGER(11), nullable=False, index=True, comment='项目id')
    # source_org_id = Column(INTEGER(11), ForeignKey('org.id'), nullable=True, index=True, comment='操作源机构id（统计用）')
    source_org_id = Column(INTEGER(11), nullable=True, index=True, comment='操作源机构id（统计用）')
    # sink_org_id = Column(INTEGER(11), ForeignKey('org.id'), nullable=True, index=True, comment='目标源机构id（统计用）')
    sink_org_id = Column(INTEGER(11), nullable=True, index=True, comment='目标源机构id（统计用）')
    # engine_id = Column(INTEGER(11), ForeignKey('engine.id'), nullable=False, index=True, comment='执行引擎id')
    engine_id = Column(INTEGER(11), nullable=False, index=True, comment='执行引擎id')
    # alarms = relationship(f'{pkgname}.Alarm', backref='execution')
    alarms = relationship(f'{pkgname}.Alarm',
                          primaryjoin=f'{pkgname}.Execution.id == {pkgname}.Alarm.execution_id',
                          foreign_keys=f'{pkgname}.Alarm.execution_id',
                          backref='execution')
    history_files = relationship(f'{pkgname}.FtplinkHistoryFile',
                                 primaryjoin=f'{pkgname}.Execution.id == {pkgname}.FtplinkHistoryFile.execution_id',
                                 uselist=False,
                                 foreign_keys=f'{pkgname}.FtplinkHistoryFile.execution_id',
                                 backref='execution')

    def to_dict(self,
                includes=None,
                excludes=None):
        result = super().to_dict(includes, excludes)
        result['task_name'] = self.task.etl_name
        result['alarm_receivers'] = self.task.alarm_receivers
        result['execution_name'] = self.execution_name
        return result

    @property
    def params_dict(self):
        ret = {}
        try:
            ret = json.loads(self.params)
        except Exception as why:
            print(f'load execution params error: {why}, params: {self.params}')
        return ret

    @property
    def current(self):
        return self.params_dict.get('current', None)

    @property
    def execution_name(self):
        return f'{self.task.etl_name}:{self.current}:{self.id}'


class Dsn(BaseModel):
    __tablename__ = 'dsn'

    id = Column(INTEGER(11), primary_key=True)
    name = Column(String(50), nullable=False, index=True, unique=True, comment='连接名')
    connect = Column(Text, nullable=True, comment='连接参数json格式')
    dsn_type = Column(String(50), nullable=False, comment='操作数据源的类型，与source_type保持一致')
    is_delete = Column(INTEGER(11), nullable=False, server_default=text("'0'"))
    create_user = Column(String(50), nullable=False)
    update_user = Column(String(50))
    comment = Column(String(256), comment='备注')
    # org_id = Column(INTEGER(11), ForeignKey('org.id'), nullable=False, index=True, comment='所属机构')
    org_id = Column(INTEGER(11), nullable=False, index=True, comment='所属机构')


class Org(BaseModel):
    __tablename__ = 'org'

    id = Column(INTEGER(11), primary_key=True, comment='自增id')
    create_user = Column(String(100), nullable=False, comment='创建人')
    update_user = Column(String(100), nullable=False, comment='修改人')
    is_delete = Column(INTEGER(11), nullable=False, server_default=text("'0'"), comment='删除标识位')
    name = Column(String(50), nullable=False, index=True, unique=True, comment='外部机构名')
    params = Column(Text, comment='机构参数')
    ad_users = Column(String(256), comment='机构负责人')
    comment = Column(String(256), comment='备注')
    source_executions = relationship(f'{pkgname}.Execution', backref='source_org',
                                     primaryjoin=f'{pkgname}.Org.id == {pkgname}.Execution.source_org_id',
                                     foreign_keys=f'{pkgname}.Execution.source_org_id')
    sink_executions = relationship(f'{pkgname}.Execution', backref='sink_org',
                                   primaryjoin=f'{pkgname}.Org.id == {pkgname}.Execution.sink_org_id',
                                   foreign_keys=f'{pkgname}.Execution.sink_org_id')
    source_history_files = relationship(f'{pkgname}.FtplinkHistoryFile', backref='source_org',
                                        primaryjoin=f'{pkgname}.Org.id == {pkgname}.FtplinkHistoryFile.source_org_id',
                                        foreign_keys='FtplinkHistoryFile.source_org_id')
    sink_history_files = relationship(f'{pkgname}.FtplinkHistoryFile', backref='sink_org',
                                      primaryjoin=f'{pkgname}.Org.id == {pkgname}.FtplinkHistoryFile.sink_org_id',
                                      foreign_keys='FtplinkHistoryFile.sink_org_id')
    dsns = relationship(f'{pkgname}.Dsn',
                        primaryjoin=f'{pkgname}.Org.id == {pkgname}.Dsn.org_id',
                        foreign_keys=f'{pkgname}.Dsn.org_id',
                        backref='org')

    @property
    def pyname(self):
        return strutil.hanzi2pinyin(self.name)


class FtplinkHistoryFile(BaseModel):
    __tablename__ = 'ftplink_history_file'

    id = Column(INTEGER(11), primary_key=True, comment='自增id')
    create_user = Column(String(100), nullable=False, comment='创建人')
    source = Column(String(32), nullable=False, comment='源组件名')
    sink = Column(String(32), nullable=False, comment='目标组件名')
    remote_file = Column(String(256), nullable=True, comment='远程文件路径')
    local_file = Column(String(256), comment='本地文件路径')
    bytes = Column(BIGINT(20), nullable=False, server_default=text("'0'"), comment='文件字节数')
    md5 = Column(String(32), nullable=False, comment='文件MD5值')
    row_num = Column(INTEGER(11), nullable=False, server_default=text("'0'"), comment='文件行数')
    col_num = Column(INTEGER(11), nullable=False, server_default=text("'0'"), comment='文件列数')
    col_list = Column(Text, comment='列信息')
    ctime = Column(String(100), nullable=True, comment='远程文件修改时间')
    check_meta = Column(Text, comment='其他校验的元数据信息')
    file_type = Column(String(32), nullable=False, comment='文件类型')
    # execution_id = Column(INTEGER(11), ForeignKey('execution.id'), nullable=False, index=True, comment='执行id')
    execution_id = Column(INTEGER(11), nullable=False, index=True, comment='执行id')
    # task_id = Column(INTEGER(11), ForeignKey('task.id'), nullable=False, index=True, comment='任务id')
    task_id = Column(INTEGER(11), nullable=False, index=True, comment='任务id')
    # project_id = Column(INTEGER(11), ForeignKey('project.id'), nullable=False, index=True, comment='项目组id')
    project_id = Column(INTEGER(11), nullable=False, index=True, comment='项目组id')
    # source_org_id = Column(INTEGER(11), ForeignKey('org.id'), nullable=True, index=True, comment='操作源机构id（统计用）')
    source_org_id = Column(INTEGER(11), nullable=True, index=True, comment='操作源机构id（统计用）')
    # sink_org_id = Column(INTEGER(11), ForeignKey('org.id'), nullable=True, index=True, comment='目标源机构id（统计用）')
    sink_org_id = Column(INTEGER(11), nullable=True, index=True, comment='目标源机构id（统计用）')


class Project(BaseModel):
    __tablename__ = 'project'

    id = Column(INTEGER(11), primary_key=True)
    create_user = Column(String(100, 'utf8_bin'), nullable=False, comment='创建人')
    update_user = Column(String(100, 'utf8_bin'), nullable=False, comment='修改人')
    is_delete = Column(INTEGER(11), nullable=False, server_default=text("'0'"))
    name = Column(String(50, 'utf8_bin'), nullable=False, index=True, unique=True, comment='项目组名称')
    desc = Column(String(256, 'utf8_bin'), nullable=True, comment='项目描述')
    admins = Column(String(256, 'utf8_bin'), nullable=True, comment='项目组管理员，最多5人')
    executions = relationship(f'{pkgname}.Execution',
                              primaryjoin=f'{pkgname}.Project.id == {pkgname}.Execution.project_id',
                              foreign_keys=f'{pkgname}.Execution.project_id',
                              backref='project')
    history_files = relationship(f'{pkgname}.FtplinkHistoryFile',
                                 primaryjoin=f'{pkgname}.Project.id == {pkgname}.FtplinkHistoryFile.project_id',
                                 foreign_keys=f'{pkgname}.FtplinkHistoryFile.project_id',
                                 backref='project')
    users = relationship(f'{pkgname}.ProjectUserRelation',
                         primaryjoin=f'{pkgname}.Project.id == {pkgname}.ProjectUserRelation.project_id',
                         foreign_keys=f'{pkgname}.ProjectUserRelation.project_id',
                         backref='project')
    tasks = relationship(f'{pkgname}.Task',
                         primaryjoin=f'{pkgname}.Project.id == {pkgname}.Task.project_id',
                         foreign_keys=f'{pkgname}.Task.project_id',
                         backref='project')
    applys = relationship(f'{pkgname}.Apply',
                          primaryjoin=f'{pkgname}.Project.id == {pkgname}.Apply.project_id',
                          foreign_keys=f'{pkgname}.Apply.project_id',
                          backref='project')

    @property
    def admin_list(self):
        admin = self.admins.split(',') if self.admins is not None else []
        admin_relations = ProjectUserRelation.get(project_id=self.id, role_type=ProjectRoleType.ADMIN.value)
        admin.extend([r.user_id for r in admin_relations])
        return list(set(admin))

    def is_admin(self, uid):
        return uid in self.admin_list


class ProjectUserRelation(BaseModel):
    __tablename__ = 'project_user_relation'
    __table_args__ = (
        Index('idx_ids', 'project_id', 'user_id', unique=True),
    )

    id = Column(INTEGER(11), primary_key=True)
    # project_id = Column(INTEGER(11), ForeignKey('project.id'), index=True, nullable=False, comment='项目组id')
    project_id = Column(INTEGER(11), index=True, nullable=False, comment='项目组id')
    user_id = Column(String(50, 'utf8_bin'), nullable=False, index=True, comment='用户id（识别码）')
    role_type = Column(INTEGER(11), nullable=False, comment='用户类型（0普通用户、1管理员）')
    is_default = Column(INTEGER(11), nullable=False, server_default=text("'0'"), comment='是否默认项目组')


class SystemConfig(BaseModel):
    __tablename__ = 'system_config'

    id = Column(INTEGER(11), primary_key=True)
    create_user = Column(String(50), nullable=False)
    update_user = Column(String(50))
    key = Column(String(50), nullable=False, index=True, unique=True)
    value = Column(Text, nullable=True)
    name = Column(String(50), nullable=False, comment='配置名')
    comment = Column(String(256), comment='配置备注')
    is_delete = Column(INTEGER(11), nullable=False, server_default=text("'0'"))


class TaskConfig(BaseModel):
    __tablename__ = 'task_config'

    id = Column(BIGINT(20), primary_key=True, unique=True)
    key = Column(String(128, 'utf8_bin'), nullable=False, comment='配置项名称')
    value = Column(Text, nullable=True, comment='配置项值')
    # task_id = Column(INTEGER(11), ForeignKey('task.id'), nullable=False, index=True, comment='任务id')
    task_id = Column(INTEGER(11), nullable=False, index=True, comment='任务id')
    # cid = Column(INTEGER(11), ForeignKey('component.id'), nullable=False, index=True, comment='组件id')
    cid = Column(INTEGER(11), nullable=False, index=True, comment='组件id')
    create_user = Column(String(50, 'utf8_bin'), nullable=False)
    update_user = Column(String(50, 'utf8_bin'))
