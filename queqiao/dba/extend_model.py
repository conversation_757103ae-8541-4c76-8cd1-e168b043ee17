"""
Author: xiaohei
Date: 2022/6/10
Email: <EMAIL>
Host: xiaohei.info
"""
import json

from instance.default import API_VERSION
from queqiao.conf.enums import PermissionStatus, ExecutionStatus
from queqiao.conf.errors import InternalException, IllegalParamsException
from queqiao.conf.system import SystemConfig
from queqiao.dba import models
from queqiao.dba.models import Execution
from queqiao.lib.push import Pusher
from queqiao.log import LogFactory
from queqiao.util.comm.dtutil import timer

log = LogFactory.get_logger()


class Apply(models.Apply):
    pass


class Task(models.Task):
    pass


class TaskPermission(models.TaskPermission):
    def to_dict(self,
                includes=None,
                excludes=None):
        result = super().to_dict(includes, excludes)
        permission = result.pop('permission')
        permission = TaskPermission.decode_permission(permission)
        for p in permission.keys():
            result[p] = 1 if permission[p] else 0
        return result

    @property
    def managers(self):
        managers = self.task.project.admin_list
        managers.append(self.task.create_user)
        managers = list(set(managers))
        return managers

    @property
    def readable_permissions(self):
        permissions = TaskPermission.decode_permission(self.permission)
        readable_permissions = []
        for key in permissions.keys():
            if permissions[key]:
                if key == 'read':
                    permission_name = '读'
                elif key == 'write':
                    permission_name = '写'
                else:
                    permission_name = '执行'
                readable_permissions.append(permission_name)
        return ','.join(readable_permissions)

    def send_approve(self):
        # todo: 确认前端地址
        http_url = f'http://{SystemConfig.read("API_HOST")}:{SystemConfig.read("API_PORT")}'
        approve_url = f'{http_url}/api/{API_VERSION}/task/permission/approve/{self.id}'
        msg = f'{self.user_id} 申请任务 {self.task.name} {self.readable_permissions} 权限，审批链接: {approve_url}'
        pusher = Pusher.get_pusher()
        pusher.push(msg, [self.task.create_user])

    def send_approve_result(self, approver):
        result = '通过' if PermissionStatus(self.status) == PermissionStatus.ENTABLE else '被驳回'
        msg = f'您对任务 {self.task.name} 申请的权限 {self.readable_permissions} 已 {result} ！审批人：{approver}'
        pusher = Pusher.get_pusher()
        pusher.push(msg, [self.user_id])

    @classmethod
    def decode_permission(cls, pnum):
        if pnum < 1 or pnum > 7:
            raise InternalException(f'permission number must in [1,7], received {pnum}')
        rnum = int(bin(pnum)[2:])
        permission = {}

        hundred = int(rnum / 100)
        ten = int((rnum / 10) % 10)
        unit = rnum % 10
        # 个位数为1 +execute
        # 十位数为1 +write
        # 百位数为1 +read
        permission['read'] = hundred == 1
        permission['write'] = ten == 1
        permission['execute'] = unit == 1
        return permission

    @classmethod
    def encode_permission(cls, read=True, write=False, execute=False):
        permission = 0
        if read:
            permission += 100
        if write:
            permission += 10
        if execute:
            permission += 1
        linux_permission = int(f'{permission}', 2)
        if linux_permission < 1:
            raise IllegalParamsException(f'permission must be set a least one!')
        return linux_permission


class TaskType(models.TaskType):
    pass


# todo: 确认前端地址
class Alarm(models.Alarm):
    @classmethod
    def success(cls, execution_id=None, execution_qid=None):
        if not execution_id and not execution_qid:
            raise IllegalParamsException(f'execution.id or execution.qid must be set')
        execution = Execution.get(id=execution_id) if execution_id else Execution.get_one(qid=execution_qid)
        if ExecutionStatus(execution.status) == ExecutionStatus.SUCCESS:
            task = execution.task
            Alarm._info(execution.id, f'任务[{execution.execution_name}]执行成功, 点击查看执行详情: ', task.alarm_receivers)

    @classmethod
    def alarm(cls, alarm_content, execution_id=None, execution_qid=None):
        if not execution_id and not execution_qid:
            raise IllegalParamsException(f'execution.id or execution.qid must be set')
        execution = Execution.get(id=execution_id) if execution_id else Execution.get_one(qid=execution_qid)
        task = execution.task
        Alarm._warn(execution.id, f'任务[{execution.execution_name}]执行告警: {alarm_content}', task.alarm_receivers)

    @classmethod
    def failed(cls, execution_id=None, execution_qid=None):
        if not execution_id and not execution_qid:
            raise IllegalParamsException(f'execution.id or execution.qid must be set')
        execution = Execution.get(id=execution_id) if execution_id else Execution.get_one(qid=execution_qid)
        task = execution.task
        Alarm._info(execution.id, f'任务[{execution.execution_name}]执行失败, 点击查看执行详情: ', task.alarm_receivers)

    @classmethod
    def retry(cls, execution_id=None, execution_qid=None):
        if not execution_id and not execution_qid:
            raise IllegalParamsException(f'execution.id or execution.qid must be set')
        execution = Execution.get(id=execution_id) if execution_id else Execution.get_one(qid=execution_qid)
        task = execution.task
        params = json.loads(execution.params)
        Alarm._warn(execution.id,
                    f'任务[{execution.execution_name}]执行失败（将于{params["retry_interval"]}秒后重试, 重试次数{params["retry_times"]}）, 点击查看执行详情: ',
                    task.alarm_receivers)

    @classmethod
    def _info(cls, execution_id, msg, receivers):
        alarm_record = cls.new(receivers=receivers, content=msg, execution_id=execution_id)
        alarm_record.save()
        cls._info_pure(msg, receivers)

    @classmethod
    def _info_pure(cls, msg, receivers):
        pusher = Pusher.get_pusher()
        receivers = ','.join(receivers) if isinstance(receivers, list) else receivers
        pusher.push(msg, receivers)

    @classmethod
    def _warn(cls, execution_id, msg, receivers):
        pusher = Pusher.get_pusher()
        last_alarm_record = cls.get(execution_id=execution_id)
        last_alarm_record = last_alarm_record[-1] if len(last_alarm_record) > 0 else None
        if last_alarm_record and last_alarm_record.keep_quiet > 0:
            seconds = timer.time_diff(timer.now().datetime,
                                      timer.fromdt(str(last_alarm_record.create_time)).datetime)
            if seconds <= (last_alarm_record.keep_quiet * 60):
                log.debug(
                    f'current alarm interval seconds {seconds} is in quiet({last_alarm_record.keep_quiet} min), skip')
                return
        receivers = ','.join(receivers) if isinstance(receivers, list) else receivers
        alarm_record = cls.new(receivers=receivers, execution_id=execution_id)
        alarm_record.save()

        default_quite = 30
        http_url = f'http://{SystemConfig.read("API_HOST")}:{SystemConfig.read("API_PORT")}'
        content = f'{msg}, ack in 30 mins: {http_url}/api/{SystemConfig.read("API_VERSION")}/alarm/keep_quiet' \
                  f'/{alarm_record.id}?keep_quiet_n_mins={default_quite}'
        alarm_record.content = content
        alarm_record.save()

        pusher.push(content, receivers)
