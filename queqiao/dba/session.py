#  type: ignore

from functools import partial

import sqlalchemy.orm as orm
from flask import current_app
from flask_sqlalchemy import SQLAlchemy, get_state


class RoutingSession(orm.Session):

    '''
    https://gist.github.com/adhorn/b84dc47175259992d406
    '''

    def __init__(self, db, autocommit=False, autoflush=False, **options):
        self.app = db.get_app()
        self.db = db
        self._model_changes = {}
        orm.Session.__init__(
            self,
            autocommit=autocommit,
            autoflush=autoflush,
            bind=db.engine,
            binds=db.get_binds(self.app),
            **options
        )

    def get_bind(self, mapper=None, clause=None):
        try:
            state = get_state(self.app)
        except (AssertionError, AttributeError, TypeError) as err:
            current_app.logger.info(
                "cant get configuration. default bind. Error:" + err
            )
            return orm.Session.get_bind(self, mapper, clause)
        if state is None or not self.app.config['SQLALCHEMY_BINDS']:
            return orm.Session.get_bind(self, mapper, clause)

        if self._name:
            return state.db.get_engine(self.app, bind=self._name)

        if self._flushing:
            return state.db.get_engine(self.app, bind='master')

        # return state.db.get_engine(self.app, bind='slave')
        return state.db.get_engine(self.app, bind='master')

    _name = None

    def using_bind(self, name):
        sess = RoutingSession(self.db)
        vars(sess).update(vars(self))
        sess._name = name
        return sess


class RouteSQLAlchemy(SQLAlchemy):

    def __init__(self, *args, **kwargs):
        SQLAlchemy.__init__(self, *args, **kwargs)
        self.session.using_bind = lambda s: self.session().using_bind(s)

    def create_scoped_session(self, options=None):
        if options is None:
            options = {}
        scopefunc = options.pop('scopefunc', None)
        return orm.scoped_session(
            partial(RoutingSession, self, **options), scopefunc=scopefunc
        )
