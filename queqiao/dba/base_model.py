# coding=utf-8
from collections import Iterable
from contextlib import contextmanager
from datetime import datetime

from sqlalchemy import inspect, text
from sqlalchemy.ext.declarative import declared_attr
from sqlalchemy.orm.attributes import InstrumentedAttribute

from queqiao.dba import db
from queqiao.util.comm import objutil
from queqiao.util.comm.dtutil import timer


def format_field(field):
    if isinstance(field, datetime):
        return timer.fromdt(str(field)).datetime
    return field


FIXED_FIELDS = ['id', 'create_time', 'update_time', 'create_user', 'update_user', 'is_delete']


class BaseModel(db.Model):
    __abstract__ = True

    @declared_attr
    def create_time(cls):
        return db.Column(db.DateTime, server_default=db.func.now(), comment='创建时间', nullable=False)

    @declared_attr
    def update_time(cls):
        return db.Column(
            db.DateTime,
            server_default=text(
                'CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP'
            ),
            comment='更新时间',
            nullable=False
        )

    @classmethod
    def new(cls, **kwargs):
        now = datetime.now()
        return cls(create_time=now, update_time=now, **kwargs)

    @classmethod
    def delete_by(cls, **kwargs):
        query = cls.query.filter_by(**kwargs) if kwargs else cls.query
        query.delete()
        db.session.commit()

    @classmethod
    def safety_delete_by(cls, **kwargs):
        res = cls.get(**kwargs)
        if isinstance(res, Iterable):
            for r in res:
                r.safety_delete()
        else:
            res.safety_delete()

    @classmethod
    def __set_query_condition(cls, **kwargs):
        # 应用层等待时间超长导致session失效
        try:
            db.session.commit()
        except Exception as _:
            db.session.close()
        query = cls.query

        if hasattr(cls, 'is_delete'):
            if 'see_delete' not in kwargs:
                kwargs['is_delete'] = 0
            else:
                kwargs.pop('see_delete')
                kwargs['is_delete'] = 1

        if kwargs:
            query = query.filter_by(**kwargs)
        return query

    @classmethod
    def get(cls, **kwargs):
        if 'id' in kwargs:
            kwargs['only_one'] = 1
        one = False
        if 'only_one' in kwargs:
            one = True
            kwargs.pop('only_one')
        query = cls.__set_query_condition(**kwargs)
        if one:
            return query.first()
        else:
            return query.all()

    @classmethod
    def get_paginate(cls, limit, offset, **kwargs):
        '''
        分页查询，从第offset条开始取limit条数据
        :param limit: 每页条数
        :param offset: 从第n条开始取
        :return: 分页数据字典
        '''
        query = cls.__set_query_condition(**kwargs)
        paginate = query.paginate(page=int(offset / limit) + 1, per_page=int(limit), error_out=False)
        records = [obj.to_dict() for obj in paginate.items]
        return {'offset': (paginate.page - 1) * paginate.per_page, 'has_prev': paginate.has_prev,
                'prev': paginate.prev_num, 'has_next': paginate.has_next, 'next': paginate.next_num,
                'limit': paginate.per_page, 'pages': paginate.pages, 'total': paginate.total, 'records': records}

    # 不对返回结果做特殊处理
    @classmethod
    def get_with(cls, *filters):
        try:
            db.session.commit()
        except Exception as _:
            db.session.close()
        return cls.query.filter(*filters).all()

    @classmethod
    def get_one(cls, **kwargs):
        return cls.get(only_one=1, **kwargs)

    @classmethod
    def get_required_and_optional_fields(cls, get_fixed_fields=False):
        required_fileds = []
        optional_fields = []
        for info in objutil.get_cls_attributes(cls):
            aname, atype = info
            if isinstance(atype, InstrumentedAttribute) and hasattr(atype, 'nullable'):
                if not get_fixed_fields and str(aname) in FIXED_FIELDS:
                    continue
                field_list = optional_fields if getattr(atype, 'nullable') else required_fileds
                field_list.append(aname)
        return required_fileds, optional_fields

    @classmethod
    def exists(cls, **kwargs):
        res = cls.get(**kwargs)
        return True if res else False

    def to_dict(
            self,
            includes=None,
            excludes=None
    ):
        excludes = list(set(excludes)) if excludes else []
        includes = list(set(includes)) if includes else []
        if includes:
            filters = lambda x: x in includes
        elif excludes:
            filters = lambda x: x not in excludes
        else:
            filters = lambda x: True
        # if not with_record_time:
        #     filters.update(('update_time', 'create_time'))
        # if includes:
        #     filters.difference_update(includes)

        return {
            c.key: format_field(getattr(self, c.key))
            for c in inspect(self).mapper.column_attrs
            if filters(c.key)
        }

    @property
    def columns(self):
        # rf, of = self.__class__.get_required_and_optional_fields(get_fixed_fields=True)
        # rf.extend(of)
        # return rf
        column_attrs = inspect(self).mapper.column_attrs
        cols = [c.key for c in column_attrs]
        return cols

    @contextmanager
    def auto_commit(self):
        try:
            yield
            db.session.commit()
        except Exception as e:
            db.session.rollback()
            raise e

    def update(self, **kwargs):
        for k, v in kwargs.items():
            setattr(self, k, v)

    # 应用层save时如果obj提前创建很久（超出SQLALCHEMY_POOL_RECYCLE值），则可能出现connect reset by peer异常，需要进行异常处理（重新获取obj后save）
    def save(self):
        with self.auto_commit():
            self.update_time = timer.now().datetime
            db.session.add(self)

    def safety_delete(self):
        if not hasattr(self, 'is_delete'):
            self.delete()
            return
        self.is_delete = 1
        self.save()

    def delete(self):
        self.__class__.query.filter_by(id=self.id).delete()
        # BaseModel.query.filter_by(id=self.id).delete()
        db.session.commit()

    # def flush(self):
    #     db.session.add(self)
    #     db.session.flush()
    #
    # def commit(self):
    #     try:
    #         db.session.commit()
    #     except Exception as e:
    #         db.session.rollback()
    #         raise e

    # @classmethod
    # def batch_insert(cls, obj_list):
    #     if not isinstance(obj_list, list):
    #         raise IllegalParamsException(f'obj_list {obj_list} must be list, current type: {type(obj_list)}')
    #     db.session.execute(
    #         cls.__table__.insert(), obj_list
    #     )
    #     db.session.commit()
