"""
Author: xiaohei
Date: 2022/4/19
Email: <EMAIL>
Host: xiaohei.info
"""
from queqiao.dba.query import CustomQuery
from queqiao.dba.session import RouteSQLAlchemy

# db.engine.execute(sql) / db.session.execute(sql)
# db.create_all()

# engine
# session = db.session or sessionmaker(engine)
# session.execute(sql)
# session.query(User) or User.query()
db = RouteSQLAlchemy(
    query_class=CustomQuery, session_options={'expire_on_commit': False}
)
