"""
Author: xiaohei
Date: 2021/12/1
Email: <EMAIL>
Host: xiaohei.info
"""
from queqiao.util.comm import osutil


def get_path_bytes(path):
    du_cmd = f'hdfs dfs -du {path}' + " | awk \'{print $1}\'"
    res = osutil.callb(du_cmd)
    du_bytes = sum([int(r) for r in res])
    return du_bytes


def get_path_bytes_range(path, start, end):
    du_cmd = f'hdfs dfs -du {path}' + " | awk \'{print $1,$3}\'"
    res = osutil.callb(du_cmd)
    partitions = []
    for r in res:
        r = str(r).replace("b'", "").replace("\\n", "").replace("'", "")
        bt = int(r.split(' ')[0])
        partition = r.split('=')[-1]
        if start <= partition <= end:
            partitions.append(bt)
    du_bytes = sum(partitions)
    return du_bytes
