"""
Author: xiaohei
Date: 2022/5/7
Email: <EMAIL>
Host: xiaohei.info
"""
from instance.default import HOSTNAME
from queqiao.conf.system import SystemConfig
from queqiao.util.comm import osutil, strutil, fileutil
from queqiao.util.hadoop.hive import HiveClient


class SparkCmdClient(HiveClient):

    def __get_config(self, key):
        return self.__config.get(key) if key in self.__config.keys() else SystemConfig.read(key)

    def __init__(self, config=None, logger=None):
        super().__init__(logger)
        if config is None:
            config = {}
        self.__config = config
        self.cached_sql = []
        self.__deploy_mode = self.__get_config('SPARK_DEPLOY_MODE')

        self.__driver_memory = self.__get_config('SPARK_DEFAULT_DRIVER_MEMORY')
        self.__num_executors = self.__get_config('SPARK_DEFAULT_NUM_EXECUTORS')
        self.__executor_memory = self.__get_config('SPARK_DEFAULT_EXECUTOR_MEMORY')
        self.__executor_cores = self.__get_config('SPARK_DEFAULT_EXECUTOR_CORES')

        self.__yarn_queue = self.__get_config('SPARK_YARN_QUEUE')

        self.__keytab = self.__get_config('KERBEROS_KEYTAB')
        self.__ktuser = self.__get_config('KERBEROS_USER')
        self.__ktuser = self.__ktuser.replace('_HOST', HOSTNAME)
        # spark-sql --master yarn --deploy-mode cluster --queue root.zw03.hadoop-fspcom.etltest --driver-memory 1G --executor-memory 5G --executor-cores 1 --num-executors 10
        self.__submit_spark_sql = ['spark-sql']
        self.__exec_once = True

    def set_exec_once(self):
        self.__exec_once = True

    def set_exec_cache(self):
        self.__exec_once = False

    def set_yarn_mode(self, job_name):
        self.__submit_spark_sql.append(f'--master yarn --deploy-mode {self.__deploy_mode}')
        self.__submit_spark_sql.append(f'--queue {self.__yarn_queue}')
        self.__submit_spark_sql.append(f'--driver-memory {self.__driver_memory}')
        self.__submit_spark_sql.append(f'--name fspinno_queqiao:{job_name}')
        self.__submit_spark_sql.append(f'--num-executors {self.__num_executors}')
        self.__submit_spark_sql.append(f'--executor-memory {self.__executor_memory}')
        self.__submit_spark_sql.append(f'--executor-cores {self.__executor_cores}')
        spark_submit_configs = SystemConfig.read('SPARK_SUBMIT_CONFIGS')
        if spark_submit_configs and spark_submit_configs.startswith('--'):
            self.logger.info(f'get spark submit configs: {spark_submit_configs}')
            self.__submit_spark_sql.append(spark_submit_configs)

    def set_local_mode(self):
        self.__submit_spark_sql = ['spark-sql']

    def open(self):
        kinit = f'kinit -kt {self.__keytab} {self.__ktuser}'
        klist_ret = osutil.callb('klist')
        if len(klist_ret) < 2:
            self.logger.info(f'kerberos principal not found, will kinit with {kinit}')
            self.logger.info(f'auth kerberos: {kinit}')
            osutil.call(kinit)

    def close(self):
        pass

    def _select_and_download(self, sql, save_file):
        self.logger.info('start download from spark server...')
        submit_spark_sql = list(self.__submit_spark_sql)
        sql = sql.replace('"', '\\"')
        submit_spark_sql.append(f'-e "{sql};"')
        submit_spark_sql = ' '.join(submit_spark_sql)
        submit_spark_sql = f'{submit_spark_sql} > {save_file}'.replace('--deploy-mode cluster', '--deploy-mode client')
        self.logger.info(f'submit spark sql command: ')
        self.logger.info(submit_spark_sql)

        ret1 = osutil.call(submit_spark_sql)
        if ret1 != 0:
            raise Exception('sparksql execute failed!')
        ret2 = fileutil.reset_sep(save_file, curr_sep='\t', target_sep=self._col_sep)
        if ret2 != 0:
            raise Exception(f'reset sep failed! saved file path: {save_file}')

        self.logger.info('download from server finished')

    def exec(self, sql, async_=False):
        if self.__exec_once:
            submit_spark_sql = list(self.__submit_spark_sql)
            sql = sql.replace('"', '\\"')
            submit_spark_sql.append(f'-e "{sql};"')
            submit_spark_sql = ' '.join(submit_spark_sql)
            self.logger.info(f'submit spark sql command: ')
            self.logger.info(submit_spark_sql)
            return osutil.call(submit_spark_sql)

        self.cached_sql.append(sql + ';')
        # async为true时效果为flush（执行）
        # async为false时效果为缓存
        if not async_:
            return 0

        sql = ['set hive.exec.dynamic.partition=true;', 'set hive.exec.dynamic.partition.mode=nonstrict;',
               'set spark.sql.storeAssignmentPolicy=LEGACY;']
        sql.extend(self.cached_sql)

        uid = strutil.uid()
        tmp_sql_file = f'/tmp/queqiao_sparksql_{uid}.sql'
        with open(tmp_sql_file, 'w') as sql_file:
            sql_file.write('\n'.join(sql))
        submit_spark_sql = list(self.__submit_spark_sql)
        submit_spark_sql.append(f'-f {tmp_sql_file}')
        submit_spark_sql = ' '.join(submit_spark_sql)
        self.logger.info(f'submit spark sql command: ')
        self.logger.info(submit_spark_sql)
        self.logger.debug(f'sql file content:')
        self.logger.debug('\n'.join(sql))
        ret = osutil.calls(submit_spark_sql, stderr=True)
        errors = [line for line in ret if line.startswith('Error ') or line.startswith('Exception ')]
        rcode = 0 if len(errors) == 0 else 1
        if rcode == 0:
            osutil.rm(tmp_sql_file)
        else:
            with open(f'/tmp/queqiao_sparksql_{uid}.err', 'w') as err:
                err.write(f'cached sql: \n')
                for sql in self.cached_sql:
                    err.write(sql + '\n')
                for r in ret:
                    err.write(r + '\n')
        return rcode

    def schema(self, tablename):
        columns = []
        sql = f'desc {tablename}'
        cmd = f'spark-sql -e "{sql}"'
        res = osutil.calls(cmd)
        col_info1 = [r.replace('\n', '').replace('\t', ',').replace('NULL', '').replace('null', '') for r in res if
                     '#' not in r]
        col_info = []
        [col_info.append(i) for i in col_info1 if i not in col_info]

        for col in col_info:
            arr = col.split(',')
            column = {
                'name': arr[0],
                'type': arr[1],
                'comment': arr[2] if len(arr) == 3 else ''
            }
            columns.append(column)
        return columns
