"""
Author: xiaohei
Date: 2022/5/7
Email: <EMAIL>
Host: xiaohei.info
"""
import time

from TCLIService.ttypes import TOperationState
from pyhive import hive

from queqiao.util.comm import osutil
from queqiao.util.comm.dtutil import timer
from queqiao.util.hadoop.hive import HiveClient


class PyhiveClient(HiveClient):

    def __kinit(self):
        kinit = f'kinit -kt {self.keytab} {self.kuser}'
        klist_ret = osutil.calls('klist')
        if len(klist_ret) < 2:
            self.logger.info(f'kerberos principal not found, will kinit for {self.kuser}')
            self.logger.info(f'auth kerberos: {kinit}')
            osutil.call(kinit)
        else:
            principal = str(klist_ret[1])
            if self.kuser not in principal:
                self.logger.info(f'current principal is {principal}, will kinit for {self.kuser}')
                self.logger.info(f'auth kerberos: {kinit}')
                osutil.call(kinit)

    def __init__(self, pyhive_host, pyhive_port,
                 kerberos_service_name, kerberos_user,
                 beeline_u, keytab, krb_host=None,
                 logger=None):
        super().__init__(logger)
        self.logger.debug(f'init pyhive client with pyhive_host: {pyhive_host}, pyhive_port: {pyhive_port},'
                          f'kerberos_service_name: {kerberos_service_name}, kerberos_user: {kerberos_user}, '
                          f'keytab: {keytab}, beeline_u: {beeline_u}')
        self._col_sep = "$'\001'"
        self.keytab = keytab
        self.kuser = kerberos_user
        self.__kinit()
        connect_params = {'host': pyhive_host, 'port': pyhive_port, 'auth': 'KERBEROS',
                          'kerberos_service_name': kerberos_service_name}
        if krb_host:
            connect_params['krb_host'] = krb_host
        try:
            self.__client = hive.connect(**connect_params)
        except Exception as why:
            self.logger.error(f'hive connect error: {why}, sleep 5 and try to reconnect with kinit')
            time.sleep(5)
            self.__client = hive.connect(**connect_params)

        self.__client = self.__client.cursor()
        self.__beeline_u = beeline_u
        self.logger.debug(f'pyhive client init success')

    def open(self):
        self.__kinit()

    def close(self):
        try:
            if self.__client:
                self.__client.close()
        except Exception as why:
            self.logger.error(f'pyhive close error: {why}')

    def _select_and_download(self, sql, save_file):
        self.logger.info('start download from hive server...')

        # 直接使用beeline下载
        sql = sql if sql.endswith(';') else f'{sql};'
        beeline_cmd = f'beeline --escapeCRLF=true --outputformat=dsv --showHeader=false --delimiterForDSV={self._col_sep} ' \
                      f'-u "{self.__beeline_u}" -e "{sql}" > {save_file}'
        self.logger.info(f'start exec beeline cmd: {beeline_cmd}')
        ret_code = osutil.call(beeline_cmd)
        if ret_code != 0:
            raise Exception('beeline execute failed!')
        self.logger.info('download from server finished')

    def exec(self, sql, async_=False):
        '''
        使用场景：insert、drop、desc等无执行结果或者少量执行结果的sql语句，大数据量的执行使用query+download
        :param sql: 执行的sql
        :param async_: 是否同步等待
        :return:
        '''
        self.__client.execute(sql, async_=True)
        self.logger.info(f'sql submited: {sql}')
        # 同步模式
        if not async_:
            result = self.__wait_for_finish()
            return 0 if result else 1
        # 异步模式不用处理，后续自行获取结果

    def schema(self, tablename):
        sql = f'desc {tablename}'
        self.__client.execute(sql)
        result = self.__client.fetchall()
        columns = []
        for r in result:
            if r[0] and '#' not in r[0]:
                meta = {"name": r[0], "type": r[1], "comment": r[2]}
                if meta not in columns:
                    columns.append(meta)
        self.logger.info(f'get meta success, columns meta: {columns}')
        return columns

    def __wait_for_finish(self, sleep_time=5, max_wait_time=10800):
        start_time = timer.now()
        while True:
            status = self.__client.poll().operationState
            if status in [TOperationState.ERROR_STATE, TOperationState.TIMEDOUT_STATE, TOperationState.UKNOWN_STATE]:
                exec_log = self.__client.fetch_logs()
                print_log = '\n'.join(exec_log)
                self.logger.error(f'sql execute error: {print_log}')
                return None
            # CANCELED_STATE
            # CLOSED_STATE
            # ERROR_STATE
            # FINISHED_STATE
            # INITIALIZED_STATE
            # PENDING_STATE
            # RUNNING_STATE
            # TIMEDOUT_STATE
            # UKNOWN_STATE
            elif status == TOperationState.FINISHED_STATE:
                self.logger.info('sql execute success')
                desc = None
                results = None
                try:
                    desc = self.__client.description
                    results = self.__client.fetchall()
                except Exception as why:
                    pass
                return {'desc': desc, 'results': results}
            else:
                self.logger.info('waiting for finished...')
            elapsed = (timer.now() - start_time).seconds
            if elapsed > max_wait_time:
                raise TimeoutError(f'current query runnning for a long time({max_wait_time / 3600}h), '
                                   f'stop to wait and raise exception, please retry')
            time.sleep(sleep_time)
