"""
Author: xiaohei
Date: 2021/10/18
Email: <EMAIL>
Host: xiaohei.info
"""
import abc

import six

from instance.default import TMP_FOLDER
from queqiao.log import LogFactory
from queqiao.util.comm import osutil, strutil, sqlutil


@six.add_metaclass(abc.ABCMeta)
class HiveClient:
    def __init__(self, logger=None):
        self.logger = logger if logger else LogFactory.get_logger()
        self._col_sep = '\x01'
        self._save_path = f'{TMP_FOLDER}/hiveclient'

    def set_default_col_sep(self, col_sep):
        self._col_sep = col_sep if col_sep != 'special' else self._col_sep

    def set_default_save_path(self, save_path):
        self._save_path = save_path

    def __get_query_info(self, sql, save_file):
        if not osutil.exists(save_file):
            return {'total': -1, 'resultSize': -1, 'status': -1, 'columns': [], 'file_path': save_file}
        query_info = {'total': osutil.wc(save_file), 'resultSize': osutil.bytes(save_file), 'status': 0,
                      'file_path': save_file}
        if 'select ' in sql.lower():
            self.logger.info('generating select sql query info...')
            sql_cols = sqlutil.get_select_cols(sql)
            sql_tables = sqlutil.get_tables(sql)
            # todo: select distinct xxx 会识别出distinct字段
            self.logger.info(f'get sql_cols: {sql_cols}, sql_tables: {sql_tables} from select sql')
            # 多表关联查询情况下暂时无法解析正确表与字段，使用多表schema处理方法
            # 在此情况下sql_cols的值可能包含as别名不可使用
            multiple_table_query = len(sql_tables) > 1
            table_cols = self.schema(list(sql_tables)[0]) if not multiple_table_query else self.schema_multiple(
                sql_tables)
            self.logger.info(f'multiple_table_query: {multiple_table_query}, table_cols: {table_cols}')
            table_cols_meta = {}
            for col_meta in table_cols:
                # 去除空、#注释、重复字段
                if col_meta['name'] in ['', '#'] or col_meta['name'] in table_cols_meta.keys():
                    continue
                table_cols_meta[col_meta['name']] = col_meta
            # 以sql_cols字段的顺序和内容为准,如果sql_cols中存在*则取查询到的meta
            # todo: select * 在复杂子查询中也触发导致异常
            query_info['columns'] = list(table_cols_meta.values()) if '*' in sql_cols or multiple_table_query else [
                table_cols_meta[c] if c in table_cols_meta else {'name': c, 'type': 'string', 'comment': ''} for c in
                sql_cols]
        return query_info

    @abc.abstractmethod
    def open(self):
        '''
        打开连接
        '''
        pass

    @abc.abstractmethod
    def close(self):
        '''
        关闭连接
        '''
        pass

    def query(self, sql):
        '''
        执行查询并等待执行结束后下载结果至本地
        :param sql: 执行sql
        :return: query_info，包括total,resultSize/status/columns/file_path等信息
        '''
        # 查询时判断存储路径是否存在,避免初始化时部分环境/opt无权限创建,可以使用set_default_save_path重置
        if not osutil.exists(self._save_path):
            osutil.mkdir(self._save_path)

        result_filename = f'{self.__class__.__name__}_{strutil.uid(rung=True)}.txt'
        save_file = f'{self._save_path}/{result_filename}'
        self._select_and_download(sql, save_file)
        return self.__get_query_info(sql, save_file)

    @abc.abstractmethod
    def _select_and_download(self, sql, save_file):
        pass

    @abc.abstractmethod
    def exec(self, sql, async_=False):
        '''
        执行sql,用于DDL/DML类型操作等无需获取结果数据的场景
        :param sql: 执行sql
        :return: rcode
        '''
        pass

    @abc.abstractmethod
    def schema(self, tablename):
        '''
        获取表schema信息(列名、类型、注释)
        :param tablename: 目标表名
        :return: 列信息列表
        '''
        pass

    def schema_multiple(self, tablenames):
        '''
        多表关联查询情况下对表结构的特殊处理，默认返回空数组（即无法解析）
        在特殊客户端（talos）中可从query结果中获取查询信息并返回
        调用此方法后后续将不再对返回的字段信息进行筛选处理
        默认返回空的情况下下游无法使用建表类sink
        :param tablenames: 查询表列表
        :return: 列信息列表
        '''
        return []
