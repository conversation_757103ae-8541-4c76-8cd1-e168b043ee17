"""
Author: xiaohei
Date: 2022/9/12
Email: <EMAIL>
Host: xiaohei.info
"""
import json
import time

from queqiao.conf.errors import CmdExecuteFailedException
from queqiao.log import LogFactory
from queqiao.util.comm import osutil, fileutil
from queqiao.util.conn.http import HttpClient


class FateClient:
    def __init__(self, ip, port, api_version='v1', logger=None):
        self.ip = ip
        self.port = port
        self.base_url = f'http://{ip}:{port}/{api_version}'
        self.logger = logger if logger else LogFactory.get_logger()

    def open(self):
        pass

    def data_upload(self, file_path, partition, namespace, tablename, work_mode=1, head=1):
        uri = '/data/upload'
        params = {
            "file": file_path,
            # "id_delimiter": ",",
            "head": head,
            "partition": partition,
            "work_mode": work_mode,
            "namespace": namespace,
            "table_name": tablename,
            "drop": True
        }
        return HttpClient.upload(self.base_url + uri, file_path, params=json.dumps(params))

    def data_download(self, namespace, tablename, savefile):
        uri = '/data/download'
        params = {
            "output_path": savefile,
            "namespace": namespace,
            "table_name": tablename
        }
        return HttpClient.request(self.base_url + uri, json=params)

    def table_info(self, namespace, tablename):
        uri = '/table/table_info'
        params = {
            'table_name': tablename,
            'namespace': namespace
        }
        return HttpClient.request(self.base_url + uri, json=params)

    def job_submit(self, conf_c, conf_d):
        uri = '/job/submit'
        conf_c = conf_c if isinstance(conf_c, dict) else json.loads(str(conf_c))
        conf_d = conf_d if isinstance(conf_d, dict) else json.loads(str(conf_d))
        params = {
            'dsl': conf_d,
            'runtime_conf': conf_c
        }
        return HttpClient.request(self.base_url + uri, json=params)

    def job_query(self, role, job_id):
        uri = '/job/query'
        params = {
            'job_id': job_id,
        }
        if role:
            params['role'] = role
        return HttpClient.request(self.base_url + uri, json=params)

    def job_status(self, role, job_id):
        job_info = self.job_query(role, job_id)
        data = job_info['data'][0]
        return data['f_status'], data['f_progress']

    def job_rerun(self, job_id):
        uri = '/job/rerun'
        params = {
            'job_id': job_id
        }
        return HttpClient.request(self.base_url + uri, json=params)

    def wait_job(self, job_id, role=None):
        status, progress = self.job_status(role, job_id)
        while status not in ['success', 'failed', 'canceled']:
            self.logger.info(f'{job_id} status: {status}, progress: {progress}%')
            time.sleep(1)
            status, progress = self.job_status(role, job_id)
        self.logger.info(f'job {job_id} finished with {status}')
        return status

    def output_data(self, job_id, role, party_id, component, save_path):
        uri = '/tracking/component/output/data/download'
        params = {
            'job_id': job_id,
            'role': role,
            'party_id': party_id,
            'component_name': component
        }
        save_path = f'{save_path}/job_{job_id}_{component}_{role}_{party_id}_output_data'
        file_namme = 'data.tar.gz'
        if not osutil.exists(save_path):
            osutil.mkdir(save_path)
        HttpClient.download(self.base_url + uri, f'{save_path}/{file_namme}', json=params)
        ret = osutil.tar_zxvf(f'{save_path}/{file_namme}')
        result_files = osutil.list_files(save_path)
        # 判断解压后文件数量是否正确，一般为*.csv、*.meta
        self.logger.info(f'untar finished with code {ret}, there are {len(result_files)} files in dir: {result_files}')
        if len(result_files) < 2:
            self.logger.warn('result of current job may by wrong, please check yourself')
        # 下载的tar包可能解压异常，但数据文件正确
        # if ret != 0:
        #     raise CmdExecuteFailedException(f'untar failed, file path: {save_path}/{file_namme}')
        return save_path

    def get_summary(self, job_id, role, party_id, component):
        uri = '/tracking/component/summary/download'
        params = {
            'job_id': job_id,
            'role': role,
            'party_id': party_id,
            'component_name': component
        }
        return HttpClient.request(self.base_url + uri, json=params)
