"""
Author: xiaohei
Date: 2022/5/6
Email: <EMAIL>
Host: xiaohei.info
"""

# !/usr/bin/evn python
# -*- coding: utf-8 -*-
import logging

from ldap3 import ALL_ATTRIBUTES, Connection, ServerPool, SUBTREE

from queqiao.conf import Config

log = logging.getLogger(__name__)

LDAP_REQUIRED_CONFIGS = ['LDAP_SERVER_POOL', 'LDAP_SERVER_PORT', 'LDAP_SEARCH_DN', 'LDAP_SEARCH_PASSWORD',
                         'LDAP_SEARCH_BASE', 'LDAP_GROUP_SEARCH_BASE']


class LdapAuth:
    def __init__(self, **config):
        if not config:
            raise Exception(f'ldap config not found!')
        self.config = Config(config)
        # 'LDAP_SERVER_POOL', 'LDAP_SERVER_PORT', 'LDAP_SEARCH_DN', 'LDAP_SEARCH_PASSWORD',
        #                             'LDAP_SEARCH_BASE', 'LDAP_GROUP_SEARCH_BASE'
        # optional:LDAP_ALLOW_GROUPS: None
        miss_configs = []
        for c in LDAP_REQUIRED_CONFIGS:
            if not hasattr(self.config, c):
                miss_configs.append(c)
        if miss_configs:
            raise Exception(f'required configs {miss_configs} not found')

    def auth_user(self, username, password):
        auth_flag = False
        ldap_entry = self.get_user(username)
        if ldap_entry and ldap_entry.dn:
            ldap_server_pool = ServerPool(self.config.LDAP_SERVER_POOL)
            conn2 = Connection(ldap_server_pool, user=ldap_entry.dn, password=password, check_names=True, lazy=False,
                               raise_exceptions=False, read_only=True)
            try:
                if conn2.bind():
                    auth_flag = True
            except Exception as err:
                log.warning("{} login bind error {}".format(username, err))
                raise Exception(err)
            finally:
                conn2.unbind()
        return auth_flag, ldap_entry

    def get_user(self, username):
        ldap_entry = None
        if username and len(username) > 0:
            ldap_server_pool = ServerPool(self.config.LDAP_SERVER_POOL)
            conn = Connection(ldap_server_pool, user=self.config.LDAP_SEARCH_DN,
                              password=self.config.LDAP_SEARCH_PASSWORD,
                              check_names=True, lazy=False,
                              raise_exceptions=False,
                              read_only=True)
            try:
                conn.open()
                conn.bind()
                res = conn.search(
                    search_base=self.config.LDAP_SEARCH_BASE,
                    search_filter=self.__assemble_user_filter(username),
                    search_scope=SUBTREE,
                    attributes=ALL_ATTRIBUTES,
                    paged_size=5
                )
                if res:
                    entry = conn.response[0]
                    ldap_entry = LdapEntry(entry)
            except Exception as err:
                raise Exception("search user {} error {}".format(username, err))
            finally:
                conn.unbind()

        return ldap_entry

    def __assemble_user_filter(self, username):
        if not self.config.LDAP_ALLOW_GROUPS:
            return '(uid={})'.format(username)
        allow_groups = self.config.LDAP_ALLOW_GROUPS.split(',')
        group_filter = "(|{})".format("".join(
            ["(memberOf=cn={},{})".format(group, self.config.LDAP_GROUP_SEARCH_BASE) for group in allow_groups]))
        return "(&({}={}){})".format('uid', username, group_filter)


class LdapEntry:
    def __init__(self, entry):
        self.uid = entry.get("attributes").get('uid')[0]
        self.gid = entry.get("attributes").get('uidNumber')
        self.groups = [group.split(',')[0].split('=')[1] for group in entry.get("attributes").get("memberOf")]
        self.dn = entry['dn']
        # self.is_super_user = len(set(self.groups).intersection(
        #     set(helper.get_env_conf('LDAP_SUPER_USER_GROUP')))) > 0 if self.groups else False
