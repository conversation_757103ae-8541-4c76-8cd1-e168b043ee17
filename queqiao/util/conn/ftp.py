# coding=utf-8

"""
Author: xiaohei
Date: 2020-10-17
Email: <EMAIL>
Host: xiaohei.info
"""
import abc
import fnmatch
import os
import socket
import stat
import time
from ftplib import FTP, error_perm

import paramiko
import six

from instance.default import TMP_FOLDER
from queqiao.conf import IllegalParamsException
from queqiao.conf.errors import NotFoundException
from queqiao.conf.system import SystemConfig
from queqiao.log import LogFactory
from queqiao.util.comm import strutil, osutil
from queqiao.util.comm.dtutil import timer

log = LogFactory.get_logger()


@six.add_metaclass(abc.ABCMeta)
class RemoteFileServer:
    def __init__(self, ftp_config, logger=None):
        self.logger = logger if logger else LogFactory.get_logger()
        self.logger.debug(f'start get_connect ssh connection with config: {ftp_config} ')
        # for k in ftp_config:
        #     setattr(self, k, ftp_config[k])
        self.protocol = ftp_config.get('protocol', None)
        self.ip = ftp_config.get('ip', None)
        self.port = ftp_config.get('port', None)
        self.username = ftp_config.get('username', None)
        self.password = ftp_config.get('password', None)
        self.work_dir = ftp_config.get('work_dir', None)
        self.client = None
        self.__k = None

    @abc.abstractmethod
    def open(self):
        pass

    @abc.abstractmethod
    def close(self):
        pass

    @abc.abstractmethod
    def exists(self, target_path):
        pass

    @abc.abstractmethod
    def stat(self, target_path):
        pass

    @abc.abstractmethod
    def upload(self, local_file, target_file):
        pass

    @abc.abstractmethod
    def download(self, target_file, local_file):
        pass

    @abc.abstractmethod
    def read(self, target_file):
        pass

    @abc.abstractmethod
    def write(self, content, target_file):
        pass

    @abc.abstractmethod
    def delete(self, filepath):
        pass

    @abc.abstractmethod
    def list_dir(self, path):
        pass

    @abc.abstractmethod
    def list_dir_info(self, path):
        """获取指定路径下的所有文件名和对应的属性

        Args:
            path: 指定路径

        Returns:
            list: [(文件名, 是否为目录)]
        """
        pass

    @abc.abstractmethod
    def wc(self, target_file):
        """获取指定文件的总行数

        Args:
            target_file: 目标文件路径

        Returns:
            int: 文件总行数
        """
        pass

    @abc.abstractmethod
    def headn(self, target_file, n=100):
        """获取指定文件的前 n 行

        Args:
            target_file: 目标文件路径
            n: 要获取的行数，默认100行

        Returns:
            str: 文件前n行的内容
        """
        pass

    def test(self, test_path):
        print(self.listdir(test_path))

    @classmethod
    def get_connect(cls, config, logger=None):
        if not config:
            raise IllegalParamsException(f'config must not null')
        try:
            if config['protocol'] == 'ftp':
                connect = FtpServer(config, logger)
                log.debug(f'get_connect ftp file server from config: f{config}')
            elif config['protocol'] == 'sftp':
                connect = SftpServer(config, logger)
                log.debug(f'get_connect sftp file server from config: f{config}')
            elif config['protocol'] == 'wtp':
                connect = WtpServer(config, logger)
            else:
                raise IllegalParamsException(f'protocol type {config["protocol"]} can not found any remote server')
        except Exception as why:
            raise IllegalParamsException(why)
        return connect

    @staticmethod
    def check_port_connectivity(host, port, timeout=3):
        """
        检查指定主机和端口是否可连通
        
        Args:
            host: 主机地址
            port: 端口号
            timeout: 超时时间(秒)，默认3秒
            
        Returns:
            tuple: (bool, str) - (是否可连通, 错误信息)
        """
        try:
            # 先验证端口号范围
            port = int(port)
            if port < 0 or port > 65535:
                return False, "Port number must be 0-65535"
                
            # 创建socket连接
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(timeout)
            
            # 尝试解析主机名
            try:
                socket.gethostbyname(host)
            except socket.gaierror:
                return False, f"Failed to resolve host: {host}"
                
            # 尝试连接
            result = sock.connect_ex((host, port))
            sock.close()
            
            if result == 0:
                return True, None
            else:
                return False, f"Port {port} is not accessible on host {host}"
                
        except ValueError as e:
            return False, f"Port number must be 0-65535"
        except Exception as e:
            return False, f"Error checking port connectivity: {str(e)}"
            
    @classmethod
    def test_connection(cls, config):
        """
        测试FTP/SFTP连接是否正常
        
        Args:
            config: dict, 包含连接配置信息
                必需字段：protocol, ip/host, port, username, password
                
        Returns:
            tuple: (bool, str) - (连接是否成功, 错误信息)
        """
        if not config:
            raise IllegalParamsException('config must not be null')
            
        try:
            # 统一配置字段
            if 'host' in config:
                config['ip'] = config.pop('host')
                
            # 先检查端口连通性
            is_accessible, error_msg = cls.check_port_connectivity(config['ip'], config['port'])
            if not is_accessible:
                return False, error_msg
                
            # 设置较短的超时时间进行连接测试
            socket.setdefaulttimeout(10)  # 设置10秒超时
            conn = cls.get_connect(config)
            conn.work_dir = config.get('work_dir', '/')
            conn.open()
            conn.close()
            return True, None
        except Exception as e:
            error_msg = str(e)
            if isinstance(e, socket.gaierror):
                error_msg = f"Failed to resolve server address: {config.get('ip', 'unknown')}"
            elif isinstance(e, ConnectionRefusedError):
                error_msg = f"Connection refused, please check server address and port: {config.get('ip', 'unknown')}:{config.get('port', 'unknown')}"
            elif isinstance(e, (paramiko.AuthenticationException, error_perm)):
                error_msg = "Invalid username or password"
            elif isinstance(e, paramiko.SSHException):
                error_msg = "SSH connection error, please check protocol type"
            elif isinstance(e, socket.timeout):
                error_msg = "Connection timeout, please check network status"
            
            log.error(f"Failed to connect to {config.get('protocol', 'unknown')} server {config.get('ip', 'unknown')}:{config.get('port', 'unknown')}: {error_msg}")
            return False, error_msg


class FtpServer(RemoteFileServer):
    def open(self):
        socket.setdefaulttimeout(180)
        ftp = FTP()
        ftp.connect(host=self.ip, port=self.port)
        ftp.set_debuglevel(2)  # 开启调试模式
        ftp.encoding = 'utf-8'

        try:
            ftp.login(self.username, self.password)
            self.logger.debug(
                '[{}]login ftp {}'.format(
                    self.username,
                    ftp.getwelcome()))  # 打印欢迎信息

        except(socket.error, socket.gaierror):  # ftp 连接错误
            self.logger.warn(
                "ERROR: cannot open [{}:{}]".format(
                    self.ip, self.port))
            return None

        except error_perm:  # 用户登录认证错误
            self.logger.warn("ERROR: user Authentication failed ")
            return None
        except Exception as e:
            print(e)
            return None
        self.client = ftp
        self.client.cwd(self.work_dir)
        self.logger.debug(f'sftp connected! switch to work dir: {self.work_dir}')

    def close(self):
        self.client.close()
        self.logger.debug('ftp connection closed')

    def exists(self, target_path):
        try:
            self.client.dir(target_path)
            return True
        except Exception as e:
            self.logger.error('ftp cwd error: %s' % str(e))
            return False

    def stat(self, target_path):
        try:
            # 获取文件大小
            self.client.voidcmd('TYPE I')  # 切换到二进制模式
            size = self.client.size(target_path)
            
            # 获取文件修改时间
            mtime_str = self.client.sendcmd('MDTM ' + target_path)[4:]
            # 将FTP返回的时间字符串转换为时间戳
            mtime = time.mktime(time.strptime(mtime_str, '%Y%m%d%H%M%S'))
            
            # 返回包含文件信息的对象
            class FileStat:
                def __init__(self, size, mtime):
                    self.st_size = size
                    self.st_mtime = mtime
                    
            return FileStat(size, mtime)
            
        except error_perm as e:
            self.logger.error(f'Failed to get file stat: {str(e)}')
            raise NotFoundException(f'File {target_path} not found on FTP server')
        except Exception as e:
            self.logger.error(f'Error getting file stat: {str(e)}')
            raise

    def upload(self, local_file, target_file):
        """上传文件到FTP服务器
        
        Args:
            local_file: 本地文件路径
            target_file: 目标文件路径（包含文件名）
        """
        # 分离目标路径和文件名
        target_path = '/'.join(target_file.split('/')[:-1])
        target_file = target_file.split('/')[-1]
        
        # 创建多级目录
        if not self.exists(target_path):
            self.logger.info('Creating directory path: %s on ftp server' % target_path)
            current = ''
            for folder in target_path.split('/'):
                if not folder:
                    continue
                current = current + '/' + folder
                try:
                    if not self.exists(current):
                        self.client.mkd(current)
                except Exception as e:
                    # 忽略目录已存在的错误
                    if not str(e).startswith('550'):
                        raise

        buffer_size = 10240  # 默认是8192
        fp = open(local_file, 'rb')

        try:
            self.client.cwd(target_path)
            self.logger.info(
                "Found directory [{}] on ftp server, starting upload...".format(target_path))
            # 将传输模式改为二进制模式 ,避免提示 ftplib.error_perm: 550 SIZE not allowed in ASCII
            self.client.voidcmd('TYPE I')
            self.client.storbinary('STOR ' + target_file, fp, buffer_size)
            self.client.set_debuglevel(0)
            self.logger.info('Successfully uploaded local file: %s to ftp server: %s' % (local_file, f'{target_path}/{target_file}'))
        except error_perm as e:
            self.logger.error('File [{}] transfer failed: {}'.format(target_file, str(e)))
            raise
        except TimeoutError:
            self.logger.error('File [{}] transfer timeout'.format(target_file))
            raise
        except Exception as e:
            self.logger.error('File [{}] transfer error: {}'.format(target_file, str(e)))
            raise
        finally:
            fp.close()

    def download(self, target_file, local_file):
        target_path = '/'.join(target_file.split('/')[:-1])
        target_file = target_file.split('/')[-1]
        self.client.cwd(target_path)
        self.client.retrbinary("RETR " + target_file, open(local_file, 'wb').write)

    def read(self, target_file):
        filename = target_file.split('/')[-1]
        local_file = os.path.join(TMP_FOLDER, str(timer.now().timestamp) + '_' + filename)
        bufsize = 1024  # 设置缓冲块大小
        fp = open(local_file, 'wb')
        self.client.retrbinary('RETR ' + target_file, fp.write, bufsize)  # 接收服务器上文件并写入本地文件
        fp.close()
        # 重新读取本地文件
        fp = open(local_file, 'r')
        content = fp.read()
        fp.close()
        os.remove(local_file)
        return content

    def write(self, content, target_file):
        target_path = '/'.join(target_file.split('/')[:-1])
        bufsize = 1024
        filename = target_file.split('/')[-1]
        # 大并发场景下会有文件名冲突的问题
        local_file = os.path.join(TMP_FOLDER, str(timer.now().timestamp) + '_' + filename)
        fp = open(local_file, 'w')
        fp.write(content)
        fp.close()

        # 重新读取本地文件
        fp = open(local_file, 'rb')
        self.client.cwd(target_path)
        self.client.voidcmd('TYPE I')
        self.client.storbinary('STOR ' + target_file, fp, bufsize)
        fp.close()
        os.remove(local_file)

    def delete(self, filepath):
        self.client.delete(filepath)

    def list_dir(self, path):
        try:
            dirs = self.client.nlst(path)
        except Exception as why:
            dirs = []
        return dirs

    def list_dir_info(self, path):
        flist = []
        self.client.dir(path, flist.append)
        result = []
        for item in flist:
            parts = item.split()
            name = parts[-1]
            is_dir = item.startswith('d')
            result.append({'name': name, 'type': 'dir' if is_dir else 'file'})
        return result

    def list_files(self, path, file_list, suffix=None):
        '''
        递归获取指定目录下的所有文件
        :param path: 指定路径
        :param file_list: 返回的文件名列表
        :param suffix: 指定前缀
        :return:
        '''
        flist = []
        self.client.dir(path, flist.append)
        files = [f'{path}/{f.split()[-1]}' for f in flist if f.startswith('-') and (not suffix or f.endswith(suffix))]
        file_list.extend(files)
        # fids=[f.split(None, 4)[-1] for f in flist if f.startswith('-')]
        # dictf=dict(zip(files,fids))
        dirs = [f.split()[-1] for f in flist if f.startswith('d')]
        if len(dirs) > 0:
            for dir in dirs:
                self.list_files(f'{path}/{dir}', file_list, suffix)

    def wc(self, target_file):
        """获取指定文件的总行数

        使用FTP流式读取，直接在网络传输中统计行数，避免下载整个文件

        Args:
            target_file: 目标文件路径

        Returns:
            int: 文件总行数
        """
        try:
            line_count = 0
            buffer_size = 8192  # 8KB 缓冲区

            def count_lines_callback(data):
                """FTP数据回调函数，用于统计行数"""
                nonlocal line_count
                line_count += data.count(b'\n')

            # 切换到二进制模式
            self.client.voidcmd('TYPE I')

            # 使用 retrbinary 进行流式读取，直接在回调中统计行数
            # 这样避免了下载整个文件到本地
            self.client.retrbinary(f'RETR {target_file}', count_lines_callback, buffer_size)

            self.logger.debug(f'File {target_file} has {line_count} lines (counted via FTP stream)')
            return line_count

        except Exception as e:
            self.logger.error(f'Failed to count lines in file {target_file}: {str(e)}')
            raise

    def headn(self, target_file, n=100):
        """获取指定文件的前 n 行

        使用FTP流式读取，只读取需要的前n行数据，避免下载整个文件

        Args:
            target_file: 目标文件路径
            n: 要获取的行数，默认100行

        Returns:
            str: 文件前n行的内容
        """
        try:
            lines = []
            current_line = b''
            lines_read = 0

            def read_lines_callback(data):
                """FTP数据回调函数，用于读取前n行"""
                nonlocal current_line, lines_read, lines

                # 如果已经读取了足够的行数，就停止处理
                if lines_read >= n:
                    return

                # 将新数据添加到当前行缓冲区
                current_line += data

                # 处理缓冲区中的完整行
                while b'\n' in current_line and lines_read < n:
                    line, current_line = current_line.split(b'\n', 1)
                    try:
                        # 尝试解码为UTF-8，如果失败则使用latin-1
                        decoded_line = line.decode('utf-8').rstrip('\r')
                    except UnicodeDecodeError:
                        decoded_line = line.decode('latin-1').rstrip('\r')

                    lines.append(decoded_line)
                    lines_read += 1

            # 切换到二进制模式
            self.client.voidcmd('TYPE I')

            # 使用 retrbinary 进行流式读取
            # 注意：FTP协议无法中途停止传输，但我们可以在回调中控制处理
            try:
                self.client.retrbinary(f'RETR {target_file}', read_lines_callback, 1024)
            except Exception as e:
                # 如果是因为我们已经读取了足够的行数而导致的异常，可以忽略
                if lines_read < n:
                    raise

            # 处理最后一行（如果文件不以换行符结尾）
            if current_line and lines_read < n:
                try:
                    decoded_line = current_line.decode('utf-8').rstrip('\r\n')
                except UnicodeDecodeError:
                    decoded_line = current_line.decode('latin-1').rstrip('\r\n')
                if decoded_line:
                    lines.append(decoded_line)

            result = '\n'.join(lines[:n])  # 确保不超过n行
            self.logger.debug(f'Retrieved first {len(lines)} lines from file {target_file} (via FTP stream)')
            return result

        except Exception as e:
            self.logger.error(f'Failed to read first {n} lines from file {target_file}: {str(e)}')
            raise


class SftpServer(RemoteFileServer):
    def open(self):
        transport = paramiko.Transport((self.ip, self.port))
        transport.connect(username=self.username, password=self.password)
        self.__transport = transport
        self.logger.debug('ssh connected!')
        self.client = paramiko.SFTPClient.from_transport(self.__transport)
        self.client.chdir(self.work_dir)
        self.logger.debug(f'sftp connected! switch to work dir: {self.work_dir}')

    def close(self):
        self.client.close()
        self.logger.debug('sftp connection closed')
        self.__transport.close()
        self.logger.debug('ssh connection closed')

    def exists(self, target_path):
        try:
            self.client.stat(target_path)
            return True
        except IOError as e:
            self.logger.error('target path: %s does not exists on remote sftp server' % target_path)
            return False

    def stat(self, target_path):
        return self.client.stat(target_path)

    def _mkdir_p(self, remote_directory):
        """递归创建远程目录
        
        Args:
            remote_directory: 远程目录路径
        """
        if remote_directory == '/':
            return
        try:
            self.client.stat(remote_directory)
        except IOError:
            parent = os.path.dirname(remote_directory)
            if parent != '/':
                self._mkdir_p(parent)
            try:
                self.client.mkdir(remote_directory)
                self.logger.info(f'Created directory: {remote_directory}')
            except IOError as e:
                if not str(e).startswith('Permission denied') and not str(e).endswith('already exists'):
                    raise

    def upload(self, local_file, target_file):
        """上传文件到SFTP服务器
        
        Args:
            local_file: 本地文件路径
            target_file: 目标文件路径（包含文件名）
        """
        try:
            target_path = '/'.join(target_file.split('/')[:-1])
            if not self.exists(target_path):
                self.logger.info('Creating directory path: %s on sftp server' % target_path)
                self._mkdir_p(target_path)
                
            self.client.put(local_file, target_file, confirm=True)
            self.logger.info('Successfully uploaded local file: %s to sftp server: %s' % (local_file, target_file))
        except Exception as e:
            self.logger.error(f'Failed to upload {local_file} to {target_file}: {str(e)}')
            raise

    def download(self, target_path, local_path):
        self.client.get(target_path, local_path)

    def read(self, target_file):
        remote_file = self.client.open(target_file, 'r')
        content = remote_file.read()
        content = content if isinstance(content, str) else content.decode()
        remote_file.close()
        return content

    def write(self, content, target_file):
        remote_file = self.client.open(target_file, 'w')
        remote_file.write(content)
        remote_file.close()

    # 只能删除文件不能删除路径
    def delete(self, filepath):
        self.client.remove(filepath)

    def list_dir(self, path):
        return self.client.listdir_attr(path)

    def list_dir_info(self, path):
        attrs = self.client.listdir_attr(path)
        return [{'name': attr.filename, 'type': 'dir' if stat.S_ISDIR(attr.st_mode) else 'file'} for attr in attrs]

    def match_files(self, path, file_pattern):
        '''
        匹配指定路径下的文件模式，获取文件列表
        :param path: 指定路径
        :param file_pattern: 文件匹配模式
        :return:
        '''
        files = self.list_dir(path)
        return [f'{path}/{f.filename}' for f in files if fnmatch.fnmatch(f.filename, file_pattern)]

    def cat_head(self, target_file):
        try:
            res = self.client.open(target_file).readline()
        except Exception as e:
            self.logger.error(f'cat {target_file} head error {str(e)}')
            res = None
        return res

    def run_cmd(self, command):
        """
         执行shell命令,返回字典
         return {'color': 'red','res':error}或
         return {'color': 'green', 'res':res}
        :param command:
        :return:
        """
        ssh = paramiko.SSHClient()
        ssh._transport = self.__transport
        # 执行命令
        stdin, stdout, stderr = ssh.exec_command(command)
        # 获取命令结果
        res = strutil.to_str(stdout.read())
        # 获取错误信息
        error = strutil.to_str(stderr.read())
        # 如果有错误信息，返回error
        # 否则返回res
        if error.strip():
            return {'color': 'red', 'res': error}
        else:
            return {'color': 'green', 'res': res}

    def wc(self, target_file):
        """获取指定文件的总行数

        使用SSH命令高效统计行数，避免大文件传输和内存溢出

        Args:
            target_file: 目标文件路径

        Returns:
            int: 文件总行数
        """
        try:
            # 使用 wc -l 命令统计行数，这是最高效的方式
            command = f"wc -l '{target_file}'"
            result = self.run_cmd(command)

            if result['color'] == 'green':
                # wc -l 输出格式: "行数 文件名"
                line_count = int(result['res'].strip().split()[0])
                self.logger.debug(f'File {target_file} has {line_count} lines')
                return line_count
            else:
                # 如果 wc 命令失败，尝试使用流式读取方式
                self.logger.warning(f'wc command failed for {target_file}, falling back to stream reading')
                return self._wc_fallback(target_file)

        except Exception as e:
            self.logger.error(f'Failed to count lines in file {target_file}: {str(e)}')
            raise

    def _wc_fallback(self, target_file):
        """备用的行数统计方法，使用流式读取"""
        try:
            line_count = 0
            buffer_size = 8192  # 8KB 缓冲区

            with self.client.open(target_file, 'rb') as remote_file:
                while True:
                    buffer = remote_file.read(buffer_size)
                    if not buffer:
                        break
                    line_count += buffer.count(b'\n')

            return line_count

        except Exception as e:
            self.logger.error(f'Fallback line counting failed for {target_file}: {str(e)}')
            raise

    def headn(self, target_file, n=100):
        """获取指定文件的前 n 行

        使用SSH命令高效获取前n行，避免大文件传输

        Args:
            target_file: 目标文件路径
            n: 要获取的行数，默认100行

        Returns:
            str: 文件前n行的内容
        """
        try:
            # 使用 head -n 命令获取前n行，这是最高效的方式
            command = f"head -n {n} '{target_file}'"
            result = self.run_cmd(command)

            if result['color'] == 'green':
                content = result['res']
                self.logger.debug(f'Retrieved first {n} lines from file {target_file}')
                return content
            else:
                # 如果 head 命令失败，尝试使用流式读取方式
                self.logger.warning(f'head command failed for {target_file}, falling back to stream reading')
                return self._headn_fallback(target_file, n)

        except Exception as e:
            self.logger.error(f'Failed to read first {n} lines from file {target_file}: {str(e)}')
            raise

    def _headn_fallback(self, target_file, n):
        """备用的前n行读取方法，使用流式读取"""
        try:
            lines = []
            with self.client.open(target_file, 'r') as remote_file:
                for i, line in enumerate(remote_file):
                    if i >= n:
                        break
                    lines.append(line.rstrip('\n\r'))

            return '\n'.join(lines)

        except Exception as e:
            self.logger.error(f'Fallback head reading failed for {target_file}: {str(e)}')
            raise


class WtpServer(RemoteFileServer):
    def __init__(self, ftp_config, logger=None):
        super().__init__(ftp_config, logger)
        self.logger.debug('start create wtp client')
        self.tool_jar_path = SystemConfig.read('WTP_JAR_PATH')
        self.wtp_home_dir = SystemConfig.read('WTP_HOME_DIR')
        self._init_env()

    def open(self):
        self._init_env()

    def close(self):
        pass

    def read(self, target_file):
        pass

    def write(self, content, target_file):
        pass

    def delete(self, filepath):
        pass

    def list_dir(self, path):
        pass

    def list_dir_info(self, path):
        """
        由于WTP协议的特殊性,暂不支持获取目录信息
        返回空列表表示不支持该操作
        
        Args:
            path: 目标路径
            
        Returns:
            list: 空列表,表示不支持该操作
        """
        self.logger.warning('WTP protocol does not support listing directory information')
        return []

    def _init_env(self):
        # 初始化环境变量
        init_cmd = f'cd {self.wtp_home_dir}; . ./setp'
        osutil.call(init_cmd)

    def __exec(self, cmd):
        env = os.environ.copy()
        cmd = f'cd {self.wtp_home_dir}; . ./setp; {cmd}'
        osutil.call(cmd, env=env)

    def upload(self, local_file, target_file):
        cmd = f'java -jar {self.tool_jar_path} "upload" "{local_file}"'
        self.__exec(cmd)

    def download(self, target_path, local_path):
        '''
        wtp下载
        :param target_path: 目标文件名，不带路径
        :param local_path: 本地保存路径，只取目录名
        :return:
        '''
        local_dir = os.path.dirname(local_path)
        downloaded_file = f'{local_dir}/{target_path}'
        cmd = f'java -jar {self.tool_jar_path} "download" "{target_path}" "{local_dir}"'
        self.__exec(cmd)
        if not os.path.exists(downloaded_file):
            # 失败重试，wtp默认覆盖
            self.logger.warning('wtp file [%s] not exists nor download failed, sleep and retry once' % target_path)
            time.sleep(1)
            self.__exec(cmd)
        if os.path.exists(downloaded_file) and downloaded_file != local_path:
            self.logger.debug(f'move wtp origin file {downloaded_file} to {local_path}')
            osutil.mv(downloaded_file, local_path)

    def stat(self, target_path):
        '''
        wtp信息
        :param target_path: flg文件名或数据文件名
        :return:
        '''
        cur_date = timer.now().date
        local_dir = f'{TMP_FOLDER}/wtp/{cur_date}'

        target_path = target_path if target_path.endswith('.flg') else target_path.split('.')[0] + '.flg'
        self.logger.debug(f'get wtp flg file: {target_path}')
        file_name = os.path.basename(target_path)
        if not os.path.exists(local_dir):
            os.makedirs(local_dir)

        local_path = os.path.join(local_dir, file_name)
        self.download(target_path, local_path)
        if os.path.exists(local_path):
            with open(local_path, 'r') as flag_file:
                md5_info = flag_file.readlines()[0]
            return md5_info
        else:
            return None

    def exists(self, target_path):
        return self.stat(target_path) is not None

    def wc(self, target_file):
        """获取指定文件的总行数

        由于WTP协议的特殊性，需要先下载文件到本地再统计行数

        Args:
            target_file: 目标文件路径

        Returns:
            int: 文件总行数
        """
        try:
            # 创建临时目录和文件
            cur_date = timer.now().date
            local_dir = f'{TMP_FOLDER}/wtp/{cur_date}'
            if not os.path.exists(local_dir):
                os.makedirs(local_dir)

            filename = os.path.basename(target_file)
            local_file = os.path.join(local_dir, f'wc_{timer.now().timestamp}_{filename}')

            try:
                # 下载文件到本地
                self.download(target_file, local_file)

                if not os.path.exists(local_file):
                    raise NotFoundException(f'Failed to download file {target_file} for line counting')

                # 使用高效的流式读取统计行数
                line_count = 0
                buffer_size = 8192  # 8KB 缓冲区

                with open(local_file, 'rb') as f:
                    while True:
                        buffer = f.read(buffer_size)
                        if not buffer:
                            break
                        line_count += buffer.count(b'\n')

                self.logger.debug(f'WTP file {target_file} has {line_count} lines')
                return line_count

            finally:
                # 清理临时文件
                if os.path.exists(local_file):
                    os.remove(local_file)

        except Exception as e:
            self.logger.error(f'Failed to count lines in WTP file {target_file}: {str(e)}')
            raise

    def headn(self, target_file, n=100):
        """获取指定文件的前 n 行

        由于WTP协议的特殊性，需要先下载文件到本地再读取前n行

        Args:
            target_file: 目标文件路径
            n: 要获取的行数，默认100行

        Returns:
            str: 文件前n行的内容
        """
        try:
            # 创建临时目录和文件
            cur_date = timer.now().date
            local_dir = f'{TMP_FOLDER}/wtp/{cur_date}'
            if not os.path.exists(local_dir):
                os.makedirs(local_dir)

            filename = os.path.basename(target_file)
            local_file = os.path.join(local_dir, f'head_{timer.now().timestamp}_{filename}')

            try:
                # 下载文件到本地
                self.download(target_file, local_file)

                if not os.path.exists(local_file):
                    raise NotFoundException(f'Failed to download file {target_file} for reading')

                # 读取前n行
                lines = []
                with open(local_file, 'r', encoding='utf-8') as f:
                    for i, line in enumerate(f):
                        if i >= n:
                            break
                        lines.append(line.rstrip('\n\r'))

                result = '\n'.join(lines)
                self.logger.debug(f'Retrieved first {len(lines)} lines from WTP file {target_file}')
                return result

            finally:
                # 清理临时文件
                if os.path.exists(local_file):
                    os.remove(local_file)

        except Exception as e:
            self.logger.error(f'Failed to read first {n} lines from WTP file {target_file}: {str(e)}')
            raise
