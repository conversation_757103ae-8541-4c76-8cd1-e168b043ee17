import mysql.connector
from mysql.connector import Error

from instance.default import UNIX_LINE_ENDING, TMP_FOLDER
from queqiao.conf.system import SystemConfig

from queqiao.util.hadoop.hive import HiveClient


class MySQLClient(HiveClient):
    def __init__(self, host, port, user, password, database, logger=None, download_max=None, fetch_buffer=None):
        super().__init__(logger)
        self._save_path = f'{TMP_FOLDER}/mysqlclient'
        self.download_max = int(SystemConfig.read(
            'TALOS_DOWNLOAD_MAX')) if not download_max else download_max
        self.fetch_buffer = int(SystemConfig.read(
            'TALOS_FETCH_BUFFER')) if not fetch_buffer else fetch_buffer
        self.host = host
        self.port = port
        self.user = user
        self.password = password
        self.database = database
        self.connection = None

    def open(self):
        try:
            self.connection = mysql.connector.connect(
                host=self.host,
                port=self.port,
                user=self.user,
                password=self.password,
                database=self.database
            )
            if self.connection.is_connected():
                print("连接成功")
        except Error as e:
            print(f"连接失败: {e}")

    def close(self):
        if self.connection and self.connection.is_connected():
            self.connection.close()
            print("断开连接")

    def _select_and_download(self, sql, save_file):
        cursor = self.connection.cursor(dictionary=True)
        results = []
        try:
            cursor.execute(sql, None)
            while True:
                batch = cursor.fetchmany(self.fetch_buffer)
                if not batch:
                    break
                results.extend(batch)
        except Error as e:
            print(f"批量获取结果失败: {e}")
        finally:
            cursor.close()
        with open(save_file, 'w') as local_file:
            for row in results:
                row_value = row.values()
                line = self._col_sep.join([str(x) for x in row_value])
                local_file.write(line + UNIX_LINE_ENDING)
            local_file.flush()
        # query_info = {
        #     'status': 0,
        #     'total': len(results),
        #     'columns': '',
        #     'file_path': save_file
        # }
        return {}

    def exec(self, sql, async_=False):
        cursor = self.connection.cursor()
        try:
            cursor.execute(sql, None)
            self.connection.commit()
            print("查询执行成功")
        except Error as e:
            print(f"查询执行失败: {e}")
        finally:
            cursor.close()

    def schema(self, tablename):
        cursor = self.connection.cursor()
        cursor.execute(f"SHOW FULL COLUMNS FROM `{tablename}`")
        columns = cursor.fetchall()
        cursor.close()
        return [{"name": column[0], "type": column[1], "comment": column[8]} for column in columns]

# 使用示例
# if __name__ == "__main__":
#     with MySQLClient(host="localhost", user="root", password="password", database="test_db") as client:
#         client.execute_query("CREATE TABLE IF NOT EXISTS users (id INT AUTO_INCREMENT PRIMARY KEY, name VARCHAR(255))")
#         client.execute_query("INSERT INTO users (name) VALUES (%s)", ("Alice",))
#         results = client.fetch_results("SELECT * FROM users")
#         print(results)
#         batch_results = client.fetch_many_results("SELECT * FROM users", batch_size=2)
#         print(batch_results)

# # 使用示例
# if __name__ == "__main__":
#     client = MySQLClient(host="localhost", user="root", password="password", database="test_db")
#     client.connect()
#     client.execute_query("CREATE TABLE IF NOT EXISTS users (id INT AUTO_INCREMENT PRIMARY KEY, name VARCHAR(255))")
#     client.execute_query("INSERT INTO users (name) VALUES (%s)", ("Alice",))
#     results = client.fetch_results("SELECT * FROM users")
#     print(results)
#     client.disconnect()
