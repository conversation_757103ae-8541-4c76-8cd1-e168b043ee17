"""
Author: xiaohei
Date: 2022/5/5
Email: <EMAIL>
Host: xiaohei.info
"""
import functools
import json

from redis import ConnectionPool, StrictRedis

from queqiao.conf.env import EnvConfig
from queqiao.log import LogFactory

log = LogFactory.get_logger()

redis_host = EnvConfig.get('REDIS_HOST')
redis_port = EnvConfig.get('REDIS_PORT')
redis_pwd = EnvConfig.get('REDIS_PWD')
redis_db = EnvConfig.get('REDIS_DB')
log.debug(f'get redis_host: {redis_host}, redis_port: {redis_port}, '
             f'redis_pwd: {redis_pwd}, redis_db: {redis_db} in env config')
pool = ConnectionPool(decode_responses=True, host=redis_host, port=redis_port, password=redis_pwd, db=redis_db)
conn = StrictRedis(connection_pool=pool)
log.debug(f'create redis connect: {conn} with pool: {pool}')

_redis_cached_connections = {}


def _get_redis_connection_config(key):
    key = f'redis:conn:{key}'
    log.debug(f'get redis connection key: {key}')
    conf = conn.get(key)
    if not conf:
        return None
    # keys:host,port,password,db
    config = json.loads(conf)
    log.debug(f'load connection config: {config}')
    config['connection_pool'] = pool
    return config


class RedisClient:
    @classmethod
    def get_conn(cls, key='config'):
        if key not in _redis_cached_connections:
            config = _get_redis_connection_config(key)
            if not config:
                log.debug(f'[{key}] not found in redis connection cache, use default redis connect')
                redis_conn = conn
            else:
                log.debug(
                    f'found [{key}] in redis connection cache, create a new redis connect with config: {config}')
                redis_conn = StrictRedis(**config)
            _redis_cached_connections[key] = redis_conn
            log.debug(f'set redis connection to local cache, current connections: {_redis_cached_connections}')
        return _redis_cached_connections[key]


def redis_client(key='config'):
    def wrapper(function):
        @functools.wraps(function)
        def call(*args, **kwargs):
            kwargs['redis'] = RedisClient.get_conn(key)
            return function(*args, **kwargs)

        return call

    return wrapper
