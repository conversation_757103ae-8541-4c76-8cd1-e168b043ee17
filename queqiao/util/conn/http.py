"""
Author: xiaohei
Date: 2023/1/13
Email: <EMAIL>
Host: xiaohei.info
"""
import json as js
import os

import requests
from requests_toolbelt import MultipartEncoder
from tqdm import tqdm
from queqiao.conf.errors import HttpRequestError

DOWNLOAD_CHUNK_SIZE = 1024 * 1024


class HttpClient:
    @classmethod
    def download(cls, url, save_path, params=None, json=None):
        response = requests.get(url, stream=True, params=params, json=json)
        if response.status_code != 200:
            raise HttpRequestError(f'{url} 请求失败, 返回码: {response.status_code}, 返回内容: {response.content}')
        file_size = int(response.headers['content-length'])
        with tqdm(total=file_size, unit='B', unit_scale=True, unit_divisor=1024, ascii=True, desc=save_path) as bar:
            with open(save_path, 'wb') as f:
                for chunk in response.iter_content(chunk_size=DOWNLOAD_CHUNK_SIZE):
                    if chunk:
                        f.write(chunk)
                        bar.update(len(chunk))
                f.write(b'\n')

    @classmethod
    def upload(cls, url, file_path, params=None, json=None, with_response=False):
        with open(file_path, 'rb') as fp:
            data = MultipartEncoder(
                fields={'file': (os.path.basename(file_path), fp, 'application/octet-stream')}
            )
            response = requests.post(
                url=url,
                data=data,
                params=params,
                json=json,
                headers={'Content-Type': data.content_type}
            )
            result = response if with_response else js.loads(response.text)
            return result

    @classmethod
    def request(cls, url, params=None, json=None, method='post', with_response=False):
        request_params = {'url': url, 'params': params, 'json': json}
        request_method = requests.post if method == 'post' else requests.get
        response = request_method(**request_params)
        result = response if with_response else js.loads(response.text)
        return result
