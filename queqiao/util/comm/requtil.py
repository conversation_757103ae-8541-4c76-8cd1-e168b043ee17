"""
Author: xiaohei
Date: 2022/4/21
Email: <EMAIL>
Host: xiaohei.info
"""
from functools import wraps

from flask import request

from queqiao.conf.ApiResponse import MISSING_PARAMS


def check_json_request_params(req, checklist):
    err_params = []
    for v in checklist:
        if v not in req.json or req.json[v] is None or len(req.json[v]) < 1:
            err_params.append(v)
    return err_params


def check_post_request_params(req, checklist):
    err_params = []
    for v in checklist:
        # logger.info(f'check param {v}, value is {request.form[v]} len is {len(request.form[v])}')
        if v not in req.form or req.form[v] is None or len(req.form[v]) < 1:
            err_params.append(v)
    return err_params


def check_get_request_params(request, checklist):
    err_params = []
    for v in checklist:
        # logger.info(f'check param {v}, value is {request.form[v]} len is {len(request.form[v])}')
        if v not in request.values or request.values[v] is None or len(request.values[v]) < 1:
            err_params.append(v)
    return err_params


def get_request_param(req, key, default=None):
    if req.json and req.json.get(key) is not None:
        return req.json.get(key, default)
    elif req.form and req.form.get(key) is not None:
        return req.form.get(key, default)
    elif req.args and req.args.get(key) is not None:
        return req.args.get(key, default)
    elif req.files and req.files.get(key) is not None:
        return request.files[key]
    else:
        return default


def params_required(*keys):
    def wrapper(f):
        @wraps(f)
        def call(*args, **kwargs):
            print(f'keys: {keys}, args: {args}, kwargs: {kwargs}')
            params = {}
            for key in keys:
                value = get_request_param(request, key)
                if value is not None:
                    params[key] = value
            passed = len(params) == len(keys)
            if not passed:
                miss_keys = set(keys).difference(params.keys())
                return MISSING_PARAMS(detail=','.join(miss_keys))
            kwargs.update(params)
            return f(*args, **kwargs)

        return call

    return wrapper


def params_optional(*keys):
    def wrapper(f):
        @wraps(f)
        def call(*args, **kwargs):
            params = {}
            for key in keys:
                value = get_request_param(request, key)
                if value is not None:
                    params[key] = value
            kwargs.update(params)
            return f(*args, **kwargs)

        return call

    return wrapper


def params_optional_none(*keys):
    def wrapper(f):
        @wraps(f)
        def call(*args, **kwargs):
            params = {}
            for key in keys:
                params[key] = get_request_param(request, key)
            kwargs.update(params)
            return f(*args, **kwargs)

        return call

    return wrapper
