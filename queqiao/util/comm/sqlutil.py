# coding=utf-8
"""
Author: xiaohei
Date: 2021/3/8
Email: <EMAIL>
Host: xiaohei.info
"""
import re

import sqlparse
from moz_sql_parser import parse
from sqlparse.sql import Identifier, IdentifierList
from sqlparse.tokens import Keyword, Name

RESULT_OPERATIONS = {'UNION', 'INTERSECT', 'EXCEPT', 'SELECT'}
ON_KEYWORD = 'ON'
PRECEDES_TABLE_NAME = {'FROM', 'JOIN', 'DESC', 'DESCRIBE', 'WITH'}


def clean_sql_comment(sql):
    # remove the /* */ comments
    q = re.sub(r"/\*[^*]*\*+(?:[^*/][^*]*\*+)*/", "", sql)
    # remove whole line -- and # comments
    lines = [line for line in q.splitlines() if not re.match("^\s*(--|#)", line)]
    # remove trailing -- and # comments
    q = " ".join([re.split("--|#", line)[0] for line in lines])
    q = ' '.join(q.split())
    return q


def get_tables(sql):
    extractor = SqlExtractor(clean_sql_comment(sql))
    return extractor.tables


def get_select_cols(sql):
    tree = parse(clean_sql_comment(sql))
    select_node = tree['select']
    if isinstance(select_node, dict):
        cols = [select_node['value']]
    elif isinstance(select_node, str):
        cols = [select_node]
    else:
        cols = [i if isinstance(i, str) else (i['name'] if 'name' in i else i['value']) for i in tree['select']]
    cols = [c.split('.')[-1] if '.' in c else c for c in cols]
    return cols


class SqlExtractor:
    def __init__(self, sql_statement):
        self.sql = sqlparse.format(sql_statement, reindent=True, keyword_case='upper')
        self._table_names = set()
        self._alias_names = set()
        self._limit = None
        self._parsed = sqlparse.parse(self.stripped())
        for statement in self._parsed:
            self.__extract_from_token(statement)
            self._limit = self._extract_limit_from_query(statement)
        self._table_names = self._table_names - self._alias_names

    @property
    def tables(self):
        return self._table_names

    @property
    def limit(self):
        return self._limit

    def is_select(self):
        return self._parsed[0].get_type() == 'SELECT'

    def is_explain(self):
        return self.stripped().upper().startswith('EXPLAIN')

    def is_readonly(self):
        return self.is_select() or self.is_explain()

    def stripped(self):
        return self.sql.strip(' \t\n;')

    def get_statements(self):
        statements = []
        for statement in self._parsed:
            if statement:
                sql = str(statement).strip(' \n;\t')
                if sql:
                    statements.append(sql)
        return statements

    @staticmethod
    def __precedes_table_name(token_value):
        for keyword in PRECEDES_TABLE_NAME:
            if keyword in token_value:
                return True
        return False

    @staticmethod
    def get_full_name(identifier):
        if len(identifier.tokens) > 1 and identifier.tokens[1].value == '.':
            return '{}.{}'.format(identifier.tokens[0].value,
                                  identifier.tokens[2].value)
        return identifier.get_real_name()

    @staticmethod
    def __is_result_operation(keyword):
        for operation in RESULT_OPERATIONS:
            if operation in keyword.upper():
                return True
        return False

    @staticmethod
    def __is_identifier(token):
        return isinstance(token, (IdentifierList, Identifier))

    def __process_identifier(self, identifier):
        if '(' not in '{}'.format(identifier):
            self._table_names.add(self.get_full_name(identifier))
            return

        # store aliases
        if hasattr(identifier, 'get_alias'):
            self._alias_names.add(identifier.get_alias())
        if hasattr(identifier, 'tokens'):
            # some aliases are not parsed properly
            if identifier.tokens[0].ttype == Name:
                self._alias_names.add(identifier.tokens[0].value)
        self.__extract_from_token(identifier)

    def as_create_table(self, table_name, overwrite=False):
        exec_sql = ''
        sql = self.stripped()
        if overwrite:
            exec_sql = 'DROP TABLE IF EXISTS {};\n'.format(table_name)
        exec_sql += 'CREATE TABLE {} AS \n{}'.format(table_name, sql)
        return exec_sql

    def __extract_from_token(self, token):
        if not hasattr(token, 'tokens'):
            return

        table_name_preceding_token = False

        for item in token.tokens:
            if item.is_group and not self.__is_identifier(item):
                self.__extract_from_token(item)

            if item.ttype in Keyword:
                if self.__precedes_table_name(item.value.upper()):
                    table_name_preceding_token = True
                    continue

            if not table_name_preceding_token:
                continue

            if item.ttype in Keyword or item.value == ',':
                if (self.__is_result_operation(item.value) or
                        item.value.upper() == ON_KEYWORD):
                    table_name_preceding_token = False
                    continue
                # FROM clause is over
                break

            if isinstance(item, Identifier):
                self.__process_identifier(item)

            if isinstance(item, IdentifierList):
                for token in item.tokens:
                    if self.__is_identifier(token):
                        self.__process_identifier(token)

    def _get_limit_from_token(self, token):
        print(f'token.ttype: {token.ttype}, ')
        if token.ttype == sqlparse.tokens.Literal.Number.Integer:
            return int(token.value)
        elif token.is_group:
            try:
                return int(token.get_token_at_offset(1).value)
            except Exception as _:
                return 0

    def _extract_limit_from_query(self, statement):
        limit_token = None
        for pos, item in enumerate(statement.tokens):
            if item.ttype in Keyword and item.value.lower() == 'limit':
                limit_token = statement.tokens[pos + 2]
                print(f'limit_token: {limit_token}, type: {type(limit_token)}')
                return self._get_limit_from_token(limit_token)

    def get_query_with_new_limit(self, new_limit):
        if not self._limit:
            return self.sql + ' LIMIT ' + str(new_limit)
        limit_pos = None
        tokens = self._parsed[0].tokens
        # Add all items to before_str until there is a limit
        for pos, item in enumerate(tokens):
            if item.ttype in Keyword and item.value.lower() == 'limit':
                limit_pos = pos
                break
        limit = tokens[limit_pos + 2]
        if limit.ttype == sqlparse.tokens.Literal.Number.Integer:
            tokens[limit_pos + 2].value = new_limit
        elif limit.is_group:
            tokens[limit_pos + 2].value = (
                '{}, {}'.format(next(limit.get_identifiers()), new_limit)
            )

        str_res = ''
        for i in tokens:
            str_res += str(i.value)
        return str_res


def convert_mysql_to_hive_type(mysql_type):
    """
    将MySQL字段类型转换为Hive字段类型
    :param mysql_type: MySQL字段类型，可能包含长度限制，例如 'varchar(32)'
    :return: 对应的Hive字段类型
    """
    # 去除可能的括号和数字，只保留基本类型和长度
    parts = mysql_type.strip().split('(')
    base_type = parts[0].lower()

    # 映射基本类型
    hive_type = {
        'bigint': 'BIGINT',
        'int': 'INT',
        'smallint': 'SMALLINT',
        'tinyint': 'TINYINT',
        'decimal': 'DECIMAL',
        'double': 'DOUBLE',
        'float': 'FLOAT',
        'binary': 'BINARY',
        'varbinary': 'BINARY',
        'char': 'STRING',
        'varchar': 'STRING',  # Hive中没有VARCHAR，统一转换为STRING
        'mediumtext': 'STRING',
        'text': 'STRING',
        'datetime': 'TIMESTAMP',
        'time': 'STRING',
        'timestamp': 'TIMESTAMP',
        'date': 'DATE',
        'json': 'STRING',  # Hive中没有直接对应的JSON类型，通常使用STRING存储JSON格式的文本
    }.get(base_type, 'STRING')  # 默认转换为STRING

    return hive_type


if __name__ == '__main__':
    mysql_types = ['bigint', 'int', 'smallint', 'tinyint', 'decimal(10,2)', 'double', 'float', 'binary',
                   'varbinary',
                   'char(10)', 'varchar(32)', 'mediumtext', 'text', 'datetime', 'time', 'timestamp', 'date', 'json']

    for mysql_type in mysql_types:
        hive_type = convert_mysql_to_hive_type(mysql_type)
        print(f"MySQL Type: {mysql_type} -> Hive Type: {hive_type}")
