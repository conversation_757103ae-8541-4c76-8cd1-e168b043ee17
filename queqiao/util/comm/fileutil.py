"""
Author: xiaohei
Date: 2021/9/28
Email: <EMAIL>
Host: xiaohei.info
"""
import csv
import os
import platform
import tarfile

import pandas as pd
from werkzeug.utils import secure_filename

from queqiao.conf.enums import CompressType
from queqiao.conf.errors import NotFoundException
from queqiao.log import LogFactory
from queqiao.util.comm import osutil, strutil

log = LogFactory.get_logger()


def parse_excel(excel_path):
    res = []
    df = pd.read_excel(excel_path, header=0).fillna('')
    header = df.columns.tolist()
    for value in df.values:
        obj = {}
        row = value.tolist()
        for col in header:
            col_idx = header.index(col)
            # 值为空的列去除
            if row[col_idx] != '':
                obj[col] = row[col_idx]
        res.append(obj)
    return res


def save_to_local(request_file, save_path):
    filename = request_file.filename.split("/")[-1]
    filename = secure_filename(filename)
    local_path = os.path.join(save_path, filename)
    request_file.save(local_path)
    return local_path


def compress(origin_file, compress_file, compress_type, compress_passwd=None):
    if not compress_type or compress_type == CompressType.NONE.value:
        return origin_file

    compress_type = CompressType(compress_type)
    if not isinstance(origin_file, list):
        file_name = origin_file.split('/')[-1]
        save_path = origin_file.replace(f'/{file_name}', '')
    else:
        file_name = ' '.join([file_path.split('/')[-1] for file_path in origin_file])
        save_path = os.path.dirname(origin_file[0])
    # 带路径的信息
    if '/' in compress_file:
        compress_path = '/'.join(compress_file.split('/')[:-1])
        compress_file = compress_file.split('/')[-1]
    else:
        compress_path = save_path
        compress_file = compress_file
    log.debug(f'get save_path: {save_path}, file_name: {file_name}')

    if compress_type == CompressType.ZIP:
        zip_cmd = 'zip' if not compress_passwd else f'zip -P {compress_passwd}'
        compress_cmd = f'cd {save_path} && {zip_cmd} {compress_file} {file_name}'
    elif compress_type in [CompressType.GZ, CompressType.GZIP]:
        # 会删除源文件
        # compress_cmd = f'gzip {normal_filename}'
        compress_cmd = f'cd {save_path} && gzip -c {file_name} > {compress_file}'
    elif compress_type == CompressType.TARGZ:
        compress_cmd = f'cd {save_path} && tar -czvf {compress_file} {file_name}'
    elif compress_type == CompressType.Z7:
        compress_cmd = f'cd {save_path} && 7za a {compress_file} {file_name}'
        compress_cmd = compress_cmd if not compress_passwd else f'{compress_cmd} -p{compress_passwd}'
    else:
        compress_cmd = None
    log.debug(f'compress cmd: {compress_cmd}')
    ret = osutil.call(compress_cmd)
    if ret > 0:
        raise Exception('compress failed')
    if compress_path != save_path:
        osutil.mv(f'{save_path}/{compress_file}', f'{compress_path}/{compress_file}')
    return f'{compress_path}/{compress_file}'


def decompress(target_file, compress_type, compress_passwd=None, compress_merge_suffix=None):
    compress_type = CompressType(compress_type)
    if compress_type == CompressType.NONE:
        return target_file

    file_name = target_file.split('/')[-1]
    save_path = target_file.replace(f'/{file_name}', '')
    log.debug(f'get save_path: {save_path}, file_name: {file_name}')

    uid = strutil.uid(rung=True)
    decompress_tmp_path = f'{save_path}/{uid}'
    if osutil.exists(decompress_tmp_path):
        osutil.rm(decompress_tmp_path)
    osutil.mkdir(decompress_tmp_path)
    log.debug(f'create decompress tmp path: {decompress_tmp_path}')
    osutil.mv(target_file, f'{decompress_tmp_path}/{file_name}')
    log.debug(f'move {target_file} to {decompress_tmp_path}/{file_name}')

    if compress_type == CompressType.ZIP:
        if compress_passwd:
            decompress_cmd = f'cd {decompress_tmp_path} && unzip -P \'{compress_passwd}\' {file_name}'
        else:
            decompress_cmd = f'cd {decompress_tmp_path} && unzip {file_name}'
    elif compress_type == CompressType.TARGZ:
        decompress_cmd = f'cd {decompress_tmp_path} && tar -zxvf {file_name}'
    elif compress_type in [CompressType.GZ, CompressType.GZIP]:
        decompress_cmd = f'cd {decompress_tmp_path} && gunzip -c {file_name} > {file_name.split(".")[0]}.txt'
    elif compress_type == CompressType.Z7:
        if compress_passwd:
            decompress_cmd = f'cd {decompress_tmp_path} && 7za x {file_name} -p\'{compress_passwd}\''
        else:
            decompress_cmd = f'cd {decompress_tmp_path} && 7za x {file_name}'
    else:
        raise Exception(f'unkown compress type: {compress_type}')

    log.debug(f'get decompress cmd: {decompress_cmd}')
    ret = osutil.call(decompress_cmd)
    if ret > 0:
        raise Exception('decompress failed')

    # 压缩文件恢复原位
    osutil.mv(f'{decompress_tmp_path}/{file_name}', target_file)
    log.debug(f'restore compress file: {target_file} from {decompress_tmp_path}/{file_name}')
    files = osutil.list_files(decompress_tmp_path)
    log.debug(f'get {len(files)} decompress files from {decompress_tmp_path}')
    if len(files) < 1:
        raise NotFoundException(f'can not found any decompress file in {decompress_tmp_path}')
    elif len(files) == 1:
        decompress_file = files[0]
    else:
        # merge 流程
        if compress_merge_suffix:
            matched_file = next((f for f in files if f.endswith(compress_merge_suffix)), None)
            if not matched_file:
                raise Exception(f'can not found any decompress file in {decompress_tmp_path} with suffix: {compress_merge_suffix}')
            merged_file_name = f'{matched_file.split("/")[-1].split(".")[0]}.txt.merged'
            merged_cmd = f'cat {decompress_tmp_path}/*{compress_merge_suffix} > {decompress_tmp_path}/{merged_file_name}'
        else:
            merged_file_name = f'{file_name.split(".")[0]}.txt.merged'
            merged_cmd = f'cat {decompress_tmp_path}/* > {decompress_tmp_path}/{merged_file_name}'
        log.debug(f'will merge {len(files)} files in {merged_file_name} with merge cmd: {merged_cmd}')
        osutil.call(merged_cmd)
        decompress_file = f'{decompress_tmp_path}/{merged_file_name}'
    mv_decompress_file = f'{save_path}/{decompress_file.split("/")[-1].replace(".merged", "")}'
    osutil.mv(f'{decompress_file}', mv_decompress_file)
    log.debug(f'move {decompress_file} to final decompress file: {mv_decompress_file}')
    osutil.rm(decompress_tmp_path)
    log.debug(f'clean decompress tmp path: {decompress_tmp_path}')

    return mv_decompress_file


def reset_sep(file_path, curr_sep, target_sep='\x01'):
    curr_sep = curr_sep if curr_sep != 'special' else '\x01'
    convert_sep = osutil.convert_special_chars(curr_sep)
    sed_cmd = "sed -i " if platform.system() == 'Linux' else "sed -i ''"
    reset_sep_cmd = f"{sed_cmd} 's/{convert_sep}/{target_sep}/g' {file_path}"
    log.debug(f'reset_sep cmd: {reset_sep_cmd}')
    return osutil.call(reset_sep_cmd)


def reset_row_sep(file_path, row_sep_from, row_sep_to):
    output_file = f"{file_path}.tmp"
    with open(file_path, 'r') as infile, open(output_file, 'w') as outfile:
        for line in infile:
            # 替换每行中的字符串
            replaced_line = line.replace(row_sep_from, row_sep_to)
            # 写入修改后的行内容
            outfile.write(replaced_line)
    osutil.rm(file_path)
    osutil.mv(output_file, file_path)
    return file_path


def reload_sep(file_path, curr_sep, target_sep='\x01'):
    in_path = file_path
    out_path = f'{in_path}.reset'
    log.debug(
        f'rewrite target_sep from {curr_sep} to {target_sep}, in_path: {in_path}, out_path: {out_path}')
    with open(in_path, 'r') as infile, open(out_path, 'w') as outfile:
        reader = csv.reader(infile, delimiter=curr_sep)
        writer = csv.writer(outfile, delimiter=target_sep)
        for row in reader:
            writer.writerow(row)
    osutil.rm(in_path)
    osutil.mv(out_path, in_path)


def reset_encode(file_path, curr_encode, target_encode='utf-8'):
    tmp_file = file_path + '_tmp'
    osutil.mv(file_path, tmp_file)
    reset_encode_cmd = f'iconv -f {curr_encode} -t {target_encode} {tmp_file} -o {file_path}'
    log.debug(f'reset encode cmd: {reset_encode_cmd}')
    ret = osutil.call(reset_encode_cmd)
    osutil.rm(tmp_file)
    return ret


def get_filename_suffix(filename):
    if filename.endswith('.tar.gz'):
        return '.tar.gz'
    else:
        return f".{filename.split('.')[-1]}"


def get_meta(filepath, target_sep, coln_idx=-1, colv_idx=-1):
    last_col_distinct_length_list = []
    validate_col_distinct_length_list = []

    with open(filepath, 'r') as f:
        line = f.readline()
        cols = line.split(target_sep)
        first_col_max = first_col_min = cols[0]
        last_col_max = last_col_min = cols[coln_idx] if coln_idx < len(cols) else None

        cnt = 0
        while line:
            cnt += 1
            cols = line.split(target_sep)
            first_col = cols[0]
            last_col = cols[coln_idx] if coln_idx < len(cols) else None
            validate_col = cols[colv_idx] if colv_idx < len(cols) else None

            if first_col > first_col_max:
                first_col_max = first_col
            if first_col < first_col_min:
                first_col_min = first_col
            if last_col and last_col > last_col_max:
                last_col_max = last_col
            if last_col and last_col < last_col_min:
                last_col_min = last_col

            if last_col and len(last_col) not in last_col_distinct_length_list:
                last_col_distinct_length_list.append(len(last_col))
            if validate_col and len(validate_col) not in validate_col_distinct_length_list:
                validate_col_distinct_length_list.append(len(validate_col))
            line = f.readline()
        int_meta = {
            'row_num': cnt,
            'coln_len_num': len(last_col_distinct_length_list),
            'colv_len_num': len(validate_col_distinct_length_list)
        }
        meta = {
            'col1_max': first_col_max,
            'col1_min': first_col_min,
            'coln_max': last_col_max,
            'coln_min': last_col_min,
        }
        for k in meta.keys():
            if meta[k] is not None and not isinstance(meta[k], list) and meta[k] != 'NULL':
                meta[k] = str(meta[k]).strip()
            else:
                # None/NULL统一转成空字符串
                meta[k] = ''
        meta.update(int_meta)
        return meta


def get_meta_simple(filepath):
    return {
        'col1_max': "",
        'col1_min': "",
        'coln_max': "",
        'coln_min': "",
        'colv_len_num': 0,
        'coln_len_num': 0,
        'row_num': osutil.wc(filepath)
    }


def untar(file_path):
    tar = tarfile.open(file_path, "r:*")
    file_names = tar.getnames()
    for file_name in file_names:
        tar.extract(file_name)
    tar.close()


def get_distinct_values(file_path, target_sep, col_idx):
    distinct_values = set()
    with open(file_path, 'r') as f:
        line = f.readline()
        while line:
            cols = line.split(target_sep)
            distinct_values.add(cols[col_idx].strip())
            line = f.readline()
    return distinct_values
