# coding=utf-8
from crontab import CronTab


class CrontabManager(object):

    def __init__(self, user, logger):
        self.__cron = CronTab(user=user)
        self.logger = logger

    def add_job(self, cmd, cron, comment):
        try:
            self.logger.info(
                f'create new cron task with command {cmd} and set cron to {cron}, comment is {comment}')
            job = self.__cron.new(command=cmd)
            job.setall(cron)
            job.set_comment(comment)
            self.__cron.write()
            self.logger.info('new cron task saved')
        except Exception as why:
            raise Exception(
                f'error crontab expr: {cron}, {str(why)}')

    def update_job(self, cmd, cron, comment):
        cron_finish_set = False
        for curr_task in self.__cron:
            if curr_task.comment == comment:
                self.logger.info(f'find already exists crontab with comment {comment}')
                try:
                    self.logger.info('set command from %s to %s' % (curr_task.command, cmd))
                    self.logger.info('set cron to %s' % cron)
                    curr_task.command = cmd
                    curr_task.setall(cron)
                    self.__cron.write()
                    self.logger.info('cron task updated')
                    cron_finish_set = True
                    break
                except Exception as why:
                    raise Exception('error crontab expr: %s, %s' % (cron, str(why)))
        return cron_finish_set

    def del_job(self, comment):
        # jobs = self.cron.find_comment(commont_name)
        # self.cron.crons
        # 按comment清除多个定时任务，一次write即可
        cnt = self.__cron.remove_all(comment=comment)
        self.__cron.write()
        self.logger.info(f'success remove {cnt} crontab jobs with comment: {comment}')
        return cnt
