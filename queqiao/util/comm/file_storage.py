import os
import shutil
from abc import ABCMeta, abstractmethod


class BaseStorage:
    __metaclass__ = ABCMeta

    @abstractmethod
    def __init__(self, config):
        pass

    @abstractmethod
    def read(self, path):
        pass

    @abstractmethod
    def readlines(self, path):
        pass

    @abstractmethod
    def write(self, path, payload):
        pass

    @abstractmethod
    def remove(self, path):
        pass

    @abstractmethod
    def expose(self, path, expire):
        pass

    @abstractmethod
    def upload(self, local_path, path, extra_args):
        pass

    @abstractmethod
    def exists(self, path):
        pass


# class S3Storage(BaseStorage):
#
#     def __init__(self, config):
#         aws_session = Session(
#             config['aws_access_key_id'],
#             config['aws_secret_access_key'],
#         )
#         endpoint_url = config.get('endpoint_url')
#         self._s3 = aws_session.resource('s3', endpoint_url=endpoint_url)
#         self._s3_client = aws_session.client('s3', endpoint_url=endpoint_url)
#
#     def read(self, path):
#         bucket, key = self._parse_path(path)
#         obj = self._s3.Object(bucket, key).get()
#         return obj['Body'].read()
#
#     def readlines(self, path):
#         bucket, key = self._parse_path(path)
#         obj = self._s3.Object(bucket, key).get()
#         return obj['Body'].iter_lines()
#
#     def remove(self, path):
#         bucket, key = self._parse_path(path)
#         self._s3_client.delete_object(Bucket=bucket, Key=key)
#
#     def write(self, path, data):
#         bucket, key = self._parse_path(path)
#         self._s3.Object(bucket, key).put(Body=data)
#
#     def expose(self, path, expire):
#         bucket, key = self._parse_path(path)
#         signed_url = self._s3_client.generate_presigned_url(
#             'get_object',
#             Params=dict(Bucket=bucket, Key=key),
#             ExpiresIn=expire,
#         )
#         return signed_url
#
#     def upload(self, local_path, path, extra_args):
#         bucket, key = self._parse_path(path)
#         self._s3_client.upload_file(local_path, bucket, key, ExtraArgs=extra_args)
#
#     def exists(self, path):
#         bucket, key = self._parse_path(path)
#         res = self._s3_client.list_objects_v2(Bucket=bucket, Prefix=key, MaxKeys=1)
#         return 'Contents' in res
#
#     @staticmethod
#     def _parse_path(path):
#         parts = path.lstrip('/').split('/')
#         return parts[0], posixpath.join(*parts[1:]) if len(parts) > 1 else ''


class LocalStorage(BaseStorage):

    def __init__(self, config):
        self.config = config

    def read(self, path):
        with open(path, 'rb') as f:
            return f.read()

    def readlines(self, path):
        with open(path, 'rb') as f:
            return f.readlines()

    def remove(self, path):
        os.remove(path)

    def write(self, path, payload):
        basedir = os.path.dirname(path)
        if basedir and not os.path.exists(basedir):
            os.makedirs(basedir)
        with open(path, 'w') as f:
            f.write(payload)

    def expose(self, path, expire):
        return path

    def upload(self, local_path, path, extra_args):
        basedir = os.path.dirname(path)
        if basedir and not os.path.exists(basedir):
            os.makedirs(basedir)
        shutil.copyfile(local_path, path)

    def exists(self, path):
        return os.path.exists(path)


class FileStorageFactory:

    # def __init__(self, type, config):
    #     self.type = type
    #     self.config = config

    @classmethod
    def new_instance(cls, config):
        # if self.type == 's3':
        #     Storage= S3Storage
        # else:
        #     Storage = LocalStorage
        Storage = LocalStorage
        storage = Storage(config)
        return storage
