# coding=utf-8

import calendar
import time
from datetime import datetime, timedelta


# 使用参数参考：https://km.sankuai.com/page/66450191
class timer:

    def __init__(self, dt):
        self._dt = dt

    @classmethod
    def time_diff(cls, s1, s2):
        from datetime import datetime
        time1 = datetime.strptime(s1, "%Y-%m-%d %H:%M:%S")
        time2 = datetime.strptime(s2, "%Y-%m-%d %H:%M:%S")
        return (time1 - time2).seconds

    @classmethod
    def now(cls):
        """Like datetime.now, except microsecond
        """
        return cls(datetime.fromtimestamp(int(time.time())))

    @classmethod
    def fromts(cls, ts):
        return cls(datetime.fromtimestamp(ts))

    @classmethod
    def fromdt(cls, dt):
        if isinstance(dt, str):
            dt = dt if ' ' in dt else f'{dt} 00:00:00'
            dt = dt if '-' not in dt else dt.replace('-', '')
            dt = dt if '.' not in dt else dt.split('.')[0]
            ts = time.mktime(time.strptime(dt, '%Y%m%d %H:%M:%S'))
            return cls.fromts(ts)
        elif isinstance(dt, datetime):
            return cls(dt)
        else:
            raise Exception(f'dt type must in [str,datetime]')

    def format(self, format):
        return self._dt.strftime(format)

    def strftime(self, format):
        return self._dt.strftime(format)

    @property
    def date(self):
        return self._dt.strftime("%Y-%m-%d")

    @property
    def time(self):
        return self._dt.strftime("%H:%M:%S")

    @property
    def datetime(self):
        return self._dt.strftime("%Y-%m-%d %H:%M:%S")

    @property
    def datekey(self):
        return self._dt.strftime("%Y%m%d")

    @property
    def hourmin(self):
        return self._dt.strftime("%H%M")

    @property
    def hour(self):
        return self._dt.hour

    @property
    def minute(self):
        return self._dt.minute

    @property
    def second(self):
        return self._dt.second

    @property
    def year(self):
        return self._dt.year

    @property
    def month(self):
        return self._dt.month

    @property
    def yearmo(self):
        return self._dt.strftime("%Y%m")

    @property
    def year_mo(self):
        return self._dt.strftime("%Y-%m")

    @property
    def day(self):
        return self._dt.day

    @property
    def zmonth(self):
        return str(self._dt.month).zfill(2)

    @property
    def zday(self):
        return str(self._dt.day).zfill(2)

    @property
    def zhour(self):
        return str(self._dt.hour).zfill(2)

    @property
    def datestamp(self):
        """ >>> datetime.datetime(1970, 1, 2, 16, 0).toordinal()
        719164

        days = dt.toordinal() - 719164
        return days * 86400 + 57600
        """
        return (self._dt.toordinal() - 719164) * 86400 + 57600

    @property
    def timestamp(self):
        dt = self._dt
        # Or: int(time.mktime(dt.timetuple()))
        return (dt.toordinal() - 719164) * 86400 + 57600 + dt.hour * 3600 + dt.minute * 60 + dt.second

    @property
    def time_to_sec(self):
        # return (int(time.time()) - 57600) % 86400
        pass

    def delta(self, *args, **kwargs):
        newdate = self._dt
        month = newdate.month - int(kwargs.pop('months', 0))
        year = int(int(kwargs.pop('years', 0)) - month / 12 + (month % 12 == 0))
        month = month % 12 + (month % 12 == 0) * 12
        year = newdate.year - year

        day = min(newdate.day, calendar.monthrange(year, month)[1])
        newdate = newdate.replace(year=year, month=month, day=day)

        return self.__class__(newdate - timedelta(*args, **kwargs))

    @property
    def month_begin_date(self):
        return self.__class__(self._dt.replace(day=1))

    @property
    def month_end_date(self):
        end_date = calendar.monthrange(self._dt.year, self._dt.month)[1]
        return self.__class__(self._dt.replace(day=end_date))

    @property
    def week_begin_date(self):
        return self.__class__(self._dt - timedelta(days=self._dt.weekday()))

    @property
    def week_end_date(self):
        return self.__class__(self._dt + timedelta(days=7 - self._dt.weekday() - 1))

    @property
    def isoweekday(self):
        return self._dt.isoweekday()

    def delta_old(self, *args, **kwargs):
        return self.__class__(self._dt - timedelta(*args, **kwargs))

    def __sub__(self, start_date):
        return self._dt - start_date._dt

    def __repr__(self):
        return self.datetime
