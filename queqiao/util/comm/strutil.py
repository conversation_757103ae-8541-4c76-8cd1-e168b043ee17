# coding=utf-8

"""
Author: xiaohei
Date: 2020-10-17
Email: <EMAIL>
Host: xiaohei.info
"""
import hashlib
import re
import uuid

from Cheetah.Template import Template
from pyhocon import ConfigFactory
from pypinyin import lazy_pinyin


def to_str(bytes_or_str):
    """
    把byte类型转换为str
    :param bytes_or_str:
    :return:
    """
    if isinstance(bytes_or_str, bytes):
        value = bytes_or_str.decode('utf-8')
    else:
        value = bytes_or_str
    return value


def md5(s):
    return hashlib.md5(s.encode('UTF-8')).hexdigest()


def md5_file(file_path):
    m = hashlib.md5()
    with open(file_path, 'rb') as fobj:
        while True:
            data = fobj.read(4096)
            if not data:
                break
            m.update(data)
    return m.hexdigest()


def is_null(s):
    if s is None or len(s) == 0:
        return True
    else:
        return False


def hocon_dumps(dct):
    out = [f'{k}:{v}' for k, v in zip(dct.keys(), dct.values())]
    return ','.join(out)


def hocon_loads(s):
    try:
        r = ConfigFactory.parse_string(s)
    except Exception as why:
        print(why)
        r = {}
    return r


def uid(rung=False):
    u = str(uuid.uuid1())
    if rung:
        u = u.replace('-', '')
    return u


def render_vars(source, **params):
    # doc: https://cheetahtemplate.org/
    return Template(source, params).respond()


def find_date(s, only_idx=None):
    result = re.findall("\d{4}[-|.|/]?\d{2}[-|.|/]?\d{2}", s)
    return result if only_idx is None else (result[only_idx] if len(result) != 0 and len(result) > only_idx else None)


def hanzi2pinyin(s):
    res = lazy_pinyin(s)
    return ''.join(res)


def check_special_chars_in_str(o_str, replace_to):
    special_chars = ".-()$#@&*+;'\"./\\|?!`"
    for char in special_chars:
        o_str = o_str.replace(char, replace_to)
    return o_str
