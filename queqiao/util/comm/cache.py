"""
Author: xiaohei
Date: 2022/7/8
Email: <EMAIL>
Host: xiaohei.info
"""
import hashlib
from time import time

from queqiao.log import LogFactory

log = LogFactory.get_logger()
_local_caches = {}


class FromLocalCache:
    def __init__(self, val):
        self.val = f'{val} with local cache'

    def __str__(self):
        return self.val


def local_cache(expire=7200, key="", maxlen=100000, start=0, test=False):
    def wrapper(func):
        def mem_wrapped_func(*args, **kwargs):
            now = time()
            if key:
                start_key = key
            else:
                start_key = repr(func)
            k = _key_gen(start_key, *args[start:], **kwargs)
            log.debug(f'get cache key: {k}')

            value = _local_caches.get(k, None)
            if _valid_cache(value, now):
                val = value["value"] if not test else FromLocalCache(value["value"])
            else:
                log.debug('rerun origin func')
                val = func(*args, **kwargs)
                assert len(str(val)) <= maxlen
                _local_caches[k] = {"value": val, "expire": now + expire}
                log.debug(f'set value [{val}] in local cache')
            return val

        return mem_wrapped_func

    return wrapper


def clean_local_cache():
    global _local_caches
    count = len(_local_caches)
    _local_caches = {}
    return count


def _key_gen(key, *args, **kwargs):
    log.debug(f'generate cache key with key: {key}, *args:{args}, **kwargs: {kwargs}')
    code = hashlib.md5()

    code.update(str(key).encode('utf-8'))
    code.update("".join(sorted(map(str, args))).encode('utf-8'))
    code.update("".join(sorted(map(str, kwargs.items()))).encode('utf-8'))

    return code.hexdigest()


def _valid_cache(value, now):
    if value:
        if value["expire"] > now:
            log.debug(f'get value from local cache')
            return True
        else:
            log.debug('value in local cache expired!')
            return False
    else:
        log.debug('value not found in local cache')
        return False
