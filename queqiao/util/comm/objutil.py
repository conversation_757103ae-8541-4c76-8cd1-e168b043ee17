"""
Author: xiaohei
Date: 2022/4/21
Email: <EMAIL>
Host: xiaohei.info
"""
import importlib
import inspect
import pkgutil

from queqiao.log import LogFactory

log = LogFactory.get_logger()


def new_instance(module_name, class_name, *args, **kwargs):
    module_meta = __import__(module_name, globals(), locals(), [class_name])
    class_meta = getattr(module_meta, class_name)
    obj = class_meta(*args, **kwargs)
    return obj


def new_mdl_instance(module_name):
    return importlib.import_module(module_name)


def default_filter(obj):
    return True


def find_cls_in_pkg(pkg__path, pkg__package, func_filter=default_filter, recursive=True, excludes=None, mdl_only=False,
                    self_pkg_only=False):
    '''
    每次都会创建搜索包下所有class对应的对象，需要注意使用
    :param pkg__path: 包路径.__path__属性
    :param pkg__package: 包名.__package__属性
    :param func_filter: 类对象过滤条件
    :param recursive: 是否递归搜索
    :param excludes: 去除列表
    :return: {模块名:类对象列表}
    '''
    # pkg__path = pkg.__path__
    # pkg__package = pkg.__package__
    log.debug(f'pkg__path:{pkg__path},pkg__package:{pkg__package}')
    res = {}
    for filefinder, mdl_name, ispkg in pkgutil.iter_modules(pkg__path):
        if excludes and mdl_name in excludes:
            log.debug(f'module {mdl_name} is in exclude modules, skip')
            continue
        full_module_name = f'{pkg__package}.{mdl_name}'
        mdl = filefinder.find_module(full_module_name).load_module(full_module_name)
        log.debug(f'found {mdl} with mdl_name: {full_module_name}')
        log.debug(f'search classes in module {full_module_name}')
        classes = [c for _, c in inspect.getmembers(mdl, inspect.isclass) if func_filter(c)]
        if self_pkg_only:
            classes = [c for c in classes if f'{full_module_name}.' in str(c)]
        log.debug(f'found {len(classes)} classes in {full_module_name}: {classes}')
        if classes and not (mdl_only and ispkg):
            res[full_module_name] = classes

        if recursive and ispkg:
            res.update(find_cls_in_pkg(mdl.__path__, full_module_name, func_filter, recursive, excludes, mdl_only,
                                       self_pkg_only))
    return res


def find_mdl_in_pkg(pkg__path, pkg__package, recursive=True):
    log.debug(f'pkg__path:{pkg__path},pkg__package:{pkg__package}')
    res = {}
    for filefinder, mdl_name, ispkg in pkgutil.iter_modules(pkg__path):
        full_module_name = f'{pkg__package}.{mdl_name}'
        # 必须使用全名，否则将匹配所有符合的子包名
        mdl = filefinder.find_module(full_module_name).load_module(full_module_name)
        log.debug(f'found {full_module_name} with module {mdl}')
        res[full_module_name] = mdl

        if recursive and ispkg:
            res.update(find_mdl_in_pkg(mdl.__path__, full_module_name, recursive))
    return res


def get_cls_functions(cls):
    return get_cls_members(cls, filter=lambda a: inspect.isfunction(a))


def get_cls_attributes(cls):
    return get_cls_members(cls, filter=lambda a: not inspect.isfunction(a))


def get_cls_members(cls, filter=None):
    members = inspect.getmembers(cls, filter) if filter else inspect.getmembers(cls)
    return members
