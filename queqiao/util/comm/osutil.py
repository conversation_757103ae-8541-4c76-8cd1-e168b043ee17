"""
Author: xiaohei
Date: 2021/8/28
Email: <EMAIL>
Host: xiaohei.info
"""
import os
import platform
import shutil
import subprocess

import psutil


def touch(file_name):
    if not os.path.exists(file_name):
        f = open(file_name, 'w')
        f.close()


def exists(file_name):
    return os.path.exists(file_name)


def cp(f_from, f_to):
    shutil.copy2(f_from, f_to)


def mv(f_from, f_to):
    shutil.move(f_from, f_to)


def rm(file_name):
    if not exists(file_name):
        print(f'{file_name} does not exists')
        return
    if os.path.isdir(file_name):
        print(f'{file_name} is a dir, deleted')
        shutil.rmtree(file_name, ignore_errors=True)
    else:
        print(f'{file_name} is a file, deleted')
        os.remove(file_name)


def mkdir(dir_path):
    os.makedirs(dir_path)


def list_files(path, prefix=None, suffix=None):
    all_files = []
    for root, dirs, files in os.walk(path):
        for file in files:
            if (not prefix and not suffix) \
                    or (prefix and suffix and file.startswith(prefix) and file.endswith(suffix)) \
                    or (prefix and file.startswith(prefix)) \
                    or (suffix and file.endswith(suffix)):
                all_files.append(os.path.join(root, file))
        # for dir in dirs:
        #     all_dirs.append(os.path.join(root, dir))
    return all_files


def wc(file_name):
    out = subprocess.getoutput("wc -l %s" % file_name)
    return int(out.split()[0])


def nf(file_name, target_sep):
    sep = target_sep if target_sep != 'special' else '\x01'
    cmd = "head -n 1 %s | awk -F '%s' '{print NF}'" % (file_name, sep)
    out = subprocess.getoutput(cmd)
    return int(out.split()[0]) if len(out) > 0 else 0


def callb(cmd, stderr=False):
    p = subprocess.Popen(cmd, shell=True, stdout=subprocess.PIPE, stderr=subprocess.STDOUT) if stderr \
        else subprocess.Popen(cmd, shell=True, stdout=subprocess.PIPE)
    out = p.stdout.readlines()
    return out


def calls(cmd, stderr=False):
    res = callb(cmd, stderr)
    ret = [r.decode('utf8').strip() for r in res]
    return ret[0] if len(ret) == 1 else ret


def call(cmd, env=None):
    # 不能直接用check_call，会直接异常退出
    kwargs = {'shell': True}
    if env:
        kwargs['env'] = env
    print(f'call cmd: [{cmd}] with kwargs: [{kwargs}]')
    return subprocess.call(cmd, **kwargs)


def df(disk):
    df_cmd = 'df | grep ' + disk + ' | awk \'{print $4}\''
    df_res = callb(df_cmd)
    ava_size = int(df_res[0]) * 1024 if len(df_res) > 0 else 0
    return ava_size


def remove_header(filename):
    header = calls(f'head -n 1 {filename}')
    cmd = f'tail -n +2 {filename} > {filename}.tmp && mv {filename}.tmp {filename}'
    call(cmd)
    return header


def add_header(filename, header):
    if platform.system() == 'Linux':
        call(f"sed -i '1i {header}' {filename}")
    else:
        call(f"sed -i '' '1i\\\n{header}\n' {filename}")


def merge_files(many_files, target_file=None):
    target_file = target_file if target_file else many_files.replace('*', '')
    if target_file == many_files:
        return 0, target_file
    if not exists(os.path.dirname(target_file)):
        mkdir(os.path.dirname(target_file))
    cmd = f'cat {many_files} > {target_file}'
    ret = call(cmd)
    return ret, target_file


def bytes(filepath):
    if not exists(filepath):
        return -1
    return os.path.getsize(filepath)


def ctime(filepath):
    if not exists(filepath):
        return -1
    return os.path.getctime(filepath)


def get_pcmd(pid):
    p = psutil.Process(pid)
    return ' '.join(p.cmdline())


def convert_special_chars(ora_str):
    spec_chars = ['^', '/', '$']
    for i in spec_chars:
        ora_str = ora_str.replace(i, '\\' + i)
    return ora_str


def tar_zxvf(file_path):
    dir_path = os.path.dirname(file_path)
    file_name = os.path.basename(file_path)
    cmd = f'cd {dir_path} && tar -zxvf {file_name}'
    return call(cmd)
