from typing import Optional

from queqiao.conf.errors import OrgRequestFailedException
from queqiao.util.mt.mthttp import HttpClientBase


class OrgClient(HttpClientBase):
    """
    org 服务, api 文档 https://km.sankuai.com/page/121665947
    """

    service_name = 'org'

    DATA_SCOPE = {
        'meituan': 'tenantId=1;source=ALL'
    }

    def get_emp_info(self, mis: str):
        user_info = self.__get_user_info(mis)
        if not user_info:
            raise OrgRequestFailedException(f'mis:{mis} does not exists')

        reportEmpName = user_info.get('reportEmpName')
        orgName = user_info.get('orgName')
        return reportEmpName, orgName

    def __get_user_info(self, mis: str) -> Optional[dict]:
        path = f'/api/org2/emps/_batch?mises={mis}&snapshot=0'
        for k, v in self.DATA_SCOPE.items():
            r = self._request('get', path, headers={'data-scope': v})
            user_info = r.get('data')
            if user_info:
                return user_info[0]

        return None

    def check_result(self, r: dict) -> None:
        pass
