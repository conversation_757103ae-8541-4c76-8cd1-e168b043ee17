
from queqiao.log import LogFactory
from queqiao.util.conn.http import HttpClient

log = LogFactory.get_logger()


class Utvs:
    '''
    接口文档：
    https://service.sankuai.com/#/services/com.sankuai.nlpml.udm.utvsapi/docs/PersonaCrowdService_octo.thrift/PersonaCrowdService_RerunCrowdResponse%2520rerunCrowd(RerunCrowdRequest%2520request)?isOnline=true
    IDL文件：
    https://km.sankuai.com/collabpage/719448050#b-f0d5353b5f94428b82509e65ae6b27f8
    update: mtthrift接口的调用移动到天问中统一处理，此处仅通过http调用天问接口
    '''
    @classmethod
    def rerun_crowd(cls, persona_group_id):
        if not isinstance(persona_group_id, int):
            try:
                persona_group_id = int(persona_group_id)
            except ValueError:
                raise ValueError(
                    f'Invalid persona_group_id: {persona_group_id}')
        log.info(
            f'Calling persona rerunCrowd with persona_group_id: {persona_group_id}')
        response = HttpClient.request(
            url='https://twlab.sankuai.com/board-service/test/persona/rerun',
            method='POST',
            json={
                'persona_group_id': persona_group_id
            }
        )
        log.info(f'response from persona api: {response}')
        return response
