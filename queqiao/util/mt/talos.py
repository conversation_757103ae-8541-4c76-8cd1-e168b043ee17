"""
Author: xiaohei
Date: 2022/5/7
Email: <EMAIL>
Host: xiaohei.info
"""
import time

from pytalos.client import AsyncTalosClient

from instance.default import UNIX_LINE_ENDING, TALOS_DEFAULT_DSN
from queqiao.conf.system import SystemConfig
from queqiao.util.comm.dtutil import timer
from queqiao.util.hadoop.hive import HiveClient


class TalosClient(HiveClient):
    def __init__(self, uname=None, passwd=None, engine=None, logger=None, download_max=None, fetch_buffer=None):
        super().__init__(logger)
        username = SystemConfig.read('TALOS_USERNAME') if not uname else uname
        password = SystemConfig.read(
            'TALOS_PASSWORD') if not passwd else passwd
        engine = SystemConfig.read('TALOS_ENGINE') if not engine else engine
        self.__client = AsyncTalosClient(username=username, password=password)
        self.__talos_engine = engine
        self.download_max = int(SystemConfig.read(
            'TALOS_DOWNLOAD_MAX')) if not download_max else download_max
        self.fetch_buffer = int(SystemConfig.read(
            'TALOS_FETCH_BUFFER')) if not fetch_buffer else fetch_buffer

    def open(self):
        self.__client.open_session()

    def close(self):
        self.__client.close_session()

    def _select_and_download(self, sql, save_file):
        # 同步读取并下载结果
        qid = self.__client.submit(
            dsn=TALOS_DEFAULT_DSN, statement=sql, engine=self.__talos_engine)
        self.logger.info(
            'submit sql to hive server, qid: %s, current talos engine is: %s' % (qid, self.__talos_engine))
        self.__wait_for_finish(qid)
        query_info = self.__client.get_query_info(qid)
        self.query_info = query_info
        self.logger.info('total count: %s, result size: %s' %
                         (query_info['total'], query_info['resultSize']))

        if int(query_info['total']) <= self.download_max:
            res = self.__client.download(qid)
            with open(save_file, 'w') as local_file:
                for row in res:
                    line = self._col_sep.join([str(x) for x in row])
                    local_file.write(line + UNIX_LINE_ENDING)
        else:
            self.logger.info(
                'result size %s is great then TALOS_DOWNLOAD_MAX: %s, switch to batch download, buffer size is %s' % (
                    query_info['total'], self.download_max, self.fetch_buffer))
            offset = 0
            index = 0
            with open(save_file, 'w') as local_file:
                while offset < query_info['total']:
                    if index >= 20 and index % 20 == 0:
                        self.logger.info(f'{index} reopen talos session')
                        self.open()
                    index += 1
                    res = self.__client.fetch_result(
                        qid, offset, self.fetch_buffer)
                    res = res['data']
                    self.logger.info(
                        'current batch: %s, offset: %s, fetched %s data' % (str(index), str(offset), len(res)))
                    for row in res:
                        line = self._col_sep.join([str(x) for x in row])
                        # line = target_sep.join(row)
                        local_file.write(line + UNIX_LINE_ENDING)
                    local_file.flush()
                    offset = offset + len(res)
        self.logger.info('download from server finished')
        # total/resultSize/columns
        return query_info

    def exec(self, sql, async_=False):
        qid = self.__client.submit(dsn=TALOS_DEFAULT_DSN, statement=sql, engine=self.__talos_engine)
        self.__wait_for_finish(qid)
        return self.__client.get_query_info(qid)

    def schema(self, tablename):
        sql = f'desc {tablename}'
        qid = self.__client.submit(
            dsn=TALOS_DEFAULT_DSN, statement=sql, engine=self.__talos_engine)
        self.logger.info(
            f'will desc {tablename} and get meta from talos, wait for finished...')
        self.__wait_for_finish(qid, sleep_time=1)

        # 存在query_info表示已经过查询，直接从query_info中获取列信息
        columns = {}
        if hasattr(self, 'query_info'):
            for col in self.query_info['columns']:
                columns[col['name']] = col

        # 获取源表信息
        schemas = {}
        res = self.__client.download(qid)
        for r in res:
            arr = ''.join(r).split(' ')
            if len(arr) > 2:
                col = {'name': arr[0], 'type': arr[1], 'comment': arr[2]}
            else:
                # 没有注释的情况
                col = {'name': arr[0], 'type': arr[1], 'comment': ''}
            schemas[col['name']] = col

        # 如果有query_info则补全注释，否则使用schema
        if len(columns) > 0:
            for col in columns.keys():
                if col in schemas:
                    columns[col]['comment'] = schemas[col]['comment']
        else:
            columns = schemas
        self.logger.info(f'get meta success, columns meta: {columns.values()}')
        return list(columns.values())

    def schema_multiple(self, tablenames):
        for col in self.query_info['columns']:
            col['comment'] = ''
        return self.query_info['columns']

    def __wait_for_finish(self, qid, sleep_time=5, max_wait_time=10800):
        start_time = timer.now().timestamp
        while True:
            query_info = self.__client.get_query_info(qid)
            query_log = self.__client.engine_log(qid)
            # self.logger.info('query status is: %s' % query_info['status'])
            # 查询状态，枚举类型：PENDING（排队中），RUNNING（正在执行），FINISHED（查询成功），KILLED（查询被取消），FAILED（查询失败，执行引擎发生错误），ERROR（查询失败，Talos发生错误）
            if query_info['status'] == "FINISHED":
                self.logger.info(query_log)
                break
            elif query_info['status'] in ["QUERY_TIMEOUT", "FAILED", "KILLED"] or query_info["status"].startswith(
                    "ERROR_"):
                self.logger.error(query_log)
                raise Exception(query_log)
            elapsed = timer.now().timestamp - start_time
            if elapsed > max_wait_time:
                raise TimeoutError(f'current query {qid} runnning for a long time({max_wait_time / 3600}h), '
                                   f'stop to wait and raise exception, please retry')
            time.sleep(sleep_time)
