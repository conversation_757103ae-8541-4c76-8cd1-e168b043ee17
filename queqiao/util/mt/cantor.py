'''
未测试，使用手册参考：
http://cantor.data.st.sankuai.com/apidocs/
https://km.sankuai.com/page/152448473
'''

import logging
from typing import Any, Dict, List, Union

from queqiao.conf.errors import CantorRequestFailedException
from queqiao.util.mt.mthttp import HttpClientBase

log = logging.getLogger(__name__)


class CantorClient(HttpClientBase):
    service_name = 'cantor'

    def check_result(self, data: Dict[str, Any]) -> None:
        if data.get('code', -1) != 0:
            raise CantorRequestFailedException(data.get('msg') or data.get('message'))

        if len(data.get('data', {})) == 0:
            raise CantorRequestFailedException(data.get('msg') or data.get('message'))

    def once_exec(
            self, creator: str, exec_mode: str,
            exec_tasks: Dict[str, Dict[str, Union[str, List[str]]]],
            schedule_time: str, desc: str
    ) -> int:
        urlpath = '/api/v2/once_exec'
        payload = dict(
            creator=creator,
            exec_mode=exec_mode,
            exec_tasks=exec_tasks,
            schedule_time=schedule_time,
            desc=desc,
        )
        log.info(f'cantor once_exec with params: {payload}')
        data = self._request('post', urlpath, json=payload)
        log.info(f'cantor once_exec result: {data}')
        return data['data']['id']

    def get_exec_info(self, once_exec_id: int) -> \
            Dict[str, Union[str, int, Dict[str, Union[str, int]]]]:
        urlpath = f'/api/v2/once_exec/{once_exec_id}/exec_info'
        data = self._request('get', urlpath)
        return data['data']

    def get_node_exec_info(
            self, once_exec_id: int, plan_name: str, with_log: bool = False
    ) -> Dict[str, Union[int, str]]:
        urlpath = f'/api/v2/once_exec/{once_exec_id}/plan/{plan_name}/exec_info'
        view_log = 'true' if with_log else 'false'
        data = self._request('get', urlpath, params=dict(view_log=view_log))
        return data['data']

    def ops(self, once_exec_id: int, action: str) -> int:
        urlpath = f'/api/v2/once_exec_ops/{once_exec_id}/{action}'
        data = self._request('post', urlpath)
        return data['data']['id']

    def pause(self, once_exec_id: int) -> int:
        return self.ops(once_exec_id, action='pause')

    def resume(self, once_exec_id: int) -> int:
        return self.ops(once_exec_id, action='resume')

    def get_online_task_duration_distribution(self, task_type: str, duration_second: int) -> Dict[str, Any]:
        urlpath = f'/api/v2/job/history/duration_total_distribute'
        params = {
            'task_type': task_type,
            'duration_second': duration_second,
        }
        return self._request('get', urlpath, params=params)

    def get_task_duration_time_history(self, task_name: str) -> Dict[str, Any]:
        urlpath = f'/api/v2/job/history/duration_time/{task_name}'
        return self._request('get', urlpath)
