# -*- coding: utf-8 -*-
from __future__ import unicode_literals

import json as pyjson
from typing import Any, Dict, Optional

import requests
from flask import current_app as app
from retrying import retry

from queqiao.conf.errors import NETWORK_ERRORS, HttpRequestError
from queqiao.log import LogFactory
from queqiao.util.mt.mwsauth import MWSAuth

log = LogFactory.get_logger()

TIMEOUT = 5


def retry_if_networkerror_or_timeout(exc: Exception) -> bool:
    return isinstance(exc, NETWORK_ERRORS)


class HttpClientBase:
    service_name = ''

    def __init__(
            self, service: str, host: str, client_id: str, client_secret: str
    ) -> None:
        self.service = service
        self.host = host
        self.client_id = client_id
        self.client_secret = client_secret
        self.auth = MWSAuth(client_id, client_secret)

    @classmethod
    def from_flask_config(cls, service: Optional[str] = None) -> Any:
        service = service or cls.service_name
        service_config = app.config[service.upper() + '_SERVICE_INFO']
        return cls(service, **service_config)

    @retry(
        wait_exponential_multiplier=1000,
        wait_exponential_max=10000,
        stop_max_delay=30 * 1000,
        retry_on_exception=retry_if_networkerror_or_timeout,
    )
    def _request(
            self,
            method: str,
            path: str,
            headers: Optional[dict] = None,
            json: Optional[Dict[str, Any]] = None,
            data: Optional[Dict[str, Any]] = None,
            params: Optional[Dict[str, str]] = None,
            timeout: int = TIMEOUT
    ) -> Dict[str, Any]:  # escape hatch, 因为这个方法返回的数据太多类型了

        req_args = dict(
            data=data or {},
            params=params or {},
            timeout=timeout,
            auth=self.auth,
        )
        options = dict(
            json=json,
            headers=headers,
        )
        req_args.update({k: v for k, v in options.items() if v})
        try:
            r = requests.request(method, self.host + path, **req_args)
            r.raise_for_status()  # raise HTTPError when status code >= 400
            result = pyjson.loads(r.text)
            self.check_result(result)
            return result
        except requests.HTTPError as exc:
            log.exception(
                f'request {self.service_name}: {method} {path} with {data or params} '
                f'got HTTPError: {exc}'
            )
            # 交给上层处理
            raise HttpRequestError(f'request {self.service_name} failed.')
        except Exception as e:
            log.exception(
                f'request {self.service_name}: {method} {path} with {data or params} miss exception: {e}'
            )
            raise e

    def check_result(self, r: Dict[str, Any]) -> None:
        """ 能正常接受到响应 但是响应不正确 抛出异常"""
        raise NotImplementedError
