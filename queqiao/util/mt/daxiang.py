"""
Author: xiaohei
Date: 2022/5/7
Email: <EMAIL>
Host: xiaohei.info
"""
import base64
import hashlib
import hmac
import json
import time

import requests
from mtthrift import MTThrift

from instance.default import AUDIT_UDB_APPKEY, AUDIT_IDL_FILE_UDB, APPKEY, system_env, AUDIT_UDB_TENANT, \
    AUDIT_SERVER_APPKEY, AUDIT_IDL_FILE
from instance.default import DXPS_CLIENT_ID, DXPS_CLIENT_SECRET, DXPS_URL_PATH, DXPS_PUB_ID, DXPS_HOST
from queqiao.conf.system import SystemConfig
from queqiao.log import LogFactory
from queqiao.util.mt import audit_env

log = LogFactory.get_logger()
mt_uids = {}


class Udb:
    @classmethod
    def get_uid(cls, mis):
        env = system_env if system_env in AUDIT_UDB_TENANT.keys() else 'test'
        tenant = AUDIT_UDB_TENANT.get(env)
        log.debug(f'env: {env}, tenant: {tenant}')

        log.debug(f'init udb_thrift with udb_idl_file: {AUDIT_IDL_FILE_UDB}')
        udb_thrift = MTThrift(AUDIT_UDB_APPKEY, AUDIT_IDL_FILE_UDB, env=env, client_appkey=APPKEY)
        passport = f'{mis}@{tenant}'

        try:
            uid = udb_thrift.client.getUid(passport)
            if uid > 0:
                return uid
            else:
                raise Exception(f'get uid with passport:{passport} error!')
        # except Exception as e:
        #     raise Exception(str(e))
        finally:
            udb_thrift.client.close()


# todo: 使用快搭
class DxAudit:
    @classmethod
    def __get_mt_uid(cls, mis):
        if mis not in mt_uids:
            mt_uids[mis] = Udb.get_uid(mis)
        uid = mt_uids[mis]
        return uid

    @classmethod
    def __init_mtthrift(cls):
        # 每次都要重新创建thrift，否则会与udf的thrift串内容导致异常
        return MTThrift(AUDIT_SERVER_APPKEY, AUDIT_IDL_FILE, env=audit_env, client_appkey=APPKEY)

    @classmethod
    def __get_audit_template(cls, key, group):
        template_ids = json.loads(SystemConfig.read('DXKS_TEMPLATE_IDS'))
        env_ids = template_ids.get(audit_env, None)
        if not env_ids:
            raise Exception(f'{audit_env} not found in {template_ids}')
        group_ids = env_ids.get(group) if group in env_ids else env_ids.get('default')
        tid = group_ids.get(key, None)
        if not tid:
            raise Exception(f'{key} not found in {group_ids}')

        templates = json.loads(SystemConfig.read('DXKS_TEMPLATES'))
        template = templates.get(key, None)
        if not template:
            raise Exception(f'group {group} does not match any audit template')
        return tid, template

    @classmethod
    def create_audit(cls, mis, group, content):
        # 管理后台: https://admin.neixin.cn/oa/approval/index?code=4f0b38c04cb2f4fcc0640071cd68ecce
        if not isinstance(content, dict):
            raise Exception(f'audit content required be a list dict')
        mt_uid = cls.__get_mt_uid(mis)
        log.debug(f'get mt uid {mt_uid} with mis {mis}')
        audit_thrift = cls.__init_mtthrift()
        temp_key = 'single' if 'where2where' in content else 'batch'
        template_id, template_content = cls.__get_audit_template(temp_key, group)
        log.debug(f'get template {template_content} with key: {temp_key}')
        try:
            log.debug(f'env: {audit_env}, template_id: {template_id}')

            # if len(content) != len(template_content):
            #     raise Exception(f'audit content length {len(content)} dose not match template '
            #                     f'content length {len(template_content)}')
            template_content = json.dumps(template_content)
            # 批量模板填充内容:apply_id,create_time,create_user,leader,department,scenario,describe,security_level,information,apply_uri,project,user_group
            # 单条模板填充内容:apply_id,create_time,create_user,leader,department,scenario,describe,security_level,information,task_name,where2where,trans_type,result_size,engine,params,source_configs,sink_configs
            for k in content.keys():
                template_content = template_content.replace('{' + str(k) + '}', str(content[k]))
            if '"{' in template_content:
                raise Exception(
                    f'template dose not fill enough with content, content: {content}, template: {template_content}')
            log.info(f'start request thrift server with mis: {mis}, uid: {mt_uid}')
            log.debug(f'template_content: {template_content}')
            apply_res = audit_thrift.client.submitApply(template_id, mt_uid, template_content)
            apply_id = apply_res.result
            res = {'code': 0, 'msg': 'success', 'data': {'apply_id': apply_id}}
            log.info('audit flow created!')
        except Exception as why:
            raise Exception(why)
        finally:
            audit_thrift.client.close()
        return res

    @classmethod
    def get_audit_detail(cls, mis, apply_id):
        mt_uid = cls.__get_mt_uid(mis)
        audit_thrift = cls.__init_mtthrift()
        try:
            detail_res = audit_thrift.client.applyDetail(apply_id, mt_uid)
            res = {
                'code': 0,
                'msg': 'success',
                'data': {
                    'apply_id': apply_id,
                    'sponsor_name': detail_res.sponsorName,
                    'next_approver': detail_res.nextApprover,
                    'all_approvers': ','.join(a.name for a in detail_res.approvers)
                }
            }
        except Exception as why:
            raise Exception(why)
        finally:
            audit_thrift.client.close()
        return res

    @classmethod
    def push_agree(cls, mis, apply_id):
        mt_uid = cls.__get_mt_uid(mis)
        audit_thrift = cls.__init_mtthrift()
        try:
            push_res = audit_thrift.client.pushAgree(mt_uid, apply_id)
            res = {
                'code': push_res.rescode,
                'msg': push_res.message,
                'data': push_res.data
            }
        except Exception as why:
            raise Exception(why)
        finally:
            audit_thrift.client.close()
        return res


class DxPush:
    @classmethod
    def __gen_headers(cls):
        timestamp = time.strftime('%a, %d %b %Y %H:%M:%S GMT', time.gmtime())
        string_to_sign = ('%s %s\n%s' % ('PUT', DXPS_URL_PATH, timestamp))
        hmac_bytes = hmac.new(bytes(DXPS_CLIENT_SECRET.encode('ascii')),
                              bytes(string_to_sign.encode('ascii')),
                              hashlib.sha1).digest()
        auth = base64.b64encode(hmac_bytes).decode("utf-8")
        return {
            'Date': timestamp,
            'Authorization': 'MWS %s:%s' % (DXPS_CLIENT_ID, auth),
            'Content-Type': 'application/json;charset=utf-8',
        }

    @classmethod
    def push_msg(cls, msg, receivers):
        if isinstance(receivers, str):
            receivers = receivers.split(',')
        if not isinstance(receivers, list):
            raise Exception(f'param receivers is not a list, type: {type(receivers)}')
        data = {
            'fromName': 'FtpLink',
            'fromUid': DXPS_PUB_ID,
            'receivers': receivers,
            'body': {
                'text': '%s' % msg
            }
        }
        r = requests.put(DXPS_HOST + DXPS_URL_PATH, headers=cls.__gen_headers(), data=json.dumps(data))
        log.debug('dx server return: %s' % r.content)
        if len(json.loads(r.content)['data']['mids']) < 1:
            return False
        else:
            return True
