# coding=utf-8
import time
import datetime
import email.utils
from urllib.parse import urlparse
import hmac
import base64
import hashlib
import re

# from requests import auth
import requests


class MWSAuth(requests.auth.AuthBase):
    """
    requests's New Forms of Authentication
    http://docs.python-requests.org/en/latest/user/authentication/#new-forms-of-authentication

    美团签名请求规范
    http://wiki.sankuai.com/pages/viewpage.action?pageId=29755412
    """

    def __init__(self, client_id, client_secret):
        self.client_id = client_id
        self.client_secret = client_secret

    def _create_http_date(self, now=None):
        """
        RFC 1123 Date Representation in Python
        http://stackoverflow.com/questions/225086/rfc-1123-date-representation-in-python
        """
        now = now or datetime.datetime.now()
        stamp = time.mktime(now.timetuple())
        return email.utils.formatdate(
            timeval=stamp,
            localtime=False,
            usegmt=True
        )

    @staticmethod
    def _gen_signature(client_secret, method, path, http_date):
        str_to_sign = "%s %s\n%s" % (method, path, http_date)
        signature = base64.b64encode(hmac.new(client_secret, str_to_sign, hashlib.sha1).digest())
        return signature

    @staticmethod
    def _gen_authorization(client_id, signature):
        return "MWS %s:%s" % (client_id, signature)

    def __call__(self, r):
        date = self._create_http_date()
        url_parts = urlparse(r.url)

        str_to_sign = "%s %s\n%s" % (r.method, url_parts.path, date)
        signature = base64.b64encode(hmac.new(self.client_secret.encode(), str_to_sign.encode(),
                                              hashlib.sha1).digest()).decode()
        authorization = 'MWS %s:%s' % (self.client_id, signature)

        r.headers['Date'] = date
        r.headers['Authorization'] = authorization
        return r


class VerificationError(Exception):
    pass


AUTHORIZATION_PATTERN = re.compile(r'MWS ([\w.]+):(.+)')


def parse_authorization(authorization):
    """
    从签名解析出client_id,signature,形如:
    MWS data:TH3gJtTHONXb6d793yoAFvycHSQ=
    """
    match = AUTHORIZATION_PATTERN.findall(authorization)
    if not match or len(match) > 1:
        raise VerificationError("%s is not a MWS authorization" % authorization)
    client_id, signature = match[0]

    return client_id, signature


def verify_signature(signature, client_secret, method, path, http_date):
    server_signature = MWSAuth._gen_signature(
        client_secret, method, path, http_date)

    return signature == server_signature


if __name__ == '__main__':
    client_id = 'data'
    client_secret = '60199cd5928f8d3a5516be5d7148595b'
    params = {
        'mobile': 13811053895,
        'mobile': 18602636149,
        'mobile': 15101516263,
        'msg': 'ETL Alert [High] 执行失败 2014-10-21 11:22:34 dujun02 horigindb.www__order INIT',
    }
    r = requests.post('http://open-in.meituan.com/sms/send', data=params, auth=MWSAuth(client_id, client_secret))
    print(r.json())
