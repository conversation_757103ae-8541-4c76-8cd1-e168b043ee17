"""
Author: xiaohei
Date: 2022/8/4
Email: <EMAIL>
Host: xiaohei.info
"""
import pytest

from instance.default import PROJECT_PATH
from queqiao.util.comm import osutil

from tests import task_talos2ftp, project


@pytest.mark.skip
@pytest.mark.parametrize('params', [
    (task_talos2ftp.name, 1),
    (f'hhh.{task_talos2ftp.name}', 1),
    (f'{project.name}.{task_talos2ftp.name}', 0)
])
def test_client(params):
    task_name, code = params
    cmd = f'python {PROJECT_PATH}/queqiao/client/request.py {task_name}'
    ret = osutil.call(cmd)
    assert ret == code
