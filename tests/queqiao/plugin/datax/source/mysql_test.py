"""
Author: xiaohei
Date: 2022/7/26
Email: <EMAIL>
Host: xiaohei.info
"""
import json

from queqiao.core.engine.base import Channel
from queqiao.plugin import plugins
from tests.queqiao.plugin.datax import mysql_source_config, dsn_dit


class TestMysql:

    def setup_class(self):
        self.source = plugins.get('queqiao.plugin.datax.source.mysql')[0](mysql_source_config)
        self.channel = Channel()

    def test_read(self):
        self.source.read(self.channel)
        assert 'reader' in self.channel.get_pipeline_keys()
        reader_config = self.channel.get_pipeline_param('reader')
        print('reader config:')
        print(json.dumps(reader_config))
        assert reader_config['name'] == 'mysqlreader'
        dsn_connect = json.loads(dsn_dit['connect'])
        assert reader_config['parameter']['username'] == dsn_connect['username']
        assert reader_config['parameter']['password'] == dsn_connect['password']
