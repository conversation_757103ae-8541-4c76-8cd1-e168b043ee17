"""
Author: xiaohei
Date: 2022/7/26
Email: <EMAIL>
Host: xiaohei.info
"""
import json

from queqiao.core.engine.base import Channel
from queqiao.plugin import plugins
from tests.queqiao.plugin.datax import mysql_sink_config, dsn_dit


class TestMysql:
    def setup_class(self):
        self.sink = plugins.get('queqiao.plugin.datax.sink.mysql')[0](mysql_sink_config)
        self.channel = Channel()

    def test_write(self):
        self.sink.write(self.channel)
        assert 'writer' in self.channel.get_pipeline_keys()
        writer_config = self.channel.get_pipeline_param('writer')
        print('writer config:')
        print(json.dumps(writer_config))
        assert writer_config['name'] == 'mysqlwriter'
        dsn_connect = json.loads(dsn_dit['connect'])
        assert writer_config['parameter']['username'] == dsn_connect['username']
        assert writer_config['parameter']['password'] == dsn_connect['password']
