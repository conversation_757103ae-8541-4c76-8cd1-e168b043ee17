"""
Author: xiaohei
Date: 2022/7/26
Email: <EMAIL>
Host: xiaohei.info
"""
import json

from queqiao.conf import Config
from queqiao.core.engine.base import Channel
from queqiao.plugin import plugins
from queqiao.util.comm.dtutil import timer
from tests.queqiao.plugin.ftplink import execution_dict


class TestFile:
    def setup_class(self):
        self.sink = plugins.get('queqiao.plugin.datax.sink.file')[0](
            Config({'target_sep': ',', 'file_path': '/tmp', 'file_name': 'queqiao_test',
                    'execution': execution_dict, 'current': timer.now().delta(1).date
                    }))
        self.channel = Channel()

    def test_write(self):
        self.sink.write(self.channel)
        assert 'writer' in self.channel.get_pipeline_keys()
        writer_config = self.channel.get_pipeline_param('writer')
        print('writer config:')
        print(json.dumps(writer_config))
        assert writer_config['name'] == 'txtfilewriter'
