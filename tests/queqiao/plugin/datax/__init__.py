"""
Author: xiaohei
Date: 2022/7/26
Email: <EMAIL>
Host: xiaohei.info
"""
import json

# sink_config = {
#     'sink_key1': 'sink_value1',
#     'sink_key2': 'sink_value2',
#     'name': 'ftp',
#     'dsn': sink_dsn.to_dict()
# }
from queqiao.conf import Config
from queqiao.util.comm.dtutil import timer
from tests.queqiao.plugin.ftplink import execution_dict

dsn_dit = {
    'name': 'mysql_source',
    'connect': json.dumps(
        {'ip': 'localhost', 'port': 3306, 'db': 'queqiao', 'username': 'root', 'password': 'root'}),
    'dsn_type': 'mysql',
    'org_id': 1
}
mysql_source_config = Config({
    'source_key1': 'source_value1',
    'name': 'mysql',
    'sql': 'select * from task',
    'split_pk': 'id',
    'dsn': dsn_dit,
    'execution': execution_dict, 'current': timer.now().delta(1).date
})

mysql_sink_config = Config({
    'sink_key1': 'sink_value1',
    'name': 'mysql',
    'write_mode': 'overwrite',
    'table_cols': 'id,status',
    'table_name': 'task',
    'pre_sql': '',
    'post_sql': '',
    'batch_size': '',
    'dsn': dsn_dit,
    'execution': execution_dict, 'current': timer.now().delta(1).date
})
