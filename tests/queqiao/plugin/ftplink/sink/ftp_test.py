"""
Author: xiaohei
Date: 2022/7/28
Email: <EMAIL>
Host: xiaohei.info
"""
import json
import os
import pytest

from queqiao.conf import Config
from queqiao.core.engine.base import Channel
from queqiao.dba.models import FtplinkHistoryFile
from queqiao.plugin import plugins
from queqiao.util.comm import osutil, strutil, fileutil
from queqiao.util.comm.dtutil import timer
from tests import sftp_client, ftp_dsn
from tests.queqiao.plugin.ftplink import execution_dict

dsn_dit = ftp_dsn.to_dict()
now = timer.now()
config1 = {
    # 全路径/相对路径
    'file_path': f'/one-sftp-xy-bank/queqiao/test/sink/{now.datekey}',
    'file_name': f'test_from_config1_{now.datekey}.txt',
    # none/.ok/abc.ready/
    # 'ok_file': None,
    # none/true
    # 'retlist_file': None,
    # none/true
    # 'delete_localfile': None,
    # none/zip/tar.gz/7z
    # 'compress_type': None,
    # none/meituan
    # 'compress_passwd': None,
    'dsn': dsn_dit,
    'execution': execution_dict,
    'file_type': 'txt',
    'current': timer.now().date,
}

config_with_header = {
    'file_name': f'test_from_config_with_header_{now.datekey}.txt',
    'with_header': 1
}

config_with_header = dict(config1, **config_with_header)

config2 = {
    'file_path': f'abs/{now.datekey}',
    'file_name': f'test_from_config2_{now.datekey}.txt',
    'ok_file': '.ok',
    'retlist_file': True,
    'delete_localfile': False,
    'compress_type': 'zip',
    'compress_passwd': 'meituanhhh',
    'dsn': dsn_dit,
    'execution': execution_dict,
    'file_type': 'zip',
    'compress_file_type': 'txt'
}

config2 = dict(config1, **config2)

config3 = {
    'file_name': f'test_from_config3_{now.datekey}.txt',
    'ok_file': f'tar{now.datekey}.ready',
    'compress_type': 'tar.gz',
    # 'compress_passwd': None,
    'file_type': 'tar.gz',
    'md5_file': '.md5'
}

config3 = dict(config2, **config3)

config_package_compress_type1 = {
    'file_path': f'abs/{now.datekey}',
    'file_name': f'test_from_config_package_compress_type1_{now.datekey}.xml',
    'ok_file': 'sink_ftp_test.verf',
    'retlist_file': True,
    'delete_localfile': False,
    'compress_passwd': 'meituanhhh',
    'dsn': dsn_dit,
    'execution': execution_dict,
    'compress_type': 'tar.gz',
    'compress_file_type': 'xml',
    'compress_with_ok_file': True
}

config_package_compress_type2 = {
    'file_path': f'abs/{now.datekey}',
    'file_name': f'test_from_config_package_compress_type2_{now.datekey}.xml',
    'ok_file': '.ok',
    'retlist_file': True,
    'delete_localfile': False,
    'compress_passwd': 'meituanhhh',
    'dsn': dsn_dit,
    'execution': execution_dict,
    'compress_type': 'zip',
    'compress_with_ok_file': True,
    'compress_file_type': 'xml'
}

config_package_compress_type3 = {
    'file_path': f'abs/{now.datekey}',
    'file_name': f'test_from_config_package_compress_type3_{now.datekey}.xml',
    'ok_file': '.ok',
    'retlist_file': True,
    'delete_localfile': False,
    'dsn': dsn_dit,
    'execution': execution_dict,
    'compress_type': '7z',
    'compress_with_ok_file': True,
    'compress_file_type': 'xml'
}

# 测试压缩文件后缀处理的配置
config_suffix_test1 = {
    'file_path': f'abs/{now.datekey}',
    'file_name': f'test_suffix1_{now.datekey}.csv',
    'dsn': dsn_dit,
    'execution': execution_dict,
    'compress_type': 'tar.gz',
    'compress_file_type': 'csv',
    'file_type': 'tar.gz'
}

config_suffix_test2 = {
    'file_path': f'abs/{now.datekey}',
    'file_name': f'test_suffix2_{now.datekey}.txt',
    'dsn': dsn_dit,
    'execution': execution_dict,
    'compress_type': 'zip',
    'compress_file_type': 'txt',
    'file_type': 'zip'
}

local_savedir = '/tmp/queqiao'
local_filename = 'sink_ftp_test.txt'
local_filepath = f'{local_savedir}/{local_filename}'
local_filepath_ok = f'{local_savedir}/{local_filename}.ok'
ok_dict = {'ok_key1': 'value1', 'ok_key2': 'value2', 'source': 'hive',
           'local_file': local_filepath, 'bytes': 0, 'md5': '123', 'row_num': -1, 'col_cnt': -1, 'ctime': '1',
           'table_schema': [{'name': 'col1'}, {'name': 'col2'}, {'name': 'col3'}], 'target_sep': '\x01'}


class TestFtp:
    def setup_class(self):
        osutil.rm(local_savedir)
        if not osutil.exists(local_savedir):
            osutil.mkdir(local_savedir)
        if not osutil.exists(f'{local_savedir}/download'):
            osutil.mkdir(f'{local_savedir}/download')
        osutil.call(f'echo "9999\x011\x010000\n0000\x012\x019999" > {local_filepath}')

    def teardown_class(self):
        osutil.rm(local_savedir)

    @pytest.mark.parametrize('config', [
        config1,
        config2,
        config3,
        config_with_header
    ])
    def test_write(self, config):
        sink = plugins.get('queqiao.plugin.ftplink.sink.ftp')[0](Config(config))
        channel = Channel()
        ok_dict['md5'] = strutil.md5_file(local_filepath)
        channel.set_pipeline_param(local_filepath=local_filepath, ok_dict=ok_dict)
        sink.write(channel)
        # 压缩的部分会删除源文件，补上
        osutil.call(f'echo "9999\x011\x010000\n0000\x012\x019999" > {local_filepath}')

        connect = json.loads(dsn_dit['connect'])
        remote_dir = config['file_path'] if config['file_path'].startswith('/') else \
            connect['work_dir'] + '/' + config['file_path']
            
        # 根据压缩类型构造预期的远程文件名
        expected_filename = config['file_name']
        if 'compress_type' in config and config['compress_type'] and config['compress_type'] != 'none':
            # 获取文件名（不带任何后缀），处理类似.tar.gz的多重后缀情况
            filename_without_ext = os.path.splitext(os.path.basename(config['file_name']))[0]
            # 如果文件名中仍有.，继续分割直到去掉所有后缀
            while '.' in filename_without_ext:
                filename_without_ext = os.path.splitext(filename_without_ext)[0]
            expected_filename = filename_without_ext + f".{config['compress_type']}"
            
        remote_filepath = f"{remote_dir}/{expected_filename}"
        print(f"Checking remote file: {remote_filepath}")
        assert sftp_client.exists(remote_filepath)
        local_saved_filepath = f'{local_savedir}/download/{expected_filename}'
        sftp_client.download(remote_filepath, local_saved_filepath)
        if 'compress_type' in config and config['compress_type']:
            local_saved_filepath = fileutil.decompress(local_saved_filepath, config['compress_type'],
                                                       config['compress_passwd'])
            if 'compress_file_type' in config:
                assert local_saved_filepath.endswith(config['compress_file_type'])

        if 'with_header' in config and bool(config['with_header']):
            header = osutil.remove_header(local_saved_filepath)
            assert header == '\x01'.join([col['name'] for col in ok_dict['table_schema']])
        else:
            assert strutil.md5_file(local_saved_filepath) == strutil.md5_file(local_filepath)
        if 'ok_file' in config and config['ok_file']:
            ok_filename = config['ok_file'] if not config['ok_file'].startswith('.') else (os.path.splitext(config['file_name'])[0] + f".{config['compress_type']}" if 'compress_type' in config and config['compress_type'] else config['file_name']) + config[
                'ok_file']
            local_saved_filepath_ok = f'{local_savedir}/download/{ok_filename}'
            sftp_client.download(f'{remote_dir}/{ok_filename}', local_saved_filepath_ok)
        if 'md5_file' in config and config['md5_file']:
            md5_filename = config['md5_file'] if not config['md5_file'].startswith('.') else (os.path.splitext(config['file_name'])[0] + f".{config['compress_type']}" if 'compress_type' in config and config['compress_type'] else config['file_name']) + config[
                'md5_file']
            local_saved_filepath_md5 = f'{local_savedir}/download/{md5_filename}'
            print(f'=================={remote_dir}/{md5_filename}==========')
            sftp_client.download(f'{remote_dir}/{md5_filename}', local_saved_filepath_md5)
            assert osutil.calls(f'cat {local_saved_filepath_md5}') == strutil.md5_file(local_saved_filepath)
        if 'retlist_file' in config and config['retlist_file']:
            remote_list_file = f'{remote_dir}/MT_FILES.list'
            local_list_file = f'{local_savedir}/download/MT_FILES.list'
            assert sftp_client.exists(remote_list_file)
            sftp_client.download(remote_list_file, local_list_file)
            with open(local_list_file, 'r') as f:
                list_content = f.read()
            print(f'list_content: {list_content}')
            assert os.path.splitext(config['file_name'])[0] + f".{config['compress_type']}" if 'compress_type' in config and config['compress_type'] else config['file_name'] in list_content
        # 构造远程文件名
        filename = os.path.splitext(config["file_name"])[0]
        if "compress_type" in config and config["compress_type"]:
            filename = f"{filename}.{config['compress_type']}"
        else:
            filename = config["file_name"]
        
        # 构造远程文件路径
        if config['file_path'].startswith('/'):
            remote_file = f"{config['file_path']}/{filename}"
        else:
            remote_file = f"/one-sftp-xy-bank/queqiao/test/{config['file_path']}/{filename}"
            
        history_file = FtplinkHistoryFile.get_one(remote_file=remote_file)
        assert history_file
        print(f'get ftplink history file: {history_file.to_dict()}')
        assert history_file.create_user == 'jiangyuande'
        assert history_file.source == 'hive'
        assert history_file.sink == 'ftp'
        assert history_file.local_file == local_filepath
        assert history_file.file_type == config['file_type']
        assert history_file.col_list == 'col1,col2,col3'

    @pytest.mark.parametrize('config', [
        config_package_compress_type1, 
        config_package_compress_type2, 
        config_package_compress_type3
    ])
    def test_package_compress_type(self, config):
        sink = plugins.get('queqiao.plugin.ftplink.sink.ftp')[0](Config(config))
        channel = Channel()
        ok_dict['md5'] = strutil.md5_file(local_filepath)
        channel.set_pipeline_param(local_filepath=local_filepath, ok_dict=ok_dict)
        sink.write(channel)
        # 压缩的部分会删除源文件，补上
        osutil.call(f'echo "9999\x011\x010000\n0000\x012\x019999" > {local_filepath}')

        connect = json.loads(dsn_dit['connect'])
        remote_dir = config['file_path'] if config['file_path'].startswith('/') else \
            connect['work_dir'] + '/' + config['file_path']
        remote_filename = config['file_name'].split('.')[0] + f'.{config["compress_type"]}' if config['compress_type'] else config['file_name']
        remote_filepath = f"{remote_dir}/{remote_filename}"
        assert sftp_client.exists(remote_filepath)
        local_saved_filepath = f'{local_savedir}/download/{remote_filename}'
        sftp_client.download(remote_filepath, local_saved_filepath)

        if 'compress_passwd' not in config:
            local_saved_filepath = fileutil.decompress(local_saved_filepath, config['compress_type'])
        else:
            local_saved_filepath = fileutil.decompress(local_saved_filepath, config['compress_type'],
                                                       config['compress_passwd'], ".xml")

    @pytest.mark.parametrize('config', [
        config_suffix_test1,
        config_suffix_test2
    ])
    def test_compress_suffix(self, config):
        """测试压缩文件后缀处理"""
        print(f"\n=== Testing with config: {json.dumps(config, indent=2)} ===")
        
        sink = plugins.get('queqiao.plugin.ftplink.sink.ftp')[0](Config(config))
        channel = Channel()
        ok_dict['md5'] = strutil.md5_file(local_filepath)
        channel.set_pipeline_param(local_filepath=local_filepath, ok_dict=ok_dict)
        
        print(f"Local file before write: {local_filepath}")
        print(f"Local file exists before write: {osutil.exists(local_filepath)}")
        print(f"Local file content before write: {osutil.calls(f'cat {local_filepath}')}")
        
        sink.write(channel)
        
        # 压缩的部分会删除源文件，补上
        osutil.call(f'echo "9999\x011\x010000\n0000\x012\x019999" > {local_filepath}')

        # 验证远程文件名和路径
        connect = json.loads(dsn_dit['connect'])
        remote_dir = config['file_path'] if config['file_path'].startswith('/') else \
            connect['work_dir'] + '/' + config['file_path']
            
        # 验证上传到FTP的文件名已经更新为压缩格式后缀
        expected_remote_filename = '.'.join(config['file_name'].split('.')[:-1]) + f".{config['compress_type']}"
        remote_filepath = f"{remote_dir}/{expected_remote_filename}"
        
        print(f"\nRemote file check:")
        print(f"Expected remote filename: {expected_remote_filename}")
        print(f"Full remote filepath: {remote_filepath}")
        print(f"Remote dir exists: {sftp_client.exists(remote_dir)}")
        print(f"Remote dir content: {sftp_client.list_dir(remote_dir) if sftp_client.exists(remote_dir) else 'N/A'}")
        
        assert sftp_client.exists(remote_filepath), f"Remote file {remote_filepath} does not exist"
        
        # 下载并解压文件
        local_saved_filepath = f'{local_savedir}/download/{expected_remote_filename}'
        print(f"\nDownloading to: {local_saved_filepath}")
        sftp_client.download(remote_filepath, local_saved_filepath)
        print(f"Downloaded file exists: {osutil.exists(local_saved_filepath)}")
        print(f"Downloaded file size: {os.path.getsize(local_saved_filepath) if osutil.exists(local_saved_filepath) else 'N/A'}")
        
        decompressed_filepath = fileutil.decompress(local_saved_filepath, config['compress_type'])
        print(f"\nDecompressed to: {decompressed_filepath}")
        print(f"Decompressed file exists: {osutil.exists(decompressed_filepath)}")
        print(f"Decompressed file content: {osutil.calls(f'cat {decompressed_filepath}') if osutil.exists(decompressed_filepath) else 'N/A'}")
        
        # 验证解压后的文件名后缀
        expected_decompressed_suffix = config['compress_file_type']
        assert decompressed_filepath.endswith(expected_decompressed_suffix)
        
        # 验证文件内容
        assert strutil.md5_file(decompressed_filepath) == strutil.md5_file(local_filepath)

