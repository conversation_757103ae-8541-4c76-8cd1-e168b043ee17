"""
Author: xiaohei
Date: 2022/9/19
Email: <EMAIL>
Host: xiaohei.info
"""
import json

import pytest

from queqiao.conf import Config
from queqiao.core.engine.base import Channel
from queqiao.plugin import plugins
from queqiao.util.comm import osutil
from queqiao.util.comm.dtutil import timer
from queqiao.util.conn.fate import FateClient
from tests import guest_party_id, host_party_id, guest_server_ip, test_bucket_cnt
from tests.queqiao.plugin.ftplink import execution_dict

intersection_job_conf = {
    "dsl_version": "2",
    "initiator": {
        "role": "guest",
        "party_id": guest_party_id
    },
    "role": {
        "guest": [
            guest_party_id
        ],
        "host": [
            host_party_id
        ]
    },
    "job_parameters": {
        "common": {
            "task_parallelism": 1,
            "computing_partitions": 2,
            "task_cores": 2
        }
    },
    "component_parameters": {
        "common": {
            "intersection_0": {
                "intersect_method": "raw",
                "sync_intersect_ids": True,
                "join_role": "host"
            }
        },
        "role": {
            "guest": {
                "0": {
                    "reader_0": {
                        "table": {
                            "name": "breast_hetero_guest",
                            "namespace": "experiment"
                        }
                    },
                    "data_transform_0": {
                        "with_label": False
                    }
                }
            },
            "host": {
                "0": {
                    "reader_0": {
                        "table": {
                            "name": "breast_hetero_host",
                            "namespace": "experiment"
                        }
                    },
                    "data_transform_0": {
                        "with_label": False
                    }
                }
            }
        }
    }
}
intersection_job_dsl = {
    "components": {
        "reader_0": {
            "module": "Reader",
            "output": {
                "data": [
                    "data"
                ]
            }
        },
        "data_transform_0": {
            "module": "DataTransform",
            "input": {
                "data": {
                    "data": [
                        "reader_0.data"
                    ]
                }
            },
            "output": {
                "data": [
                    "data"
                ],
                "model": [
                    "model"
                ]
            }
        },
        "intersection_0": {
            "module": "Intersection",
            "input": {
                "data": {
                    "data": [
                        "data_transform_0.data"
                    ]
                }
            },
            "output": {
                "data": [
                    "data"
                ]
            }
        }
    }
}

single_config = {
    'current': timer.now().delta(1).date, 'execution': execution_dict,
    'role': 'guest', 'party_id': guest_party_id,
    'bucket_cnt': 1, 'task_bucket_cnt': 1, 'null_format': '', 'partition': 4, 'work_mode': 1,
    'namespace': 'queqiao_test', 'tablename': 'test_fatetask_sink',
    'local_filepath': '/tmp/test_fatetask_sink.csv', 'content': '1,2,NULL,4,5\na,NULL,d,2,1',
    'job_conf': json.dumps(intersection_job_conf), 'job_dsl': json.dumps(intersection_job_dsl), 'validate_rate': None,
    'dsn': {
        'connect': json.dumps({'ip': guest_server_ip, 'port': 9380})
    }
}

bucket_job_conf = json.dumps(intersection_job_conf).replace('breast_hetero_guest', 'breast_hetero_guest_{bucket_id}')
bucket_config = {'bucket_cnt': test_bucket_cnt, 'task_bucket_cnt': test_bucket_cnt, 'job_conf': bucket_job_conf,
                 'validate_rate': 0.9}
bucket_config = dict(single_config, **bucket_config)
content = []
bucket_avg_cnt = 10
for i in range(0, bucket_avg_cnt * 5):
    content.append(f'{i}a,{i}b,{i}c')
bucket_config['content'] = '\n'.join(content)
configs = [
    single_config,
    bucket_config
]
local_ok_dict = {'ok_key1': 'value1', 'ok_key2': 'value2', 'source': 'fate',
                 'local_file': '', 'bytes': 0, 'md5': '123', 'row_num': -1, 'col_cnt': -1, 'ctime': '1',
                 'target_sep': ',', 'table_schema': [
        {'name': 'id', 'type': 'string', 'comment': 'col11'},
        {'name': 'label', 'type': 'int', 'comment': 'col22'},
        {'name': 'f1', 'type': 'string', 'comment': 'col33'},
        {'name': 'f2', 'type': 'string', 'comment': 'col33'}
    ]}


class TestFateTaskSink:
    def setup_class(self):
        self.client = FateClient(guest_server_ip, 9380)
        self.client.open()
        for c in configs:
            local_filepath = c['local_filepath']
            content = c['content']
            osutil.calls(f'echo "{content}" > {local_filepath}')

    def teardown_class(self):
        for c in configs:
            osutil.rm(c['local_filepath'])

    @pytest.mark.parametrize('config', configs)
    def test_write(self, config):
        sink = plugins.get(f'queqiao.plugin.ftplink.sink.fatetask')[0](Config(config))
        channel = Channel()
        local_filepath = config['local_filepath']
        channel.set_pipeline_param(local_filepath=local_filepath, ok_dict=local_ok_dict)
        sink.write(channel)
