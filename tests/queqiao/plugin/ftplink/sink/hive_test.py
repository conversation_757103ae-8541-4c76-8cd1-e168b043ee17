"""
Author: xiaohei
Date: 2022/7/28
Email: <EMAIL>
Host: xiaohei.info
"""

import pytest

from instance.default import system_env
from queqiao.conf import Config, IllegalParamsException
from queqiao.conf.errors import ExecuteFailedException
from queqiao.core.engine.base import Channel
from queqiao.plugin import plugins
from queqiao.util.comm import osutil
from queqiao.util.comm.dtutil import timer
from tests import pyhive_client, hive_dsn
from tests.queqiao.plugin.ftplink import execution_dict

local_savedir = '/tmp/queqiao/pytest/sink/hive'
local_filename = 'hivesink.txt'
local_filepath = f'{local_savedir}/{local_filename}'
ok_dict = {'ok_key1': 'value1', 'ok_key2': 'value2', 'source': 'file',
           'local_file': local_filepath, 'bytes': 0, 'md5': '123', 'row_num': -1, 'col_cnt': -1, 'ctime': '1',
           'table_schema': [
               {'name': 'col1', 'type': 'string', 'comment': 'col11'},
               {'name': 'col2', 'type': 'int', 'comment': 'col22'},
               {'name': 'col3', 'type': 'string', 'comment': 'col33'}
           ]}

content = "9999\x011\x010000\n0000\x012\x019999"
dsn_dit = hive_dsn.to_dict()
# 非分区表
config1 = {
    'table_name': 'hive_sink_test1',
    # none/special
    # 'target_sep': None,
    # none/partition
    # 'table_type': None,
    # none/partition_date,指定了partition_key之后字段列表必须包含partition_key，否则不要指定使用默认分区
    # 'partition_key': None,
    # into/overwrite
    # 'write_mode': None,
    'dsn': dsn_dit,
    'ok_dict': ok_dict,
    'content': content,
    'execution': execution_dict,
    'current': timer.now().delta(1).date,
}

# 分区表-填充日分区
config2 = {
    'table_name': 'hive_sink_test2',
    # none/special
    'target_sep': 'special',
    # none/partition
    'table_type': 'partition',
    # none/partition_date
    'partition_key': None,
    # into/overwrite
    'write_mode': 'overwrite',
    'dsn': dsn_dit,
    'ok_dict': ok_dict,
    'content': content,
    'execution': execution_dict,
    'current': timer.now().delta(1).date,
}

config_err = {
    'table_name': 'hive_sink_error',
    'target_sep': ',',
    'content': '9999,[1,2),0000\n0000,(10,100],9999',
}
config_err = dict(config2, **config_err)

# 分区表-指定字段分区
config3 = {
    'table_name': 'hive_sink_test3',
    # none/special
    'target_sep': '^?',
    # none/partition
    'table_type': 'partition',
    # none/partition_date
    'partition_key': 'partition_month',
    # into/overwrite
    'write_mode': 'into',
    'life_cycle': 93,
    'dsn': dsn_dit,
    'ok_dict': {'ok_key1': 'value1', 'ok_key2': 'value2', 'source': 'file',
                'local_file': local_filepath, 'bytes': 0, 'md5': '123', 'row_num': -1, 'col_cnt': -1, 'ctime': '1',
                'table_schema': [
                    {'name': 'col1', 'type': 'string', 'comment': 'col11'},
                    {'name': 'col2', 'type': 'int', 'comment': 'col22'},
                    {'name': 'col3', 'type': 'string', 'comment': 'col33'},
                    {'name': 'partition_month', 'type': 'string', 'comment': '分区字段'}
                ]},
    'content': "9999^?1^?0000^?2022-06\n0000^?2^?9999^?2022-07",
    'execution': execution_dict,
    'current': timer.now().delta(1).date,
}
config_pk_month_overwrite = {'write_mode': 'overwrite', 'table_name': 'hive_sink_test3_overwrite'}
config_pk_month_overwrite = dict(config3, **config_pk_month_overwrite)

config_with_pk_kv = {'write_mode': 'overwrite', 'table_name': 'hive_sink_test4_with_pk_kv',
                     'partition_key': 'pk_day', 'partition_value': '20230101',
                     'ok_dict': {'ok_key1': 'value1', 'ok_key2': 'value2', 'source': 'file',
                                 'local_file': local_filepath, 'bytes': 0, 'md5': '123', 'row_num': -1, 'col_cnt': -1,
                                 'ctime': '1',
                                 'table_schema': [
                                     {'name': 'col1', 'type': 'string', 'comment': 'col11'},
                                     {'name': 'col2', 'type': 'int', 'comment': 'col22'},
                                     {'name': 'col3', 'type': 'string', 'comment': 'col33'}
                                 ]}}
config_with_pk_kv = dict(config3, **config_with_pk_kv)
config_with_custom_cols = {'write_mode': 'overwrite', 'table_name': 'hive_sink_test4_with_custom_cols',
                           'custom_cols': 'ccc1:vvv1,ccc2:vvv2'}
config_with_custom_cols = dict(config_with_pk_kv, **config_with_custom_cols)
# partition_key不在字段中报错
config4 = {
    'table_name': 'hive_sink_test4',
    'partition_key': 'partition_date',
}
config4 = dict(config2, **config4)

# kinit失效报错
config5 = {
    'table_name': 'hive_sink_test5',
    'kdestroy': True
}
config5 = dict(config2, **config5)


@pytest.mark.skipif(system_env != 'test', reason='test in test env')
@pytest.mark.test
class TestHive:
    def setup_class(self):
        osutil.rm(local_savedir)
        if not osutil.exists(local_savedir):
            osutil.mkdir(local_savedir)

    def teardown_class(self):
        osutil.rm(local_savedir)

    @pytest.mark.parametrize('config', [
        config4,
        config5
    ])
    def test_write_error(self, config):
        osutil.call(f'echo \"{config["content"]}\" > {local_filepath}')
        local_ok_dict = config['ok_dict']

        sink = plugins.get('queqiao.plugin.ftplink.sink.hive')[0](Config(config))
        channel = Channel()
        channel.set_pipeline_param(local_filepath=local_filepath, ok_dict=local_ok_dict)
        if 'kdestroy' in config:
            osutil.call('kdestroy')
        if config['table_name'] == 'hive_sink_test4':
            with pytest.raises(IllegalParamsException) as why:
                sink.write(channel)
            print(f'!!!!!!! {why.value} !!!!!!!')
        else:
            with pytest.raises(ExecuteFailedException) as why:
                sink.write(channel)
            print(f'!!!!!!! {why.value} !!!!!!!')

    @pytest.mark.parametrize('params', [
        (config1, 'hive'),
        (config2, 'hive'),
        (config3, 'hive'),
        (config_err, 'hive'),
        (config1, 'mthive'),
        (config2, 'mthive'),
        (config3, 'mthive'),
        (config_err, 'mthive'),
        (config1, 'tgcbchive'),
        (config2, 'tgcbchive'),
        (config3, 'tgcbchive'),
        (config_pk_month_overwrite, 'mthive'),
        (config_with_pk_kv, 'mthive'),
        (config_with_custom_cols, 'mthive'),
    ])
    def test_write(self, params):
        config, plugin = params
        osutil.call(f'echo \"{config["content"]}\" > {local_filepath}')
        local_ok_dict = config['ok_dict']

        sink = plugins.get(f'queqiao.plugin.ftplink.sink.{plugin}')[0](Config(config))
        channel = Channel()
        channel.set_pipeline_param(local_filepath=local_filepath, ok_dict=local_ok_dict)
        sink.write(channel)

        sql = f'select * from tgdw.{config["table_name"]}'
        query_result = pyhive_client.query(sql)
        print(f'========query_result: {query_result}')
        assert query_result['status'] == 0
        # overwrite 的部分有多个分区，每个分区2
        assert query_result['total'] >= 2
        print(f'===========insert into total count: {query_result["total"]}')
        partition_key = 'partition_date' if 'partition_key' not in config or not config["partition_key"] else config[
            'partition_key']
        table_schema_cc = ok_dict['table_schema']
        if config['table_name'] in ['hive_sink_test2', 'hive_sink_test3', 'hive_sink_error',
                                    'hive_sink_test3_overwrite', 'hive_sink_test4_with_custom_cols',
                                    'hive_sink_test4_with_pk_kv']:
            pcmt = ''
            table_schema_cc = [{'name': 'col1', 'type': 'string', 'comment': 'col11'},
                               {'name': 'col2', 'type': 'int', 'comment': 'col22'},
                               {'name': 'col3', 'type': 'string', 'comment': 'col33'},
                               {'name': f'{partition_key}', 'type': 'string', 'comment': pcmt}]
        if 'custom_cols' in config:
            query_columns = [c for c in query_result['columns'] if c['comment'] != 'user-defind']
            assert query_columns == table_schema_cc
        else:
            assert query_result['columns'] == table_schema_cc
        assert osutil.exists(query_result['file_path'])
        with open(query_result['file_path'], 'r') as f:
            lines = f.readlines()
            print(lines)
            if 'test2' in config['table_name']:
                assert lines[-1].split('\x01')[-1].strip() == timer.now().delta(1).date
                lines1 = ['\x01'.join(l.split('\x01')[:-1]) for l in lines]
                # 只取前2个数据（多个分区）
                assert '\n'.join(lines1[:2]) == config['content']
            elif 'test3' in config['table_name']:
                assert lines[0].split('\x01')[-1].strip() == '2022-06'
            elif 'test4' in config['table_name']:
                if 'kv' in config['table_name']:
                    assert lines[-1].split('\x01')[-1].strip() == '20230101'
                elif 'custom_cols' in config['table_name']:
                    assert lines[-1].split('\x01')[-2].strip() == 'vvv2'
            elif config['table_name'].endswith('error'):
                assert lines[-1].split('\x01')[-1].strip() == timer.now().delta(1).date
                # 收到check_import告警
                assert lines[-1].split('\x01')[-2].strip() == '100]'

        tabletype_sql = f'show create table tgdw.{config["table_name"]}'
        query_result = pyhive_client.query(tabletype_sql)
        with open(query_result['file_path'], 'r') as f:
            lines = f.readlines()
            print(''.join(lines))
            if config['table_name'] in ['hive_sink_test2', 'hive_sink_test3', 'hive_sink_error',
                                        'hive_sink_test3_overwrite', 'hive_sink_test4_with_pk_kv',
                                        'hive_sink_test4_with_custom_cols']:
                assert f'`{partition_key}` string' in \
                       ''.join(lines).replace('\n', '').split('PARTITIONED BY (')[1].split(')')[0].strip()
            else:
                assert 'partitioned by'.upper() not in ''.join(lines).replace('\n', '')

            if 'target_sep' in config and config['target_sep'] is not None and config[
                'target_sep'] != 'special' and len(config['target_sep']) > 1:
                print(f'=======target_sep > 2: {config["target_sep"]}')
                assert config['target_sep'] not in ''.join(lines).replace('\n', '')
                assert 'life_cycle' in ''.join(lines).replace('\n', '')
