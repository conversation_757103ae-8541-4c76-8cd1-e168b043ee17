"""
Author: xiaohei
Date: 2022/7/28
Email: <EMAIL>
Host: xiaohei.info
"""
import pytest

from instance.default import SYS_ADMIN, system_env
from queqiao.conf import Config
from queqiao.conf.system import SystemConfig
from queqiao.core.engine.base import Channel
from queqiao.plugin import plugins
# 分区表-填充日分区
from queqiao.util.comm import osutil
from queqiao.util.comm.dtutil import timer
from tests import pyhive_client, hive_dsn
from tests.queqiao.plugin.ftplink import execution_dict
from tests.queqiao.plugin.ftplink.sink.hive_test import local_filepath, local_savedir

dsn_dit = hive_dsn.to_dict()
SystemConfig.dump('TG_CBC_TL_ONLINE_BANKDW', {'shdw': '0000', 'secshdw': '9999'}, 'Hive默认分区键', SYS_ADMIN)
config = {
    'table_name': 'tgcbchive_sink_test',
    # none/special
    'target_sep': 'special',
    # none/partition
    'table_type': 'partition',
    # none/partition_date
    'partition_key': 'partition_month',
    # into/overwrite
    'write_mode': 'overwrite',
    'dsn': dsn_dit,
    'ok_dict': {'ok_key1': 'value1', 'ok_key2': 'value2', 'source': 'file',
                'local_file': local_filepath, 'bytes': 0, 'md5': '123', 'row_num': -1, 'col_cnt': -1, 'ctime': '1',
                'table_schema': [
                    {'name': 'col1', 'type': 'string', 'comment': 'col11'},
                    {'name': 'col2', 'type': 'int', 'comment': 'col22'},
                    {'name': 'col3', 'type': 'string', 'comment': 'col33'},
                    {'name': 'partition_month', 'type': 'string', 'comment': ''},
                ]},
    'content': "9999\x011\x010000\x012022-06\n0000\x012\x019999\x012022-07",
    'distribute_col': 'col3',
    'execution': execution_dict,
    'current': timer.now().date,
}


@pytest.mark.skipif(system_env != 'test', reason='test in test env')
@pytest.mark.test
class TestTghiveSink:
    def setup_class(self):
        osutil.rm(local_savedir)
        if not osutil.exists(local_savedir):
            osutil.mkdir(local_savedir)

    def teardown_class(self):
        osutil.rm(local_savedir)

    def test_write(self):
        local_ok_dict = config['ok_dict']
        osutil.call(f'echo \"{config["content"]}\" > {local_filepath}')

        sink = plugins.get(f'queqiao.plugin.ftplink.sink.tgcbchive')[0](Config(config))
        channel = Channel()
        channel.set_pipeline_param(local_filepath=local_filepath, ok_dict=local_ok_dict)
        sink.write(channel)

        shdw_sql = f'select * from shdw.{config["table_name"]}'
        shdw_result = pyhive_client.query(shdw_sql)
        print(f'shdw_result: {shdw_result}')
        assert shdw_result['total'] == 1
        shdw_data = osutil.calls(f'cat {shdw_result["file_path"]}').split('\x01')
        assert shdw_data[0] == '9999'
        assert shdw_data[2] == '0000'
        assert osutil.calls(f'cat {shdw_result["file_path"]}') == '9999\x011\x010000\x012022-06'

        secshdw_sql = f'select * from secshdw.{config["table_name"]}'
        secshdw_result = pyhive_client.query(secshdw_sql)
        print(f'secshdw_result: {secshdw_result}')
        assert secshdw_result['total'] == 1
        secshdw_data = osutil.calls(f'cat {secshdw_result["file_path"]}').split('\x01')
        assert secshdw_data[0] == '0000'
        assert secshdw_data[2] == '9999'
        assert osutil.calls(f'cat {secshdw_result["file_path"]}') == '0000\x012\x019999\x012022-07'
