"""
Author: xiaohei
Date: 2022/9/19
Email: <EMAIL>
Host: xiaohei.info
"""
import json
import time

import pytest

from queqiao.conf import Config, IllegalParamsException
from queqiao.core.engine.base import Channel
from queqiao.dba import db
from queqiao.dba.models import Execution
from queqiao.plugin import plugins
from queqiao.util.comm import osutil
from queqiao.util.comm.dtutil import timer
from queqiao.util.conn.fate import FateClient
from tests import test_client
from tests.queqiao.plugin.ftplink import execution_dict

header = 'col1,col2,col3,col4,col5'
config_temp = {
    'current': timer.now().delta(1).date, 'execution': execution_dict,
    'role': 'guest', 'party_id': 9999,
    'namespace': 'queqiao_test', 'target_sep': ',', 'partition': 8, 'null_format': '', 'work_mode': 1,
    'dsn': {
        'connect': json.dumps({'ip': '**************', 'port': 9380})
    }
}
config1 = {'bucket_cnt': -1, 'null_format': '', 'tablename': 'test_fate_sink_0_nullformat_empty', 'cols': header,
           'local_filepath': '/tmp/test_fate_sink_with_header.csv', 'content': '1,2,NULL,4,5'}
config2 = {'bucket_cnt': 0, 'null_format': 'N', 'tablename': 'test_fate_sink_0_nullformat_N', 'cols': 'col2,col3',
           'local_filepath': '/tmp/test_fate_sink.csv', 'content': '2,NULL'}
# 没配置分隔符和key，执行异常
# config3 = {'bucket_cnt': 10, 'tablename': 'test_fate_sink_10_nullformat_NULL_error', 'cols': 'col1,col2,col3',
#            'key_idx': None, 'content': '1,2,3',
#            'local_filepath': '/tmp/test_fate_sink_error.csv'}
# bucket_cnt
config4 = {'bucket_cnt': 10, 'tablename': 'test_fate_sink_10_nullformat_NULL_0', 'cols': 'col2,col3,col4',
           'key_idx': 0, 'target_sep': ',',
           'local_filepath': '/tmp/test_fate_sink_10.csv'}
content4 = []
bucket_avg_cnt = 10
for i in range(0, bucket_avg_cnt * 10):
    content4.append(f'{i}a,{i}b,{i}c')
config4['content'] = '\n'.join(content4)
config5 = {'bucket_cnt': 10, 'tablename': 'test_fate_sink_10_nullformat_NULL_3', 'cols': 'col1\x01col2\x01col3',
           'target_sep': 'special', 'key_idx': 2, 'local_filepath': '/tmp/test_fate_sink_10_sep.csv'}
content5 = []
for i in range(0, bucket_avg_cnt * 10):
    content5.append(f'{i}a\x01{i}b\x01{i}c')
config5['content'] = '\n'.join(content5)
# bucket_col
content6 = []
content7 = []
enum = ['x', 'y', 'z']
for i in range(0, bucket_avg_cnt * 10):
    content6.append(f'{i}a\x01{i}b\x01{i}c\x01{enum[i % len(enum)]}')
    content7.append(f'{i}a,{i}b,{i}c,x')
config6 = {'bucket_cnt': 3, 'bucket_col': 'cole', 'tablename': 'test_fate_sink_10_bucket_col',
           'cols': 'col1\x01col2\x01col3\x01cole', 'target_sep': 'special', 'key_idx': 2,
           'local_filepath': '/tmp/test_fate_sink_10_bucket_col.csv', 'content': '\n'.join(content6)}
config7 = {'bucket_cnt': 1, 'bucket_col': 'cole', 'tablename': 'test_fate_sink_10_bucket_col1',
           'cols': 'col1,col2,col3,cole', 'target_sep': ',', 'key_idx': 2,
           'local_filepath': '/tmp/test_fate_sink_10_bucket_col1.csv', 'content': '\n'.join(content7)}
configs = [
    # config1,
    # config2,
    # config4,
    # config5,
    config6,
    config7
]
local_ok_dict = {'ok_key1': 'value1', 'ok_key2': 'value2', 'source': 'fate',
                 'local_file': '', 'bytes': 0, 'md5': '123', 'row_num': -1, 'col_cnt': -1, 'ctime': '1',
                 'table_schema': [
                     {'name': 'col1', 'type': 'string', 'comment': 'col11'},
                     {'name': 'col2', 'type': 'int', 'comment': 'col22'},
                     {'name': 'col3', 'type': 'string', 'comment': 'col33'}
                 ]}


class TestFateSink:
    def setup_class(self):
        self.client = FateClient('**************', 9380)
        self.client.open()
        for c in configs:
            local_filepath = c['local_filepath']
            content = c['content']
            osutil.calls(f'echo "{content}" > {local_filepath}')

    # def teardown_class(self):
    #     for c in configs:
    #         osutil.rm(c['local_filepath'])

    @pytest.mark.parametrize('config', configs)
    def test_write(self, config):
        config = dict(config_temp, **config)
        sink = plugins.get(f'queqiao.plugin.ftplink.sink.fate')[0](Config(config))
        channel = Channel()
        local_filepath = config['local_filepath']

        ok_dict = local_ok_dict.copy()
        target_sep = config['target_sep']
        target_sep = '\x01' if target_sep == 'special' else target_sep
        ok_dict['table_schema'] = [{'name': c, 'type': 'string', 'comment': ''} for c in
                                   config['cols'].split(target_sep)]
        ok_dict['target_sep'] = target_sep
        channel.set_pipeline_param(local_filepath=local_filepath, ok_dict=ok_dict)
        sink.write(channel)

        content = config['content'].replace('NULL', config['null_format'])

        if config['bucket_cnt'] <= 1:
            assert config['cols'] + '\n' + content == '\n'.join(osutil.calls(f'cat {local_filepath}'))
            data = self.client.table_info(config['namespace'], config['tablename'])
            data = data['data']
            assert data['count'] == osutil.wc(local_filepath) - 1
            assert data['exist'] == 1
            assert data['schema']['header'] in config['cols']
        else:
            target_sep = '\x01' if config['target_sep'] == 'special' else config['target_sep']
            content = content if config['target_sep'] == ',' else content.replace(target_sep, ',')
            cols = config['cols'] if config['target_sep'] == ',' else config['cols'].replace(target_sep, ',')
            assert content == '\n'.join(osutil.calls(f'cat {local_filepath}'))
            data = self.client.table_info(config['namespace'], config['tablename'])
            data = data['data']
            assert data['exist'] == 0
            bucket_list = [i for i in range(0, config['bucket_cnt'])] if 'bucket_col' not in config else enum
            for bucket_id in bucket_list:
                data = self.client.table_info(config['namespace'], f"{config['tablename']}_{bucket_id}")
                data = data['data']
                assert data['exist'] == 1
                assert data['schema']['header'] in cols
                print(f'bucket id: {bucket_id}, file count: {data["count"]}')
        execution = Execution.get(id=execution_dict["id"])
        db.session.refresh(execution)
        resp = test_client.get(f'/api/v1/execution/status/{execution_dict["id"]}')
        print(resp.json)
        message = json.loads(execution.message)
        assert len(message) == config['bucket_cnt']
        for i in message:
            data = self.client.table_info(i['namespace'], i['tablename'])
            data = data['data']
            assert data['exist'] == 1

    # @pytest.mark.parametrize('config', [config3])
    # def test_write_error(self, config):
    #     config = dict(config_temp, **config)
    #     sink = plugins.get(f'queqiao.plugin.ftplink.sink.fate')[0](Config(config))
    #     channel = Channel()
    #     local_filepath = config['local_filepath']
    #
    #     ok_dict = local_ok_dict.copy()
    #     target_sep = config.pop('target_sep')
    #     target_sep = '\x01' if target_sep == 'special' else target_sep
    #     ok_dict['table_schema'] = [{'name': c, 'type': 'string', 'comment': ''} for c in
    #                                config.pop('cols').split(target_sep)]
    #     ok_dict['target_sep'] = target_sep
    #
    #     channel.set_pipeline_param(local_filepath=local_filepath, ok_dict=ok_dict)
    #     with pytest.raises(IllegalParamsException) as why:
    #         sink.write(channel)
    #         print(why.value)
