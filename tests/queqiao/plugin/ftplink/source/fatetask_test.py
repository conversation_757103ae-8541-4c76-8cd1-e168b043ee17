"""
Author: xiaohei
Date: 2022/9/19
Email: <EMAIL>
Host: xiaohei.info
"""
import json

import pytest

from queqiao.conf import Config
from queqiao.core.engine.base import Channel
from queqiao.plugin import plugins
from queqiao.util.comm import osutil
from queqiao.util.comm.dtutil import timer
from tests import predict_job_conf, predict_job_dsl, guest_server_ip, predict_component_name, test_bucket_cnt
from tests.queqiao.plugin.ftplink import execution_dict

single_config = {
    'current': timer.now().delta(1).date, 'execution': execution_dict,
    'role': 'guest', 'party_id': 10000,
    'cols': 'id,predict_score',
    'component_name': predict_component_name,
    'bucket_cnt': 1, 'job_conf': json.dumps(predict_job_conf), 'job_dsl': json.dumps(predict_job_dsl),
    'dsn': {
        'connect': json.dumps({'ip': guest_server_ip, 'port': 9380})
    }
}

bucket_job_conf = json.dumps(predict_job_conf).replace('breast_hetero_guest',
                                                       'breast_hetero_guest_{bucket_id}').replace(
    'breast_hetero_host', 'breast_hetero_host_{bucket_id}')
bucket_config = {'bucket_cnt': test_bucket_cnt, 'job_conf': bucket_job_conf}
bucket_config = dict(single_config, **bucket_config)


class TestFateTask:

    @pytest.mark.parametrize('config', [single_config, bucket_config])
    def test_read(self, config):
        source = plugins.get('queqiao.plugin.ftplink.source.fatetask')[0](Config(config))
        channel = Channel()
        source.read(channel)
        assert 'ok_dict' in channel.get_pipeline_keys()
        print('ok_dict:')
        ok_dict = channel.get_pipeline_param('ok_dict')
        print(json.dumps(ok_dict))
        assert 'local_filepath' in channel.get_pipeline_keys()
        local_filepath = channel.get_pipeline_param("local_filepath")
        print(f'local_filepath: {local_filepath}')
        assert osutil.exists(local_filepath)
