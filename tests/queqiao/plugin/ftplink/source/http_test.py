"""
Author: xiaohei
Date: 2023/1/13
Email: <EMAIL>
Host: xiaohei.info
"""
import json

import pytest

from queqiao.conf import Config
from queqiao.core.engine.base import Channel
from queqiao.plugin import plugins
from queqiao.util.comm.dtutil import timer
from tests.queqiao.plugin.ftplink import execution_dict

http_source_config = {
    'url': 'http://msstest.vip.sankuai.com/tgdap-test/chentianzeng/upload_sample_2.csv',
    'current': timer.now().delta(1).date, 'execution': execution_dict,
    'has_header': True, 'target_sep': ','}
update_source_config = {'params': 'k1:v2,k2:v2'}


class TestHttp:
    @pytest.mark.parametrize('config',
                             [
                                 Config(http_source_config),
                                 Config(dict(http_source_config, **update_source_config)),
                             ])
    def test_read(self, config):
        source = plugins.get('queqiao.plugin.ftplink.source.http')[0](config)
        channel = Channel()
        source.read(channel)
        assert 'ok_dict' in channel.get_pipeline_keys()
        print('ok_dict:')
        ok_dict = channel.get_pipeline_param('ok_dict')
        print(json.dumps(ok_dict))
        assert ok_dict['row_num'] == 1132
        assert 'local_filepath' in channel.get_pipeline_keys()
        local_filepath = channel.get_pipeline_param("local_filepath")
        print(f'local_filepath: {local_filepath}')
