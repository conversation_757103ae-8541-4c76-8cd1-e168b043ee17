"""
Author: xiaohei
Date: 2022/7/27
Email: <EMAIL>
Host: xiaohei.info
"""
import json

import pytest
from pytalos import TalosException

from queqiao.conf import Config
from queqiao.core.engine.base import Channel
from queqiao.plugin import plugins
from queqiao.util.comm import strutil
from queqiao.util.comm.dtutil import timer
from tests import talos_dsn, talos_dsn_with_passwd
from tests.queqiao.plugin.ftplink import execution_dict

test_tablename = 'mart_fspinno_queqiao.pytest'
dsn_dit = talos_dsn_with_passwd.to_dict()
config1 = {
    'sql': f'select id,name from {test_tablename}',
    'table': f'from_pytest_{timer.now().timestamp}',
    'dsn': dsn_dit,
    'execution': dict(execution_dict, **{'task_name': 'test.config1'}),
    'current': timer.now().date,
}


class TestTalos:

    @pytest.mark.parametrize('config', [config1])
    def test_read(self, config):
        source = plugins.get('queqiao.plugin.ftplink.source.talosctas')[0](Config(config))
        channel = Channel()
        source.read(channel)

        assert 'ok_dict' in channel.get_pipeline_keys()
        print('ok_dict:')
        ok_dict = channel.get_pipeline_param('ok_dict')
        print(json.dumps(ok_dict))
        assert 'local_filepath' in channel.get_pipeline_keys()
        local_filepath = channel.get_pipeline_param("local_filepath")
        print(f'local_filepath: {local_filepath}')
        with open(local_filepath, 'r') as f:
            content = json.loads("\n".join(f.readlines()))
        print(f'ctas content: {content}')
        assert content['total'] == 2
