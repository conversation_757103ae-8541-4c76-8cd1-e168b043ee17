
"""
Author: xiaohei
Date: 2022/7/26
Email: <EMAIL>
Host: xiaohei.info
"""
import json
import os
import time
from concurrent.futures.thread import ThreadPoolExecutor

import pytest

from queqiao.conf import Config
from queqiao.core.engine.base import Channel
from queqiao.dba.extend_model import Alarm
from queqiao.dba.models import FtplinkHistoryFile
from queqiao.plugin import plugins
from queqiao.util.comm import osutil, strutil, fileutil
from queqiao.util.comm.dtutil import timer
from queqiao.util.conn.ftp import RemoteFileServer
from tests import sftp_client, ftp_dsn, meituan_org, tonglian_org
from tests.queqiao.plugin.ftplink import execution_dict

ftp_test_path = '/one-sftp-xy-bank/queqiao/test'
ok_file_params = [None, '.ok', 'demo1.ready']
dsn_dit = ftp_dsn.to_dict()
txt_config_dict1 = {
    'file_path': ftp_test_path,
    'file_name': 'txt_demo1.txt',
    # 有/无/.ok/xxx.ready -> check_file
    # json-meta检查/非json
    # 有table_schema、无table_schema
    # 'ok_file': None,
    # alarm
    'lastest_ready_time': timer.now().hourmin,
    'alarm_receivers': 'jiangyuande',
    # exception
    'max_waiting_hours': '1',
    # loop
    'ftp_polling_sec': '3',
    'target_sep': ',',
    # 有/无 -> table_cols
    'has_header': False,
    # 有/无
    'table_cols': 'col1,col2,col3',
    # zip/tar.gz/7z
    # 压缩包文件同名/不同名
    # 压缩包内单个/多个
    # 'compress_type': None,
    # 'compress_passwd': None,
    # 无/有+无his/有+有his
    # 多磁盘
    'execution': dict(execution_dict, **{'task_name': 'test.txt_config_dict1'}),
    'dsn': dsn_dit,
    'demo': '9999,1,0000\n0000,2,9999',
    'ok_demo': 'hhh ok content',
    'current': timer.now().date,
}

txt_config_dict2 = {
    'file_name': 'txt_demo2.txt',
    'ok_file': '.ok',
    'target_sep': '\x01',
    'has_header': True,
    # 'table_cols': None,
    'demo': 'h1\x01h2\x01h3\n9999\x011\x010000\n0000\x012\x019999',
    'execution': dict(execution_dict, **{'task_name': 'test.txt_config_dict2'}),
}
txt_config_dict2 = dict(txt_config_dict1, **txt_config_dict2)

txt_config_dict_conflict = {
    'file_name': 'txt_demo_conflict.txt',
    # 'ok_file': None,
    'target_sep': ',',
    # 'has_header': None,
    'table_cols': 'col1,col2,col3',
    'conflict_sep': True,
    'demo': '9999,[1,2),0000\n0000,(10,100],9999',
    'execution': dict(execution_dict, **{'task_name': 'test.txt_config_dict_conflict'}),
}
txt_config_dict_conflict = dict(txt_config_dict1, **txt_config_dict2)

txt_config_dict3 = {
    'file_name': 'txt_demo3.txt',
    'ok_file': 'demo3hh.ready',
    'table_cols': 'cc11,cc22,cc33',
    'ok_demo': json.dumps(
        {'md5': 'md5hhh', 'bytes': 0, 'ok_key1': 'value1', 'ok_key2': 'value2'}),
    'execution': dict(execution_dict, **{'task_name': 'test.txt_config_dict3'}),
}
txt_config_dict3 = dict(txt_config_dict2, **txt_config_dict3)

txt_config_matches = {
    'file_name': 'txt_demo_*.txt',
    'ok_file': 'txt_demo.txt.ok',
    'execution': dict(execution_dict, **{'task_name': 'test.txt_config_matches'}),
    'number_files': 10
}
txt_config_matches = dict(txt_config_dict1, **txt_config_matches)

txt_config_list = {
    'file_name': 'txt_demo_2.txt,txt_demo_3.txt',
    'ok_file': None,
    'has_header': False,
    'execution': dict(execution_dict, **{'task_name': 'test.txt_config_list'}),
    'number_files': 2
}
txt_config_list = dict(txt_config_dict2, **txt_config_list)

zip_config_dict = {
    'file_name': 'zip_demo1.zip',
    'compress_type': 'zip',
    'compress_passwd': 'password',
    'execution': dict(execution_dict, **{'task_name': 'test.txt_config_dict4'}),
}
zip_config_dict = dict(txt_config_dict1, **zip_config_dict)
zip_config_dict_row_sep = {
    'file_name': 'CUST_ACCOUNT_INFO_20240816_000*.zip',
    'execution': dict(execution_dict, **{'task_name': 'test.txt_config_dict4'}),
    'row_sep_from': '|-|\n',
    'row_sep_to': '\n',
    "compress_type": 'zip',
    "compress_merge_suffix": '.xml',
    "compress_with_ok_file": True
}
zip_config_dict_row_sep = dict(txt_config_dict1, **zip_config_dict_row_sep)
zip_config_matches = {
    'file_name': 'zip_demo_*.zip',
    'ok_file': 'zip_demo.zip.ok',
    'execution': dict(execution_dict, **{'task_name': 'test.zip_config_matches'}),
    'number_files': 5
}
zip_config_matches = dict(zip_config_dict, **zip_config_matches)

z7_config_dict = {
    'file_name': '7z_demo1.7z',
    'compress_type': '7z',
    'compress_passwd': 'password',
    'execution': dict(execution_dict, **{'task_name': 'test.z7_config_dict'}),
}
z7_config_dict = dict(zip_config_dict, **z7_config_dict)

targz_config_dict = {
    'file_name': 'targz_demo1.tar.gz',
    'compress_type': 'tar.gz',
    # 'compress_passwd': None,
    'execution': dict(execution_dict, **{'task_name': 'test.targz_config_dict'}),
}
targz_config_dict = dict(zip_config_dict, **targz_config_dict)

wait_config_dict1 = {
    'file_name': 'wait_demo1.txt',
    'ok_file': 'wait4.ready',
    'ok_demo': 'sleep 10',
    'do_not_upload_ok': 10,
    'execution': dict(execution_dict, **{'task_name': 'test.wait_config_dict'}),
}
wait_config_dict1 = dict(txt_config_dict2, **wait_config_dict1)

txt_config_dict_re_target_sep = {
    'file_name': 'txt_demo_re_target_sep.txt',
    'target_sep': ',',
    'table_cols': 'col1,col2,col3',
    're_target_sep': '|',
    'demo': '9999,1,0000\n0000,2,9999',
    'execution': dict(execution_dict, **{'task_name': 'test.txt_config_dict_re_target_sep'}),
}
txt_config_dict_re_target_sep = dict(txt_config_dict1, **txt_config_dict_re_target_sep)

# 添加多字符分隔符测试配置
txt_config_dict_multi_sep = {
    'file_name': 'txt_demo_multi_sep.txt',
    'target_sep': '||',
    'table_cols': 'col1,col2,col3',
    're_target_sep': '###',
    'demo': '9999||1||0000\n0000||2||9999',
    'execution': dict(execution_dict, **{'task_name': 'test.txt_config_dict_multi_sep'}),
}
txt_config_dict_multi_sep = dict(txt_config_dict1, **txt_config_dict_multi_sep)

configs = [txt_config_dict1, txt_config_dict2, txt_config_dict3, zip_config_dict, z7_config_dict, targz_config_dict,
           wait_config_dict1, txt_config_dict_conflict, txt_config_dict_re_target_sep, txt_config_dict_multi_sep]
match_configs = [txt_config_matches, zip_config_matches]
executor = ThreadPoolExecutor(max_workers=2)


def upload_ok_after_sec(ok_file):
    # 前面的几个测试需要10s，如果增加testcase需要增加时间
    seconds = 30
    print(f'will upload ok file after {seconds} seconds')
    time.sleep(seconds)
    osutil.call(f'echo "sleep 10" > /tmp/{ok_file}')
    sftp = RemoteFileServer.get_connect(json.loads(dsn_dit['connect']))
    sftp.open()
    sftp.upload(f'/tmp/{ok_file}', f'{ftp_test_path}/{ok_file}')
    sftp.close()
    print(f'upload /tmp/{ok_file} to {ftp_test_path}/{ok_file}')
    if osutil.exists(f'/tmp/{ok_file}'):
        osutil.rm(f'/tmp/{ok_file}')


class TestFtp:
    def setup_class(self):
        self.clean(self)
        if not sftp_client.exists(ftp_test_path):
            sftp_client.client.mkdir(ftp_test_path)

        for config in configs:
            demo = config['demo']
            file_name = config['file_name']
            tmp_file_name = f'/tmp/{file_name}' if file_name.endswith('.txt') else f'/tmp/{file_name.split(".")[0]}.txt'
            target_file_name = f'{ftp_test_path}/{file_name}'
            with open(tmp_file_name, 'a') as tmp:
                for d in demo.split('\n'):
                    tmp.write(d + '\n')
            if 'compress_type' in config and config['compress_type']:
                compress_type = config['compress_type']
                compress_passwd = config['compress_passwd']
                compress_file = fileutil.compress(tmp_file_name, tmp_file_name.replace('.txt', f'.{compress_type}'),
                                                  compress_type, compress_passwd)
                tmp_file_name = compress_file
            sftp_client.upload(tmp_file_name, target_file_name)
            # if 'txt_demo1.txt' == config['file_name']:
            #     self.test1_create_time = sftp_client.stat(target_file_name).st_mtime
            if 'ok_file' in config and config['ok_file']:
                if 'do_not_upload_ok' in config:
                    executor.submit(upload_ok_after_sec, config['ok_file'])
                else:
                    ok_file = config['ok_file']
                    ok_file_name = f'{file_name}{ok_file}' if ok_file.startswith('.') else ok_file
                    tmp_ok_file = f'/tmp/{ok_file_name}'
                    target_ok_file = f'{ftp_test_path}/{ok_file_name}'
                    with open(tmp_ok_file, 'w') as tmp_ok:
                        tmp_ok.write(config['ok_demo'] + '\n')
                    sftp_client.upload(tmp_ok_file, target_ok_file)
        for config in match_configs:
            demo = config['demo']
            file_name = config['file_name']
            number_files = config['number_files']
            for i in range(0, number_files):
                tmp_file_name = f"/tmp/{file_name.replace('*', str(i)).replace('.zip', '.txt')}"
                target_file_name = f'{ftp_test_path}/{file_name.replace("*", str(i))}'
                with open(tmp_file_name, 'a') as tmp:
                    for d in demo.split('\n'):
                        tmp.write(d + '\n')
                if 'compress_type' in config and config['compress_type']:
                    compress_type = config['compress_type']
                    compress_passwd = config['compress_passwd']
                    compress_file = fileutil.compress(tmp_file_name, tmp_file_name.replace('.txt', f'.{compress_type}'),
                                                      compress_type, compress_passwd)
                    tmp_file_name = compress_file
                sftp_client.upload(tmp_file_name, target_file_name)
            if 'ok_file' in config and config['ok_file']:
                ok_file = config['ok_file']
                ok_file_name = f'{file_name}{ok_file}' if ok_file.startswith('.') else ok_file
                tmp_ok_file = f'/tmp/{ok_file_name}'
                target_ok_file = f'{ftp_test_path}/{ok_file_name}'
                with open(tmp_ok_file, 'w') as tmp_ok:
                    tmp_ok.write(config['ok_demo'] + '\n')
                sftp_client.upload(tmp_ok_file, target_ok_file)

    def teardown_class(self):
        self.clean(self)

    def clean(self):
        all_configs = configs + match_configs + [txt_config_list]
        for config in all_configs:
            if 'file_path'  not in config:
                print(f'!!!!!!!clean config: {config}')
            f = f'{config["file_path"]}/{config["file_name"]}'
            if '*' in f:
                files = sftp_client.match_files(config['file_path'], config['file_name'])
                for file in files:
                    sftp_client.delete(file)
                    osutil.rm(f'/tmp/{file.split("/")[-1]}')
                    if '.zip' in file:
                        osutil.rm(f'/tmp/{file.split("/")[-1].replace(".zip", ".txt")}')
            else:
                if sftp_client.exists(f):
                    sftp_client.delete(f)
            file_name = config['file_name']
            tmp_file = f'/tmp/{file_name}' if file_name.endswith('.txt') else f'/tmp/{file_name.split(".")[0]}.txt'
            if osutil.exists(tmp_file):
                osutil.rm(tmp_file)
            if 'ok_file' in config and config['ok_file']:
                ok_file = config['ok_file']
                ok_file_name = f'{file_name}{ok_file}' if ok_file.startswith('.') else ok_file
                tmp_ok_file = f'/tmp/{ok_file_name}'
                target_ok_file = f'{ftp_test_path}/{ok_file_name}'
                if sftp_client.exists(target_ok_file):
                    sftp_client.delete(target_ok_file)
                if osutil.exists(tmp_ok_file):
                    osutil.rm(tmp_ok_file)
        Alarm.delete_by()
        FtplinkHistoryFile.delete_by()

    @pytest.mark.parametrize('config', configs)
    def test_read(self, config):
        source = plugins.get('queqiao.plugin.ftplink.source.ftp')[0](Config(config))
        channel = Channel()
        source.read(channel)
        local_filepath = channel.get_pipeline_param('local_filepath')
        ok_dict = channel.get_pipeline_param('ok_dict')
        print(f"ok_dict: {json.dumps(ok_dict)}")
        print(f"local_filepath: {local_filepath}")
        # 数据文件内容
        assert osutil.exists(local_filepath)
        data_cnt = len(config['demo'].split('\n')) if not config['has_header'] else len(config['demo'].split('\n')) - 1
        assert osutil.wc(local_filepath) == data_cnt
        data = config['demo'].split('\n') if not config['has_header'] else config['demo'].split('\n')[1:]
        if 'conflict_sep' not in config:
            assert osutil.calls(f'cat {local_filepath}') == data
        else:
            assert osutil.calls(f'cat {local_filepath}') != data

        # ok_dict check
        tmp_file = f'/tmp/{config["file_name"]}'
        if not config['has_header']:
            if not tmp_file.endswith('.txt'):
                tmp_file = fileutil.decompress(tmp_file, config['compress_type'], config['compress_passwd'])
            assert ok_dict['bytes'] == osutil.bytes(tmp_file)
            assert ok_dict['md5'] == strutil.md5_file(tmp_file)
        target_sep = config['target_sep'] if 'conflict_sep' not in config else '\x01'
        assert ok_dict['col_cnt'] == osutil.nf(tmp_file, target_sep)
        assert ok_dict['row_num'] == data_cnt
        assert ok_dict['col1_max'] == '9999'
        assert ok_dict['coln_min'] == '0000'
        assert ok_dict['coln_len_num'] == 1
        table_schema = ok_dict['table_schema']
        table_cols = [col['name'] for col in table_schema]
        config_table_cols = config['table_cols'].split(',') if config['table_cols'] else \
            config['demo'].split('\n')[0].split(
                config['target_sep'])
        print(f'=======has_header: {config["has_header"]}, table_cols: {config["table_cols"]}, '
              f'final table_cols: {table_cols}')
        assert table_cols == config_table_cols
        if config['ok_demo'].startswith('{'):
            ok_demo_dict = json.loads(config['ok_demo'])
            for key in ok_demo_dict.keys():
                assert key in ok_dict
                assert ok_demo_dict[key] == ok_dict[key]
        elif 'ok_file' in config and config['ok_file']:
            assert ok_dict['ok_content'].strip() == config['ok_demo']
        assert ok_dict['remote_file'] == f'{ftp_test_path}/{config["file_name"]}'
        assert ok_dict['source'] == 'ftp'
        if config['file_name'] == 'txt_demo1.txt':
            history_file = FtplinkHistoryFile.new(
                create_user='jiangyuande',
                source=ok_dict.pop('source'),
                sink='test',
                local_file=ok_dict.pop('local_file'),
                bytes=int(ok_dict.pop('bytes')),
                md5=ok_dict.pop('md5'),
                row_num=int(ok_dict.pop('row_num')),
                col_num=int(ok_dict.pop('col_cnt')),
                ctime=ok_dict.pop('ctime'),
                execution_id=1,
                task_id=1,
                project_id=1,
                source_org_id=meituan_org.id,
                sink_org_id=tonglian_org.id
            )
            history_file.remote_file = None if 'remote_file' not in ok_dict else ok_dict.pop('remote_file')
            check_file_type = history_file.remote_file if history_file.source == 'ftp' or history_file.sink == 'ftp' \
                else history_file.local_file
            history_file.file_type = ".".join(check_file_type.split('.')[1:])
            table_schema = ok_dict.pop('table_schema')
            history_file.col_list = ','.join([col['name'] for col in table_schema])
            history_file.check_meta = json.dumps(ok_dict)
            history_file.save()
            assert history_file.file_type == 'txt'
            assert history_file.col_list == 'col1,col2,col3'
            print(f'history_file: {json.dumps(history_file.to_dict())}')
        if 'conflict_sep' in config:
            origin_content = osutil.calls(f'cat {tmp_file}')
            source_content = osutil.calls(f'cat {local_filepath}')
            print(f'origin content line1: {origin_content[0]}, source_content line1: {source_content[0]}')
            origin_content_line1_len = len(origin_content[0].split(config['target_sep']))
            source_content_line1_len = len(source_content[0].split(config['target_sep']))
            source_content_line1_len1 = len(source_content[0].split('\x01'))
            assert origin_content_line1_len != source_content_line1_len
            assert origin_content_line1_len == 4
            assert source_content_line1_len == 1
            assert source_content_line1_len1 == 3
            assert origin_content[0].split(config['target_sep'])[-1] == source_content[0].split('\x01')[-1]

    @pytest.mark.parametrize('config', match_configs)
    def test_match_read(self, config):
        source = plugins.get('queqiao.plugin.ftplink.source.ftp')[0](Config(config))
        channel = Channel()
        source.read(channel)
        local_filepath = channel.get_pipeline_param('local_filepath')
        ok_dict = channel.get_pipeline_param('ok_dict')
        print(f"ok_dict: {json.dumps(ok_dict)}")
        print(f"local_filepath: {local_filepath}")
        number_files = config['number_files']
        # 每个文件2行
        number_content_per_file = 2
        total_count = number_files * number_content_per_file
        assert total_count == osutil.wc(local_filepath)

    @pytest.mark.parametrize('config', [txt_config_list])
    def test_list_read(self, config):
        source = plugins.get('queqiao.plugin.ftplink.source.ftp')[0](Config(config))
        channel = Channel()
        source.read(channel)
        local_filepath = channel.get_pipeline_param('local_filepath')
        ok_dict = channel.get_pipeline_param('ok_dict')
        print(f"ok_dict: {json.dumps(ok_dict)}")
        print(f"local_filepath: {local_filepath}")
        number_files = config['number_files']
        # 每个文件3行
        number_content_per_file = 2
        total_count = number_files * number_content_per_file
        assert total_count == osutil.wc(local_filepath)

    @pytest.mark.parametrize('config', [zip_config_dict_row_sep])
    def test_row_sep_replace(self, config):
        source = plugins.get('queqiao.plugin.ftplink.source.ftp')[0](Config(config))
        channel = Channel()
        source.read(channel)
        local_filepath = channel.get_pipeline_param('local_filepath')
        ok_dict = channel.get_pipeline_param('ok_dict')
        print(f"ok_dict: {json.dumps(ok_dict)}")
        print(f"local_filepath: {local_filepath}")
        # 数据文件内容
        assert osutil.exists(local_filepath)
        data_cnt = len(config['demo'].split('\n')) if not config['has_header'] else len(config['demo'].split('\n')) - 1

    @pytest.mark.parametrize('config', [txt_config_dict_conflict])
    def test_conflict_sep_replacement(self, config):
        """测试使用conflict_sep替换列分隔符为\x01的情况"""
        source = plugins.get('queqiao.plugin.ftplink.source.ftp')[0](Config(config))
        channel = Channel()
        source.read(channel)
        local_filepath = channel.get_pipeline_param('local_filepath')
        
        # 验证分隔符已被替换为\x01
        with open(local_filepath, 'r') as f:
            content = f.read()
            lines = content.split('\n')
            for line in lines:
                if line:  # 跳过空行
                    # 检查原始分隔符','不再存在
                    assert ',' not in line
                    # 检查字段是否被正确分割
                    fields = line.split('\x01')
                    assert len(fields) == 3  # 应该有3个字段
                    
        # 清理测试文件
        if os.path.exists(local_filepath):
            os.remove(local_filepath)
            
    @pytest.mark.parametrize('config', [txt_config_dict_re_target_sep])
    def test_re_target_sep_replacement(self, config):
        """测试使用re_target_sep替换列分隔符的情况"""
        source = plugins.get('queqiao.plugin.ftplink.source.ftp')[0](Config(config))
        channel = Channel()
        source.read(channel)
        local_filepath = channel.get_pipeline_param('local_filepath')
        
        # 验证分隔符已被替换为'|'
        with open(local_filepath, 'r') as f:
            content = f.read()
            lines = content.split('\n')
            for line in lines:
                if line:  # 跳过空行
                    # 检查原始分隔符','不再存在
                    assert ',' not in line
                    # 检查字段是否被正确分割
                    fields = line.split('|')
                    assert len(fields) == 3  # 应该有3个字段
                    # 验证字段值
                    assert fields[0] in ['9999', '0000']
                    assert fields[1] in ['1', '2']
                    assert fields[2] in ['0000', '9999']
                    
        # 清理测试文件
        if os.path.exists(local_filepath):
            os.remove(local_filepath)
            
    def test_re_target_sep_priority(self):
        """测试re_target_sep优先级高于conflict_sep的情况"""
        config = dict(txt_config_dict1, **{
            'target_sep': ',',
            'table_cols': 'col1,col2,col3',
            're_target_sep': '|',
            'conflict_sep': '1',
            'demo': '9999,1,0000\n0000,2,9999',
            'execution': dict(execution_dict, **{'task_name': 'test.txt_config_dict_priority'})
        })
        config = dict(txt_config_dict_re_target_sep, **config)
        
        source = plugins.get('queqiao.plugin.ftplink.source.ftp')[0](Config(config))
        channel = Channel()
        source.read(channel)
        local_filepath = channel.get_pipeline_param('local_filepath')
        
        # 验证使用re_target_sep('|')而不是conflict_sep('\x01')
        with open(local_filepath, 'r') as f:
            content = f.read()
            lines = content.split('\n')
            for line in lines:
                if line:
                    assert ',' not in line  # 原分隔符被替换
                    assert '\x01' not in line  # 没有使用conflict_sep
                    fields = line.split('|')
                    assert len(fields) == 3
                    
        # 清理测试文件
        if os.path.exists(local_filepath):
            os.remove(local_filepath)

    @pytest.mark.parametrize('config', [txt_config_dict_multi_sep])
    def test_multi_char_sep_replacement(self, config):
        """测试使用多字符分隔符替换的情况"""
        source = plugins.get('queqiao.plugin.ftplink.source.ftp')[0](Config(config))
        channel = Channel()
        source.read(channel)
        local_filepath = channel.get_pipeline_param('local_filepath')
        
        # 验证分隔符已被替换为'###'
        with open(local_filepath, 'r') as f:
            content = f.read()
            lines = content.split('\n')
            for line in lines:
                if line:  # 跳过空行
                    # 检查原始分隔符'||'不再存在
                    assert '||' not in line
                    # 检查字段是否被正确分割
                    fields = line.split('###')
                    assert len(fields) == 3  # 应该有3个字段
                    # 验证字段值
                    assert fields[0] in ['9999', '0000']
                    assert fields[1] in ['1', '2']
                    assert fields[2] in ['0000', '9999']
                    
        # 清理测试文件
        if os.path.exists(local_filepath):
            os.remove(local_filepath)

    def test_special_char_sep_replacement(self):
        """测试使用特殊字符作为分隔符的情况"""
        config = dict(txt_config_dict1, **{
            'table_cols': 'col1,col2,col3',
            're_target_sep': '<=>',
            'demo': '9999->1->0000\n0000->2->9999',
            'execution': dict(execution_dict, **{'task_name': 'test.txt_config_dict_special_sep'})
        })
        config = dict(txt_config_dict_multi_sep, **config)
        source = plugins.get('queqiao.plugin.ftplink.source.ftp')[0](Config(config))
        channel = Channel()
        source.read(channel)
        local_filepath = channel.get_pipeline_param('local_filepath')
        
        # 验证分隔符已被替换为'<=>'
        with open(local_filepath, 'r') as f:
            content = f.read()
            lines = content.split('\n')
            for line in lines:
                if line:
                    assert '->' not in line  # 原分隔符被替换
                    fields = line.split('<=>')
                    assert len(fields) == 3
                    # 验证字段值
                    assert fields[0] in ['9999', '0000']
                    assert fields[1] in ['1', '2']
                    assert fields[2] in ['0000', '9999']
                    
        # 清理测试文件
        if os.path.exists(local_filepath):
            os.remove(local_filepath)
