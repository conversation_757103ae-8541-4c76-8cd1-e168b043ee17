"""
Author: xiaohei
Date: 2022/7/27
Email: <EMAIL>
Host: xiaohei.info
"""
import json

import pytest

from instance.default import system_env
from queqiao.conf import Config
from queqiao.core.engine.base import Channel
from queqiao.plugin import plugins
from queqiao.util.comm import strutil
from queqiao.util.comm.dtutil import timer
from tests import hive_dsn, pyhive_client
from tests.queqiao.plugin.ftplink import execution_dict

test_tablename = 'tgdw.pytest_part'
dsn_dit = hive_dsn.to_dict()
config1 = {
    'table_name': 'pytest_part',
    # 'table_cols': None,
    # 'data_range': None,
    'dsn': dsn_dit,
    'execution': dict(execution_dict, **{'task_name': 'test.config1'}),
    'current': timer.now().date,
}

config2 = {
    'table_name': 'pytest_part',
    # 'table_cols': None,
    'data_range': 'partition',
    'partition_key': 'partition_date',
    'partition_value': '2022-07-10',
    'dsn': dsn_dit,
    'execution': dict(execution_dict, **{'task_name': 'test.config2'}),
    'current': timer.now().date,
}

config3 = {
    'table_name': 'pytest_part',
    'table_cols': 'id,age',
    'data_range': 'partition',
    'partition_key': 'partition_date',
    'partition_value': '2022-07-10,2022-07-12',
    'dsn': dsn_dit,
    'execution': dict(execution_dict, **{'task_name': 'test.config3'}),
    'current': timer.now().date,
}


@pytest.mark.skipif(system_env != 'test', reason='test in test env')
@pytest.mark.test
class TestHive:
    def setup_class(self):
        pyhive_client.exec(f'drop table if exists {test_tablename}')
        pyhive_client.exec(f'create table {test_tablename}(id int comment "increment id",'
                           f' name string comment "user name", age int comment "user age") '
                           f'partitioned by (partition_date string)')
        pyhive_client.exec(
            f'insert into {test_tablename} partition(partition_date="2022-07-10") values(1,"jiangyuande",10)')
        pyhive_client.exec(
            f'insert into {test_tablename} partition(partition_date="2022-07-11") values(2,"jiangyuande",20)')
        pyhive_client.exec(
            f'insert into {test_tablename} partition(partition_date="2022-07-15") values(3,"jiangyuande",30)')

    def teardown_class(self):
        pyhive_client.exec(f'drop table if exists {test_tablename}')

    @pytest.mark.parametrize('config', [config1, config2, config3])
    def test_read(self, config):
        source = plugins.get('queqiao.plugin.ftplink.source.hive')[0](Config(config))
        channel = Channel()
        source.read(channel)

        assert 'ok_dict' in channel.get_pipeline_keys()
        print('ok_dict:')
        ok_dict = channel.get_pipeline_param('ok_dict')
        print(json.dumps(ok_dict))
        row_num = 3 if not self.__get_config(config, 'data_range') else len(
            self.__get_config(config, 'partition_value').split(','))
        assert ok_dict['row_num'] == row_num
        col1_max = '3' if not self.__get_config(config, 'data_range') else str(
            len(self.__get_config(config, 'partition_value').split(',')))
        assert ok_dict['col1_max'] == col1_max
        coln_min = '10' if self.__get_config(config, 'table_cols') else '2022-07-10'
        assert ok_dict['coln_min'] == coln_min
        assert ok_dict['coln_len_num'] == 1
        assert 'local_filepath' in channel.get_pipeline_keys()
        local_filepath = channel.get_pipeline_param("local_filepath")
        print(f'local_filepath: {local_filepath}')
        assert ok_dict['md5'] == strutil.md5_file(local_filepath)
        table_schema = ok_dict['table_schema']
        table_schema_map = {}
        for meta in table_schema:
            table_schema_map[meta['name']] = meta
        if not self.__get_config(config, 'table_cols'):
            assert table_schema_map['name'] == {'name': 'name', 'type': 'string', 'comment': 'user name'}
        assert table_schema_map['id'] == {'name': 'id', 'type': 'int', 'comment': 'increment id'}
        assert table_schema_map['age'] == {'name': 'age', 'type': 'int', 'comment': 'user age'}
        col_cnt = 4 if not self.__get_config(config, 'table_cols') else len(config['table_cols'].split(','))
        assert ok_dict['col_cnt'] == col_cnt

    def __get_config(self, config, key):
        if key not in config:
            return None
        return config[key]
