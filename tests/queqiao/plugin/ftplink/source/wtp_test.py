"""
Author: xiaohei
Date: 2022/8/11
Email: <EMAIL>
Host: xiaohei.info
"""
import json

import chardet
import pytest

from instance.default import system_env
from queqiao.conf import Config
from queqiao.core.engine.base import Channel
from queqiao.plugin import plugins
from queqiao.util.comm import osutil
from queqiao.util.comm.dtutil import timer
from tests.queqiao.plugin.ftplink import execution_dict

wtp_date = timer.now().delta(5).datekey
wtp_date_ = timer.now().delta(5).date
config = {
    'file_path': '',
    'file_name': f'tmsxxx_1072_{wtp_date}.dat',
    'ok_file': f'tmsxxx_1072_{wtp_date}.flg',
    'lastest_ready_time': timer.now().hourmin,
    'alarm_receivers': 'jiangyuande',
    'max_waiting_hours': '1',
    'ftp_polling_sec': '3',
    'target_date': wtp_date,
    'target_sep': '^?',
    'has_header': False,
    'table_cols': 'data_date,credit_limit_no,cooperation_platform,product_no,credit_start_date,credit_expire_date,credit_amount,contract_status,third_party_id,business_platform_no',
    # 'compress_type': None,
    # 'compress_passwd': None,
    'file_encode': 'GBK',
    'execution': dict(execution_dict, **{'task_name': 'test.config'}),
    'current': wtp_date_,
    'dsn': {
        'connect': json.dumps({'protocol': 'wtp'})
    },
}

save_path = f'/opt/meituan/apps/queqiao/local/org_meituan/{wtp_date}'
tmp_file_savepath = f'{save_path}/{config["file_name"]}'
tmp_ok_savepath = f'{save_path}/{config["ok_file"]}'


class TestWtp:
    def setup_class(self):
        osutil.rm(tmp_file_savepath)
        osutil.rm(tmp_ok_savepath)

    def teardown_class(self):
        osutil.rm(tmp_file_savepath)
        osutil.rm(tmp_ok_savepath)

    @pytest.mark.skipif(system_env != 'test', reason='test in test env')
    @pytest.mark.testp
    def test_read(self):
        assert not osutil.exists(tmp_file_savepath)
        source = plugins.get('queqiao.plugin.ftplink.source.wtp')[0](Config(config))
        channel = Channel()
        source.read(channel)
        local_filepath = channel.get_pipeline_param('local_filepath')
        ok_dict = channel.get_pipeline_param('ok_dict')
        print(f"ok_dict: {json.dumps(ok_dict)}")
        print(f"local_filepath: {local_filepath}")

        assert len(ok_dict['ctime']) == 32
        result_filepath = f'{save_path}/{config["execution"]["task_name"]}_{config["execution"]["id"]}' \
            f'_{config["target_date"]}.dat'
        result_okfilepath = f'{result_filepath}.ok'
        assert osutil.exists(result_filepath)
        assert osutil.exists(result_okfilepath)
        flg_md5 = osutil.calls(f'cat {result_okfilepath} | head -n 1')
        assert flg_md5 == ok_dict['ctime']
        with open(result_filepath, 'rb') as f:
            data = f.read()
            tmp = chardet.detect(data)
            if tmp['encoding']:
                assert tmp['encoding'].lower() != 'utf-8'
        with open(result_filepath, 'r') as f:
            lines = f.readlines()
            for line in lines:
                print(f'====line: [{line}]')
                assert not line.startswith('^?')
                assert '----' not in line
                assert len(line.split('^?')) == len(config['table_cols'].split(','))
