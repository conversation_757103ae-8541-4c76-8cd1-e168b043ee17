"""
Author: xiaohei
Date: 2022/7/27
Email: <EMAIL>
Host: xiaohei.info
"""
import json

import pytest
from pytalos import TalosException

from queqiao.conf import Config
from queqiao.core.engine.base import Channel
from queqiao.plugin import plugins
from queqiao.util.comm import strutil
from queqiao.util.comm.dtutil import timer
from tests import talos_dsn_with_passwd, mysql_dsn
from tests.queqiao.plugin.ftplink import execution_dict

test_tablename = 'task'
dsn_dit = mysql_dsn.to_dict()
config1 = {
    'sql': f'select id,name from {test_tablename}',
    # 'target_date': timer.now().datekey,
    'dsn': dsn_dit,
    'execution': dict(execution_dict, **{'task_name': 'test.config1'}),
    'current': timer.now().date,
}


class TestTalos:

    @pytest.mark.parametrize('config', [config1])
    def test_read(self, config):
        source = plugins.get('queqiao.plugin.ftplink.source.mysql')[0](Config(config))
        channel = Channel()
        source.read(channel)

        assert 'ok_dict' in channel.get_pipeline_keys()
        print('ok_dict:')
        ok_dict = channel.get_pipeline_param('ok_dict')
        print(json.dumps(ok_dict))
        assert ok_dict['row_num'] == 5
        assert 'local_filepath' in channel.get_pipeline_keys()
        local_filepath = channel.get_pipeline_param("local_filepath")
        print(f'local_filepath: {local_filepath}')
        # assert config['target_date'] in local_filepath
        assert ok_dict['md5'] == strutil.md5_file(local_filepath)
        table_schema = ok_dict['table_schema']
        select_cols = config['sql'].split(' ')[1].split(',')
        assert len(table_schema) == len(select_cols)
        table_schema_map = {}
        for meta in table_schema:
            table_schema_map[meta['name']] = meta
        for col in select_cols:
            type = 'varchar(256)' if col == 'name' else 'int'
            assert table_schema_map[col] == {'name': col, 'type': type, 'comment': table_schema_map[col]['comment']}
