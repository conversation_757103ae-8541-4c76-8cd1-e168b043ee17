"""
Author: xiaohei
Date: 2022/7/26
Email: <EMAIL>
Host: xiaohei.info
"""
import json

import pytest

from queqiao.conf import Config
from queqiao.core.engine.base import Channel
from queqiao.plugin import plugins
from queqiao.util.comm import osutil
from queqiao.util.comm.dtutil import timer
from tests.queqiao.plugin.ftplink import execution_dict


class TestFile:

    def setup_class(self):
        if not osutil.exists('/tmp/test'):
            osutil.mkdir('/tmp/test')
        osutil.call(f'echo 9999\x01123\x010000 > /tmp/test/data1.txt')
        osutil.call(f'echo 0000\x01123\x019999 > /tmp/test/data2.txt')
        osutil.call(f'echo col1,col2,col3 > /tmp/test_data_header.txt')
        osutil.call(f'echo 9999,123,0000 >> /tmp/test_data_header.txt')
        osutil.call(f'echo 0000,123,9999 >> /tmp/test_data_header.txt')
        osutil.call(f'echo 9999,123,0000 > /tmp/test_data.txt')
        osutil.call(f'echo 0000,123,9999 >> /tmp/test_data.txt')

    def teardown_class(self):
        osutil.rm('/tmp/test')
        osutil.rm('/tmp/test_data_header.txt')
        osutil.rm('/tmp/test_data.txt')

    @pytest.mark.parametrize('config',
                             [Config({'file': '/tmp/test/data*.txt',
                                      'current': timer.now().delta(1).date, 'execution': execution_dict}),
                              Config({'file': '/tmp/test_data_header.txt', 'has_header': True,
                                      'target_sep': ',', 'current': timer.now().delta(1).date,
                                      'execution': execution_dict}),
                              Config({'file': '/tmp/test_data.txt', 'target_sep': ',',
                                      'current': timer.now().delta(1).date, 'execution': execution_dict})
                              ])
    def test_read(self, config):

        if hasattr(config, 'has_header') and config.has_header:
            assert osutil.wc(config.file) == 3
        if '*' in config.file:
            assert len(osutil.list_files('/'.join(config.file.split('/')[0:-1]), prefix='data', suffix='.txt')) == 2
        source = plugins.get('queqiao.plugin.ftplink.source.file')[0](config)
        channel = Channel()
        source.read(channel)
        assert 'ok_dict' in channel.get_pipeline_keys()
        print('ok_dict:')
        ok_dict = channel.get_pipeline_param('ok_dict')
        print(json.dumps(ok_dict))
        assert ok_dict['row_num'] == 2
        assert ok_dict['col1_max'] == '9999'
        assert ok_dict['coln_min'] == '0000'
        assert ok_dict['coln_len_num'] == 1
        assert 'local_filepath' in channel.get_pipeline_keys()
        local_filepath = channel.get_pipeline_param("local_filepath")
        print(f'local_filepath: {local_filepath}')
        # assert local_filepath == config.file.replace('*', '')
        if '*' in config.file:
            assert len(osutil.list_files('/'.join(config.file.split('/')[0:-1]))) == 3
