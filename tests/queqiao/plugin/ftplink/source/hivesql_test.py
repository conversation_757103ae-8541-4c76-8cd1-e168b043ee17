"""
Author: xiaohei
Date: 2022/7/27
Email: <EMAIL>
Host: xiaohei.info
"""
import json

import pytest

from instance.default import system_env
from queqiao.conf import Config
from queqiao.core.engine.base import Channel
from queqiao.plugin import plugins
from queqiao.util.comm import strutil
from queqiao.util.comm.dtutil import timer
from tests import hivesql_dsn, pyhive_client
from tests.queqiao.plugin.ftplink import execution_dict

dsn_dit = hivesql_dsn.to_dict()
config = {
    'sql': 'select * from pytest',
    # 'target_date': timer.now().datekey,
    'dsn': dsn_dit,
    'execution': dict(execution_dict, **{'task_name': 'test.config'}),
    'current': timer.now().date
}

test_tablename = 'pytest'


@pytest.mark.skipif(system_env != 'test', reason='test in test env')
@pytest.mark.test
class TestHivesql:
    def setup_class(self):
        pyhive_client.exec(f'drop table if exists {test_tablename}')
        pyhive_client.exec(f'create table {test_tablename}(id int comment "increment id",'
                           f' name string comment "user name", age int comment "user age")')
        pyhive_client.exec(f'insert into {test_tablename} values(1,"jiangyuande",20)')
        pyhive_client.exec(f'insert into {test_tablename} values(3,"jiangyuande",10)')
        pyhive_client.exec(f'insert into {test_tablename} values(2,"jiangyuande",30)')

    def teardown_class(self):
        pyhive_client.exec(f'drop table if exists {test_tablename}')

    def test_read(self):
        source = plugins.get('queqiao.plugin.ftplink.source.hivesql')[0](Config(config))
        channel = Channel()
        source.read(channel)

        assert 'ok_dict' in channel.get_pipeline_keys()
        print('ok_dict:')
        ok_dict = channel.get_pipeline_param('ok_dict')
        print(json.dumps(ok_dict))
        assert ok_dict['row_num'] == 3
        assert ok_dict['col1_max'] == '3'
        assert ok_dict['coln_min'] == '10'
        assert ok_dict['coln_len_num'] == 1
        assert 'local_filepath' in channel.get_pipeline_keys()
        local_filepath = channel.get_pipeline_param("local_filepath")
        print(f'local_filepath: {local_filepath}')
        # assert config['target_date'] in local_filepath
        assert ok_dict['md5'] == strutil.md5_file(local_filepath)
        table_schema = ok_dict['table_schema']
        table_schema_map = {}
        for meta in table_schema:
            table_schema_map[meta['name']] = meta
        assert table_schema_map['id'] == {'name': 'id', 'type': 'int', 'comment': 'increment id'}
        assert table_schema_map['name'] == {'name': 'name', 'type': 'string', 'comment': 'user name'}
        assert table_schema_map['age'] == {'name': 'age', 'type': 'int', 'comment': 'user age'}
