"""
Author: xiaohei
Date: 2022/9/19
Email: <EMAIL>
Host: xiaohei.info
"""
import json

import pytest

from queqiao.conf import Config
from queqiao.core.engine.base import Channel
from queqiao.plugin import plugins
from queqiao.util.comm import osutil
from queqiao.util.comm.dtutil import timer
from tests.queqiao.plugin.ftplink import execution_dict

config_temp = {
    'current': timer.now().delta(1).date, 'execution': execution_dict,
    'role': 'guest', 'party_id': 9999,
    'job_id': '202302130908473790570',
    'component_name': 'hetero_secureboost_0',
    'cols': None,
    'dsn': {
        'connect': json.dumps({'ip': '*************', 'port': 9380})
    }
}

all_col_config = {'cols': None}
all_col_config = Config(dict(all_col_config, **config_temp))
score_only_config = {'cols': 'id,predict_score'}
score_only_config = Config(dict(score_only_config, **config_temp))
predict_result_cols = 'id,label,predict_result,predict_score,predict_detail,type'
job_list_config = {}
job_list_config = Config(dict(job_list_config, **config_temp))
job_list_config.job_id = '202302130908473790570,202302130904513399380,202302130900146516120'


class TestFate:

    @pytest.mark.parametrize('config', [score_only_config, all_col_config, job_list_config])
    def test_read(self, config):
        source = plugins.get('queqiao.plugin.ftplink.source.fate')[0](config)
        channel = Channel()
        source.read(channel)
        assert 'ok_dict' in channel.get_pipeline_keys()
        print('ok_dict:')
        ok_dict = channel.get_pipeline_param('ok_dict')
        print(json.dumps(ok_dict))
        assert 'local_filepath' in channel.get_pipeline_keys()
        local_filepath = channel.get_pipeline_param("local_filepath")
        print(f'local_filepath: {local_filepath}')
        assert osutil.exists(local_filepath)
        # json格式分隔符+1
        col_cnt = len(predict_result_cols.split(',')) + 1 if not config.cols else len(config.cols.split(','))
        row1_cnt = osutil.nf(local_filepath, '\x01')
        assert col_cnt == row1_cnt
        print(f'read data from fate source:')
        for i in osutil.calls(f'head -n 5 {local_filepath}'):
            print(i)
