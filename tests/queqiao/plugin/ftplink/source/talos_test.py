"""
Author: xiaohei
Date: 2022/7/27
Email: <EMAIL>
Host: xiaohei.info
"""
import json

import pytest
from pytalos import TalosException

from queqiao.conf import Config
from queqiao.core.engine.base import Channel
from queqiao.plugin import plugins
from queqiao.util.comm import strutil
from queqiao.util.comm.dtutil import timer
from tests import talos_dsn, talos_dsn_with_passwd
from tests.queqiao.plugin.ftplink import execution_dict

test_tablename = 'mart_fspinno_queqiao.pytest'
dsn_dit = talos_dsn_with_passwd.to_dict()
config1 = {
    'sql': f'select id,name from {test_tablename}',
    # 'target_date': timer.now().datekey,
    'dsn': dsn_dit,
    'execution': dict(execution_dict, **{'task_name': 'test.config1'}),
    'current': timer.now().date,
}

config2 = {
    'sql': f'select id,age from {test_tablename}',
    # 'target_date': timer.now().datekey,
    'uname': 'talos_algo_ftplink',
    'passwd': 'VDsbbd#877',
    'engine': 'hive',
    'dsn': dsn_dit,
    'execution': dict(execution_dict, **{'task_name': 'test.config2'}),
    'current': timer.now().date,
}

config3 = {
    'sql': f'select id,age from {test_tablename}',
    'dsn': talos_dsn_with_passwd.to_dict(),
    'execution': dict(execution_dict, **{'task_name': 'test.config2'}),
    'current': timer.now().date
}

config4 = {
    'sql': f'select age,id from {test_tablename}',
    'uname': 'error',
    'passwd': 'error',
    'engine': 'hive',
    'dsn': talos_dsn_with_passwd.to_dict(),
    'execution': dict(execution_dict, **{'task_name': 'test.config2'}),
    'current': timer.now().date
}

config5 = {
    'cols': ['id', 'name', 'age', 'partition_date'],
    'sql': f'SELECT * FROM {test_tablename}_parted',
    'dsn': dsn_dit,
    'execution': dict(execution_dict, **{'task_name': 'test.config1'}),
    'current': timer.now().date,
}

config_row_sep1 = {
    'row_sep_to': '|-|\n',
    'row_sep_from': '\n',
}
config_row_sep1 = dict(config1, **config_row_sep1)
config_row_sep2 = dict(config2, **config_row_sep1)
config_row_sep3 = dict(config3, **config_row_sep1)
config_row_sep4 = dict(config4, **config_row_sep1)

class TestTalos:

    @pytest.mark.parametrize('config', [config1, config2, config3, config4])
    def test_read(self, config):
        if 'uname' in config and config['uname'] == 'error':
            with pytest.raises(TalosException):
                plugins.get('queqiao.plugin.ftplink.source.talos')[0](Config(config))
            return
        source = plugins.get('queqiao.plugin.ftplink.source.talos')[0](Config(config))
        channel = Channel()
        source.read(channel)

        assert 'ok_dict' in channel.get_pipeline_keys()
        print('ok_dict:')
        ok_dict = channel.get_pipeline_param('ok_dict')
        print(json.dumps(ok_dict))
        assert ok_dict['row_num'] == 2
        assert ok_dict['col1_max'] == '2'
        coln_min = '20' if 'age' in config['sql'] else 'jiangyuande'
        assert ok_dict['coln_min'] == coln_min
        coln_len_num = 1 if 'age' in config['sql'] else 2
        assert ok_dict['coln_len_num'] == coln_len_num
        assert 'local_filepath' in channel.get_pipeline_keys()
        local_filepath = channel.get_pipeline_param("local_filepath")
        print(f'local_filepath: {local_filepath}')
        # assert config['target_date'] in local_filepath
        assert ok_dict['md5'] == strutil.md5_file(local_filepath)
        table_schema = ok_dict['table_schema']
        select_cols = config['sql'].split(' ')[1].split(',')
        assert len(table_schema) == len(select_cols)
        table_schema_map = {}
        for meta in table_schema:
            table_schema_map[meta['name']] = meta
        for col in select_cols:
            type = 'string' if col == 'name' else 'int'
            assert table_schema_map[col] == {'name': col, 'type': type, 'comment': ''}

    def test_empty_read(self):
        source = plugins.get('queqiao.plugin.ftplink.source.talos')[0](Config(config5))
        channel = Channel()
        source.read(channel)
        assert 'ok_dict' in channel.get_pipeline_keys()
        print('ok_dict:')
        ok_dict = channel.get_pipeline_param('ok_dict')
        print(json.dumps(ok_dict))
        assert len(ok_dict['table_schema']) == len(config5['cols'])

    @pytest.mark.parametrize('config', [config_row_sep1, config_row_sep2, config_row_sep3, config_row_sep4])
    def test_row_sep(self, config):
        if 'uname' in config and config['uname'] == 'error':
            with pytest.raises(TalosException):
                plugins.get('queqiao.plugin.ftplink.source.talos')[0](Config(config))
            return
        source = plugins.get('queqiao.plugin.ftplink.source.talos')[0](Config(config))
        channel = Channel()
        source.read(channel)

        assert 'ok_dict' in channel.get_pipeline_keys()
        print('ok_dict:')
        ok_dict = channel.get_pipeline_param('ok_dict')
        print(json.dumps(ok_dict))
