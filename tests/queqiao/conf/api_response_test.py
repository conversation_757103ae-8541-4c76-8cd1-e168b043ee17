"""
Author: xiaohei
Date: 2022/7/12
Email: <EMAIL>
Host: xiaohei.info
"""
import json

import pytest

from queqiao.conf.ApiResponse import SUCCESS, DO_NOTHING, UNKNOW_ERROR, LOGIN_FAILED, ILLEGAL_PARAMS
from queqiao.conf.system import SystemConfig
from queqiao.dba.base_model import BaseModel


class TestApiResult:
    @pytest.mark.parametrize("res", [SUCCESS, DO_NOTHING, UNKNOW_ERROR, LOGIN_FAILED])
    def test_success(self, res):
        resp = res()
        assert resp.status_code == 200
        print(resp.json)
        assert resp.json

    @pytest.mark.parametrize('data', [
        {'username': 'jiangyuande', 'password': 'password'},
        [{'username': 'jiangyuande1', 'password': 'password1'}, {'username': 'jiangyuande2', 'password': 'password2'}],
        SystemConfig.read('dxks_template_ids', value_only=False)
    ])
    def test_with_data(self, data):
        resp = LOGIN_FAILED(data=data)
        assert resp.status_code == 200
        print(json.dumps(resp.json))
        assert resp.json['code'] == 201001
        data = data.to_dict() if isinstance(data, BaseModel) else data
        assert resp.json['data'] == data

    def test_with_detail(self):
        msg = 'i am a detail message for information'
        resp = UNKNOW_ERROR(detail=msg)
        assert resp.status_code == 200
        print(resp.json)
        assert resp.json['code'] == 999999
        assert msg in resp.json['message']

    def test_with_fillin(self):
        fillin = ','.join(['param1', 'param2'])
        resp = ILLEGAL_PARAMS(detail=fillin)
        assert resp.status_code == 200
        print(resp.json)
        assert resp.json['code'] == 201002
        assert fillin in resp.json['message']
