"""
Author: xiaohei
Date: 2022/6/16
Email: <EMAIL>
Host: xiaohei.info
"""
import importlib
import json
import os

import pytest

from instance.default import CONF_PATH, apps
from queqiao.conf.env import EnvConfig

fixapps = ['api', 'exe', 'wac']
envs = [f.replace('.py', '') for f in os.listdir(f'{CONF_PATH}/env') if f.endswith('.py') and not f.startswith('__')]


class TestConfigValue:

    @pytest.mark.parametrize('app', fixapps)
    def test_app_list(self, app):
        assert len(apps) == len(fixapps)
        assert app in apps

    @pytest.mark.parametrize('env', envs)
    def test_env_config_values(self, env):
        configs = importlib.import_module(f'instance.env.{env}')
        for k in dir(configs):
            if not k.startswith('__'):
                value = getattr(configs, k)
                value = json.dumps(value) if isinstance(value, dict) else value
                # print(f'{k}: {value}')
        assert configs is not None

    def test_system_env_config(self):
        assert EnvConfig.get('ENV') == os.environ.get('QUEQIAO_ENV')
