"""
Author: xiaohei
Date: 2022/5/3
Email: <EMAIL>
Host: xiaohei.info
"""
import json

import pytest

from queqiao.conf import IllegalParamsException, Config


class TestConfigObj:

    def test_dict_params(self):
        dict_params = {'k1': 'v1', 'k2': 'v2', 'k3': {'kk1': 'vv2', 'kk2': {'kkk1': 'vvv1'}}}
        config = Config(dict_params)
        assert hasattr(config, 'k1') is True
        assert hasattr(config, 'kk1') is False
        assert isinstance(getattr(config, 'k3'), Config) is True
        assert getattr(getattr(config, 'k3'), 'kk1') == 'vv2'

    @pytest.mark.parametrize('dict_params', ['a', None])
    def test_not_dict_params(self, dict_params):
        with pytest.raises(IllegalParamsException) as e:
            config = Config(dict_params)
            assert config is None
        assert str(e.value) == 'dict_params must be a dict'

    def test_to_dict(self):
        dict_params = {'k1': 'v1', 'k2': 'v2', 'k3': {'kk1': 'vv2', 'kk2': {'kkk1': 'vvv1'}}}
        config = Config(dict_params)
        to_dict = Config.to_dict(config)
        print(f'to_dict: {to_dict}')
        assert to_dict == dict_params
        print(f'to_str: {config}')
        assert json.dumps(dict_params) == str(config)
