"""
Author: xiaohei
Date: 2022/7/8
Email: <EMAIL>
Host: xiaohei.info
"""
import json

import pytest

from queqiao.conf.system import SystemConfig
from queqiao.util.conn.redis import RedisClient

test_keys = {
    'key1': 'value1',
    'key2': 'value2',
}


class TestSystemConfig:
    def setup_class(self):
        SystemConfig.delete_by(create_user='xhh')
        self.redis = RedisClient.get_conn()
        for key in test_keys.keys():
            self.redis.delete(f'system:config:{key.upper()}')
        key = 'key1'
        system_config = SystemConfig.new(create_user='xhh', update_user='xhh', key=key.upper(), value=test_keys[key],
                                         name=f'name for {key}')
        system_config.save()
        self.redis.set(f'system:config:{key}', json.dumps(system_config.to_dict()))

    def teardown_class(self):
        SystemConfig.delete_by(create_user='xhh')
        for key in test_keys.keys():
            self.redis.delete(f'system:config:{key.upper()}')
        self.redis.close()

    @pytest.mark.parametrize('key', ['notfound', 'key1'])
    def test_get(self, key):
        if key not in test_keys:
            assert SystemConfig.read(key) is None
        else:
            system_config = SystemConfig.read(key, value_only=False)
            assert system_config.value == test_keys[key]
            assert system_config.name == f'name for {key}'
            assert system_config.create_user == 'xhh'
            assert SystemConfig.read(key) == test_keys[key]

    @pytest.mark.parametrize('key', ['key1', 'key2'])
    def test_set(self, key):
        system_config = SystemConfig.dump(key, f'{test_keys[key]}++') if key == 'key1' else \
            SystemConfig.dump(key, f'{test_keys[key]}++', name='abc', opt_user='xhh')
        assert system_config is not None
        system_config_from_mysql = SystemConfig.get_one(key=key.upper())
        assert system_config == system_config_from_mysql
        system_config_from_redis = SystemConfig.from_dict(json.loads(self.redis.get(f'system:config:{key.upper()}')))
        assert system_config == system_config_from_redis
        assert system_config.value == f'{test_keys[key]}++'
        if key == 'key1':
            assert system_config.name == f'name for {key}'
            assert system_config.update_user == 'xhh'
        else:
            assert system_config.name == f'abc'
            assert system_config.update_user == 'xhh'

    def test_local_cache(self):
        SystemConfig.dump('key3', 'value3', name='name3', opt_user='xhh')
        # from redis and set to local
        system_config = SystemConfig.read('key3', value_only=False)
        assert system_config.value == 'value3'
        # from local cache
        cached_system_config = SystemConfig.read('key3', value_only=False)
        assert cached_system_config == system_config
