"""
Author: xiaohei
Date: 2022/7/12
Email: <EMAIL>
Host: xiaohei.info
"""
import pytest

from instance.default import system_env
from queqiao.util.comm import osutil, sqlutil
from queqiao.util.hadoop.spark import SparkCmdClient

test_tablename = 'pytest'


@pytest.mark.skipif(system_env != 'test', reason='test in test env')
@pytest.mark.test
class TestSparkCmdClient:
    def setup_class(self):
        self.client = SparkCmdClient()
        self.client.open()

    def teardown_class(self):
        # self.client.exec(f'drop table if exists {test_tablename}')
        self.client.close()

    @pytest.mark.parametrize('yarn_mode', [False, True])
    def test_exec(self, yarn_mode):
        if yarn_mode:
            self.client.set_yarn_mode(self.__class__.__name__)
        else:
            self.client.set_local_mode()
        self.client.exec(f'drop table if exists {test_tablename}')
        table_cnt = osutil.calls(f'spark-sql -e "show tables;" | grep {test_tablename} | wc -l')
        assert int(table_cnt) == 0
        assert self.client.exec(f'create table {test_tablename}(id int, name string, age int)') == 0
        table_cnt = osutil.calls(f'spark-sql -e "show tables;" | grep {test_tablename} | wc -l')
        assert int(table_cnt) == 1
        assert self.client.exec(f'insert into {test_tablename} values(1,"jiangyuande",20)') == 0
        assert self.client.exec(f'insert into {test_tablename} values(2,"xiaohei",22)') == 0
        result_cnt = osutil.calls(f'spark-sql -e "select count(1) from {test_tablename}"')
        assert int(result_cnt) == 2

    @pytest.mark.parametrize('yarn_mode', [False, True])
    def test_exec_cached(self, yarn_mode):
        if yarn_mode:
            self.client.set_yarn_mode(self.__class__.__name__)
        else:
            self.client.set_local_mode()
        self.client.set_exec_cache()
        assert self.client.exec(f'drop table if exists {test_tablename}') == 0
        assert self.client.exec(f'create table {test_tablename}(id int, name string, age int)') == 0
        assert self.client.exec(f'insert into {test_tablename} values(1,"jiangyuande",20)') == 0
        assert self.client.exec(f'insert into {test_tablename} values(2,"xiaohei",22)', async_=True) == 0
        result_cnt = osutil.calls(f'spark-sql -e "select count(1) from {test_tablename}"')
        assert int(result_cnt) == 2

    @pytest.mark.parametrize('params', [
        (f'select * from {test_tablename}', None, None),
        (f'select id,age from {test_tablename}', '#', None),
        (f'select name,age from {test_tablename}', '|+|', '/tmp/pytest'),
    ])
    def test_query(self, params):
        sql, col_sep, save_path = params
        select_cols = sqlutil.get_select_cols(sql)
        select_cols = select_cols if '*' not in select_cols else ['id', 'name', 'age']
        if col_sep:
            self.client.set_default_col_sep(col_sep)
        if save_path:
            self.client.set_default_save_path(save_path)
        query_result = self.client.query(sql)
        assert query_result['status'] == 0
        assert query_result['total'] == 2
        assert len(query_result['columns']) == len(select_cols)
        cols = [col['name'] for col in query_result['columns']]
        assert cols == select_cols
        assert osutil.exists(query_result['file_path'])
        with open(query_result['file_path'], 'r') as f:
            lines = f.readlines()
            if col_sep:
                assert len(lines[0].split(col_sep)) == len(select_cols)
            print(lines)

    def test_schema(self):
        schema = self.client.schema(test_tablename)
        print(schema)
        cols = [col['name'] for col in schema]
        assert len(schema) == 3
        assert 'id' in cols and 'name' in cols and 'age' in cols
