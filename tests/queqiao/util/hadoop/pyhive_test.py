"""
Author: xiaohei
Date: 2022/7/12
Email: <EMAIL>
Host: xiaohei.info
"""
import pytest

from instance.default import system_env
from queqiao.conf.system import SystemConfig
from queqiao.util.comm import osutil, sqlutil
from queqiao.util.hadoop.pyhive import PyhiveClient

test_tablename = 'pytest'
join_test_tablename = 'test'


@pytest.mark.skipif(system_env != 'test', reason='test in test env')
@pytest.mark.test
class TestPyhiveClient:
    def setup_class(self):
        self.client = PyhiveClient(
            pyhive_host=SystemConfig.read('PYHIVE_HOST'),
            pyhive_port=SystemConfig.read('PYHIVE_PORT'),
            kerberos_user=SystemConfig.read('KERBEROS_USER'),
            kerberos_service_name=SystemConfig.read('KERBEROS_SERVICE_NAME'),
            beeline_u=SystemConfig.read('BEELINE_U'),
            keytab=SystemConfig.read('KERBEROS_KEYTAB')
        )
        self.client.open()
        self.client.exec(f'drop table if exists {test_tablename}')
        osutil.calls('hdfs dfs -rmr /warehouse/tablespace/external/hive/pytest')

    def teardown_class(self):
        self.client.exec(f'drop table if exists {test_tablename}')
        self.client.close()
        osutil.calls('hdfs dfs -rmr /warehouse/tablespace/external/hive/pytest')

    def test_exec(self):
        self.client.exec(f'drop table if exists {test_tablename}')
        table_cnt = osutil.calls(f'spark-sql -e "show tables;" | grep {test_tablename} | wc -l')
        assert int(table_cnt) == 0
        assert self.client.exec(f'create external table {test_tablename}(id int, name string, age int)') == 0
        table_cnt = osutil.calls(f'spark-sql -e "show tables;" | grep {test_tablename} | wc -l')
        assert int(table_cnt) == 1
        assert self.client.exec(f'insert into {test_tablename} values(1,"jiangyuande",20)') == 0
        assert self.client.exec(f'insert into {test_tablename} values(2,"xiaohei",22)') == 0
        result_cnt = osutil.calls(f'spark-sql -e "select count(1) from {test_tablename}"')
        assert int(result_cnt) == 2

    @pytest.mark.parametrize('params', [
        (f'select * from {test_tablename}', None, None),
        (f'select id,age from {test_tablename}', '#', None),
        (f'select name,age from {test_tablename}', '+', '/tmp/pytest'),
        (f'select a.name from {test_tablename} a left join {join_test_tablename} b on a.id=b.id where b.id is null',
         None, None)
    ])
    def test_query(self, params):
        sql, col_sep, save_path = params
        select_cols = sqlutil.get_select_cols(sql)
        select_tables = sqlutil.get_tables(sql)
        table_cnt = 2 if 'join' in sql else 1
        assert len(select_tables) == table_cnt
        select_cols = select_cols if '*' not in select_cols else ['id', 'name', 'age']
        if col_sep:
            self.client.set_default_col_sep(col_sep)
        if save_path:
            self.client.set_default_save_path(save_path)
        query_result = self.client.query(sql)
        assert query_result['status'] == 0
        assert query_result['total'] == 2
        if len(select_tables) == 1:
            assert len(query_result['columns']) == len(select_cols)
            cols = [col['name'] for col in query_result['columns']]
            assert cols == select_cols
        else:
            assert len(query_result['columns']) == 0
        assert osutil.exists(query_result['file_path'])
        with open(query_result['file_path'], 'r') as f:
            lines = f.readlines()
            if col_sep:
                assert len(lines[0].split(col_sep)) == len(select_cols)
            else:
                assert len(lines[0].split('\x01')) == len(select_cols)
            print(lines)

    def test_schema(self):
        schema = self.client.schema(test_tablename)
        print(schema)
        cols = [col['name'] for col in schema]
        assert len(schema) == 3
        assert 'id' in cols and 'name' in cols and 'age' in cols
