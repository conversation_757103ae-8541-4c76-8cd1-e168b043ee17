"""
Author: xiaohei
Date: 2022/7/8
Email: <EMAIL>
Host: xiaohei.info
"""

import pytest

from queqiao.util.comm import sqlutil, osutil
from queqiao.util.conn.mysql import MySQLClient

test_tablename = 'task'
join_test_tablename = 'execution'


class TestMysqlClient:
    def setup_class(self):
        self.client = MySQLClient(host='127.0.0.1', port=3306, user='root', password='root', database='queqiao')
        self.client.open()

    def teardown_class(self):
        self.client.close()

    @pytest.mark.parametrize('params', [
        (f'select * from {test_tablename}', None, None),
        (f'select id,name from {test_tablename}', '#', None),
        (f'select name,status from {test_tablename}', '|+|', '/tmp/pytest'),
        # (
        # f'select a.name from {test_tablename} a left join {join_test_tablename} b on a.id=b.task_id',
        # None,
        # None),
    ])
    def test_query(self, params):
        sql, col_sep, save_path = params
        select_cols = sqlutil.get_select_cols(sql)
        select_cols = [c.split('.')[-1] for c in select_cols]
        select_tables = sqlutil.get_tables(sql)
        table_cnt = 2 if 'join' in sql else 1
        assert len(select_tables) == table_cnt
        select_cols = select_cols if '*' not in select_cols else ['id', 'create_user', 'update_user', 'is_delete',
                                                                  'name', 'trans_type', 'alarm_receivers',
                                                                  'retry_times', 'retry_interval', 'queue', 'priority',
                                                                  'params', 'status', 'apply_id', 'task_type_id',
                                                                  'engine_id', 'project_id', 'create_time',
                                                                  'update_time']
        if col_sep:
            self.client.set_default_col_sep(col_sep)
        if save_path:
            self.client.set_default_save_path(save_path)
        query_result = self.client.query(sql)
        assert query_result['status'] == 0
        assert query_result['total'] == 5
        assert len(query_result['columns']) == len(select_cols)
        cols = [col['name'] for col in query_result['columns']]
        assert cols == select_cols
        assert osutil.exists(query_result['file_path'])
        with open(query_result['file_path'], 'r') as f:
            lines = f.readlines()
            if col_sep:
                assert len(lines[0].split(col_sep)) == len(select_cols)
            print(lines)

    def test_schema(self):
        self.client.query(f"select * from {test_tablename}")
        schema = self.client.schema(test_tablename)
        print(schema)
        cols = [col['name'] for col in schema]
        assert len(schema) == 19
        assert 'id' in cols and 'name' in cols and 'status' in cols
