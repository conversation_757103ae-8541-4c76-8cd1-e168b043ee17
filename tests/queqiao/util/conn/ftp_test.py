"""
Author: xiaohei
Date: 2022/7/7
Email: <EMAIL>
Host: xiaohei.info
"""
import pytest
import os

from instance.default import system_env
from queqiao.conf import IllegalParamsException
from queqiao.util.comm import osutil
from queqiao.util.comm.dtutil import timer
from queqiao.util.conn.ftp import RemoteFileServer, SftpServer, FtpServer, WtpServer

# 添加skip_db标记
skip_db = pytest.mark.skipif(True, reason="Skip tests that require database connection")

ftp_conn = {
    "default": {
        "ip": "localhost",
        "port": 2121,
        "protocol": "ftp",
        "username": "queqiao",
        "password": "password",
        "work_dir": "/",
        "test_file": "/test/test_from_local.txt",
        "test_multilevel_dir": "/test/level1/level2/level3",
        "test_multilevel_file": "/test/level1/level2/level3/test.txt"
    },
    "queqiao": {
        "ip": "one-sftp.vip.sankuai.com",
        "port": 2222,
        "protocol": "sftp",
        "username": "queqiao",
        "password": "Xkh5bgcY60rBHU",
        "work_dir": "/one-sftp-xy-bank/queqiao",
        "test_file": "/one-sftp-xy-bank/queqiao/test/test_from_local.txt",
        "test_multilevel_dir": "/one-sftp-xy-bank/queqiao/test/level1/level2/level3",
        "test_multilevel_file": "/one-sftp-xy-bank/queqiao/test/level1/level2/level3/test.txt"
    },
    "error1": {
        "ip": "one-sftp.vip.sankuai.com",
        "port": 2222,
        "protocol": "sftp",
        "username": "queqiao",
        "password": "Xkh5bgcY60rBHU",
    },
    "error2": {
        "ip": "one-sftp.vip.sankuai.com",
        "port": 2222,
        "username": "queqiao",
        "password": "Xkh5bgcY60rBHU",
        "work_dir": "/one-sftp-xy-bank/queqiao"
    }
}

local_filename = 'test.txt'
local_file = f'/tmp/{local_filename}'
local_file_content = '11111'


@pytest.mark.skip
class TestFtp:

    @pytest.mark.parametrize('config', [{}, ftp_conn['error1'], ftp_conn['error2']])
    def test_init_error(self, config):
        with pytest.raises(IllegalParamsException) as e:
            ftp = RemoteFileServer.get_connect(config)
            assert ftp is None
        print(f'exception: {e.value.args[0]}')
        assert isinstance(e.value, IllegalParamsException) is True

    @pytest.mark.parametrize('config', [ftp_conn['default'], ftp_conn['queqiao']])
    def test_init(self, config):
        ftp = RemoteFileServer.get_connect(config)
        assert ftp is not None
        for k in config.keys():
            if k == "test_file":
                continue
            assert hasattr(ftp, k) is True
            assert config[k] == getattr(ftp, k)
            print(f'key: {k}, value: {getattr(ftp, k)}')

    @pytest.mark.parametrize('config', [ftp_conn['default']])
    def test_upload(self, config):
        ftp = RemoteFileServer.get_connect(config)
        assert isinstance(ftp, FtpServer)
        ftp.open()
        osutil.call(f'echo {local_file_content} > {local_file}')
        ftp.upload(local_file, config['test_file'])
        assert ftp.exists(config['test_file']) is True
        ftp.close()

    @pytest.mark.parametrize('config', [ftp_conn['default']])
    def test_download(self, config):
        osutil.rm('/tmp/test_download_from_ftp.txt')
        ftp = RemoteFileServer.get_connect(config)
        ftp.open()
        ftp.download(config['test_file'], '/tmp/test_download_from_ftp.txt')
        assert osutil.exists('/tmp/test_download_from_ftp.txt') is True
        ftp.close()

    @pytest.mark.parametrize('config', [ftp_conn['default']])
    def test_stat(self, config):
        ftp = RemoteFileServer.get_connect(config)
        ftp.open()
        stat = ftp.stat(config['test_file'])
        print(stat)
        ftp.close()

    @pytest.mark.parametrize('config', [ftp_conn['default']])
    def test_read(self, config):
        ftp = RemoteFileServer.get_connect(config)
        ftp.open()
        assert ftp.read(config['test_file']).strip() == local_file_content
        ftp.close()

    @pytest.mark.parametrize('config', [ftp_conn['default']])
    def test_write(self, config):
        ftp = RemoteFileServer.get_connect(config)
        ftp.open()
        ftp.write(f'{local_file_content}+{local_file_content}', config['test_file'])
        assert ftp.read(config['test_file']) == f'{local_file_content}+{local_file_content}'
        ftp.close()

    @pytest.mark.parametrize('config', [ftp_conn['default']])
    def test_list_dir(self, config):
        ftp = RemoteFileServer.get_connect(config)
        ftp.open()
        print(ftp.list_dir(config['work_dir']))
        ftp.close()

    @pytest.mark.parametrize('config', [ftp_conn['default']])
    def test_delete(self, config):
        ftp = RemoteFileServer.get_connect(config)
        ftp.open()
        ftp.delete(config['test_file'])
        assert ftp.exists(config['test_file']) is False
        ftp.close()
        with pytest.raises(Exception) as e:
            ftp.delete(config['test_file'])
        print(e.value.args[0])

    @pytest.mark.parametrize('config', [ftp_conn['default']])
    def test_upload_multilevel_dir(self, config):
        """测试FTP上传到多级目录的情况"""
        ftp = RemoteFileServer.get_connect(config)
        assert isinstance(ftp, FtpServer)
        ftp.open()
        
        # 准备测试文件
        osutil.call(f'echo {local_file_content} > {local_file}')
        
        try:
            # 测试上传到多级目录
            ftp.upload(local_file, config['test_multilevel_file'])
            
            # 验证目录和文件都创建成功
            assert ftp.exists(config['test_multilevel_dir']) is True
            assert ftp.exists(config['test_multilevel_file']) is True
            
            # 验证文件内容
            assert ftp.read(config['test_multilevel_file']).strip() == local_file_content
        finally:
            # 清理测试文件
            if ftp.exists(config['test_multilevel_file']):
                ftp.delete(config['test_multilevel_file'])
            ftp.close()

    @pytest.mark.parametrize('config', [ftp_conn['default']])
    def test_upload_existing_dir(self, config):
        """测试上传到已存在的目录"""
        ftp = RemoteFileServer.get_connect(config)
        assert isinstance(ftp, FtpServer)
        ftp.open()
        
        # 准备测试文件
        osutil.call(f'echo {local_file_content} > {local_file}')
        
        try:
            # 先上传一次创建目录
            ftp.upload(local_file, config['test_multilevel_file'])
            
            # 修改测试文件内容
            new_content = 'new content'
            osutil.call(f'echo {new_content} > {local_file}')
            
            # 再次上传到同一位置
            ftp.upload(local_file, config['test_multilevel_file'])
            
            # 验证文件被正确更新
            assert ftp.read(config['test_multilevel_file']).strip() == new_content
        finally:
            # 清理测试文件
            if ftp.exists(config['test_multilevel_file']):
                ftp.delete(config['test_multilevel_file'])
            ftp.close()


class TestSftp:

    @pytest.mark.parametrize('config', [ftp_conn['queqiao']])
    def test_upload(self, config):
        ftp = RemoteFileServer.get_connect(config)
        assert isinstance(ftp, SftpServer)
        ftp.open()
        osutil.call(f'echo {local_file_content} > {local_file}')
        ftp.upload(local_file, config['test_file'])
        assert ftp.exists(config['test_file']) is True
        ftp.close()

    @pytest.mark.parametrize('config', [ftp_conn['queqiao']])
    def test_download(self, config):
        ftp = RemoteFileServer.get_connect(config)
        ftp.open()
        ftp.download(config['test_file'], '/tmp/test_download_from_ftp.txt')
        assert osutil.exists('/tmp/test_download_from_ftp.txt') is True
        ftp.close()

    @pytest.mark.parametrize('config', [ftp_conn['queqiao']])
    def test_stat(self, config):
        ftp = RemoteFileServer.get_connect(config)
        ftp.open()
        stat = ftp.stat(config['test_file'])
        print(stat)
        ftp.close()

    @pytest.mark.parametrize('config', [ftp_conn['queqiao']])
    def test_read(self, config):
        ftp = RemoteFileServer.get_connect(config)
        ftp.open()
        assert ftp.read(config['test_file']).strip() == local_file_content
        ftp.close()

    @pytest.mark.parametrize('config', [ftp_conn['queqiao']])
    def test_write(self, config):
        ftp = RemoteFileServer.get_connect(config)
        ftp.open()
        ftp.write(f'{local_file_content}+{local_file_content}', config['test_file'])
        assert ftp.read(config['test_file']) == f'{local_file_content}+{local_file_content}'
        ftp.close()

    @pytest.mark.parametrize('config', [ftp_conn['queqiao']])
    def test_list_dir(self, config):
        ftp = RemoteFileServer.get_connect(config)
        ftp.open()
        print(ftp.list_dir(config['work_dir']))
        ftp.close()

    @pytest.mark.parametrize('config', [ftp_conn['queqiao']])
    def test_match_files(self, config):
        ftp = RemoteFileServer.get_connect(config)
        ftp.open()
        files = ftp.match_files(f"{config['work_dir']}/test", "test_from_local.*")
        assert len(files) == 1
        assert files[0] == f"{config['work_dir']}/test/test_from_local.txt"
        ftp.close()

    @pytest.mark.parametrize('config', [ftp_conn['queqiao']])
    def test_delete(self, config):
        ftp = RemoteFileServer.get_connect(config)
        ftp.open()
        ftp.delete(config['test_file'])
        assert ftp.exists(config['test_file']) is False
        ftp.close()
        with pytest.raises(Exception) as e:
            ftp.delete(config['test_file'])
        print(e.value.args[0])

    @pytest.mark.parametrize('config', [ftp_conn['queqiao']])
    def test_upload_multilevel_dir(self, config):
        """测试SFTP上传到多级目录的情况"""
        ftp = RemoteFileServer.get_connect(config)
        assert isinstance(ftp, SftpServer)
        ftp.open()
        
        # 准备测试文件
        osutil.call(f'echo {local_file_content} > {local_file}')
        
        try:
            # 测试上传到多级目录
            ftp.upload(local_file, config['test_multilevel_file'])
            
            # 验证目录和文件都创建成功
            assert ftp.exists(config['test_multilevel_dir']) is True
            assert ftp.exists(config['test_multilevel_file']) is True
            
            # 验证文件内容
            assert ftp.read(config['test_multilevel_file']).strip() == local_file_content
        finally:
            # 清理测试文件
            if ftp.exists(config['test_multilevel_file']):
                ftp.delete(config['test_multilevel_file'])
            ftp.close()

    @pytest.mark.parametrize('config', [ftp_conn['queqiao']])
    def test_mkdir_p(self, config):
        """测试SFTP的递归创建目录功能"""
        ftp = RemoteFileServer.get_connect(config)
        assert isinstance(ftp, SftpServer)
        ftp.open()
        
        try:
            # 测试创建多级目录
            ftp._mkdir_p(config['test_multilevel_dir'])
            
            # 验证目录创建成功
            assert ftp.exists(config['test_multilevel_dir']) is True
            
            # 测试重复创建同一目录不会报错
            ftp._mkdir_p(config['test_multilevel_dir'])
        finally:
            ftp.close()

    @pytest.mark.parametrize('config', [ftp_conn['queqiao']])
    def test_upload_existing_dir(self, config):
        """测试SFTP上传到已存在的目录"""
        ftp = RemoteFileServer.get_connect(config)
        assert isinstance(ftp, SftpServer)
        ftp.open()
        
        # 准备测试文件
        osutil.call(f'echo {local_file_content} > {local_file}')
        
        try:
            # 先创建目录
            ftp._mkdir_p(config['test_multilevel_dir'])
            
            # 上传文件
            ftp.upload(local_file, config['test_multilevel_file'])
            
            # 修改测试文件内容
            new_content = 'new content'
            osutil.call(f'echo {new_content} > {local_file}')
            
            # 再次上传到同一位置
            ftp.upload(local_file, config['test_multilevel_file'])
            
            # 验证文件被正确更新
            assert ftp.read(config['test_multilevel_file']).strip() == new_content
        finally:
            # 清理测试文件
            if ftp.exists(config['test_multilevel_file']):
                ftp.delete(config['test_multilevel_file'])
            ftp.close()

    @pytest.mark.parametrize('params', [
        ('one-sftp.vip.sankuai.com', 2222, True, None),  # 有效的SFTP端口
        ('one-sftp.vip.sankuai.com', 65536, False, 'Port number must be 0-65535'),  # 无效的端口号
        ('invalid.sftp.host', 2222, False, 'Failed to resolve host: invalid.sftp.host'),  # 无效的主机名
        ('one-sftp.vip.sankuai.com', 9999, False, 'Port 9999 is not accessible on host one-sftp.vip.sankuai.com'),  # 未开放的端口
    ])
    def test_check_port_connectivity(self, params):
        """测试SFTP端口连通性检查功能"""
        host, port, expected_result, expected_error = params
        is_accessible, error_msg = RemoteFileServer.check_port_connectivity(host, port)
        assert is_accessible == expected_result
        if expected_error:
            assert error_msg == expected_error

    @pytest.mark.parametrize('params', [
        (ftp_conn['queqiao'], True, None),  # 有效的SFTP配置
        ({
            'ip': 'one-sftp.vip.sankuai.com',
            'port': 9999,
            'protocol': 'sftp',
            'username': 'queqiao',
            'password': 'Xkh5bgcY60rBHU',
            'work_dir': '/one-sftp-xy-bank/queqiao'
        }, False, 'Port 9999 is not accessible on host one-sftp.vip.sankuai.com'),  # 端口不可访问
        ({
            'ip': 'invalid.sftp.host',
            'port': 2222,
            'protocol': 'sftp',
            'username': 'queqiao',
            'password': 'Xkh5bgcY60rBHU',
            'work_dir': '/one-sftp-xy-bank/queqiao'
        }, False, 'Failed to resolve host: invalid.sftp.host'),  # 无效主机名
        ({
            'ip': 'one-sftp.vip.sankuai.com',
            'port': 2222,
            'protocol': 'sftp',
            'username': 'invalid_user',
            'password': 'invalid_pass',
            'work_dir': '/one-sftp-xy-bank/queqiao'
        }, False, 'Invalid username or password'),  # 无效的认证信息
        (None, None, None),  # 空配置
    ])
    def test_test_connection(self, params):
        """测试SFTP连接测试功能"""
        config, expected_result, expected_error = params
        
        if config is None:
            with pytest.raises(IllegalParamsException) as e:
                RemoteFileServer.test_connection(config)
            assert str(e.value) == 'config must not be null'
            return
            
        is_success, error_msg = RemoteFileServer.test_connection(config)
        assert is_success == expected_result
        if expected_error:
            assert error_msg == expected_error


wtp_config = {'protocol': 'wtp'}
wtp_remote_file = f'tmsxxx_1072_{timer.now().datekey}.dat'
wtp_local_file = f'loan.ods_pudong_abc_d_32_20220814.txt'


class TestWtp:

    def setup_class(self):
        osutil.rm(f'/tmp/{wtp_remote_file}')

    def teardown_class(self):
        osutil.rm(f'/tmp/{wtp_remote_file}')

    @pytest.mark.skipif(system_env != 'test', reason='test in test env')
    @pytest.mark.testp
    def test_download(self):
        assert not osutil.exists(f'/tmp/{wtp_remote_file}')
        wtp = RemoteFileServer.get_connect(wtp_config)
        wtp.open()
        wtp.download(wtp_remote_file, f'/tmp/{wtp_local_file}')
        wtp.close()
        assert osutil.exists(f'/tmp/{wtp_local_file}')

    @pytest.mark.skipif(system_env != 'test', reason='test in test env')
    @pytest.mark.testp
    @pytest.mark.parametrize('file_name', [wtp_remote_file, 'abc.txt'])
    def test_stat(self, file_name):
        wtp = RemoteFileServer.get_connect(wtp_config)
        wtp.open()
        stat = wtp.stat(file_name)
        if 'abc' in file_name:
            assert stat is None
        else:
            assert stat is not None
            print(f'===========stat: {stat}')
        wtp.close()

    @pytest.mark.skipif(system_env != 'test', reason='test in test env')
    @pytest.mark.testp
    @pytest.mark.parametrize('params', [(wtp_remote_file, True), ('abc.txt', False)])
    def test_exists(self, params):
        file_name, ret = params
        wtp = RemoteFileServer.get_connect(wtp_config)
        wtp.open()
        assert wtp.exists(f'/tmp/{file_name}') is ret
        wtp.close()
