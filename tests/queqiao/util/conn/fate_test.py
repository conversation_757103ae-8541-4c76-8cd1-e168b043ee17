"""
Author: xiaohei
Date: 2022/9/12
Email: <EMAIL>
Host: xiaohei.info
"""
import json

from queqiao.util.comm import osutil, strutil
from queqiao.util.conn.fate import FateClient
from tests import guest_server_ip, predict_job_conf, predict_job_dsl, predict_output_data

job_id = None
file_path = '/tmp/test_fate_client_data_upload.csv'
namespace = 'queqiao_test'
tablename = 'test_fate_client_data_upload'


class TestFateClient:
    def setup_class(self):
        self.client = FateClient(guest_server_ip, 9380)
        self.client.open()
        osutil.call(f'echo "col1,col2,col3" > {file_path}')
        osutil.call(f'echo "111,222,333" >> {file_path}')
        osutil.call(f'echo "aaa,bbb,ccc" >> {file_path}')
        osutil.call(f'flow init --ip {guest_server_ip} --port 9380')

    def teardown_class(self):
        osutil.rm(file_path)

    def exec(self, cmd, conf_c=None, conf_d=None):
        uid = strutil.uid(rung=True)

        if conf_c:
            conf_c_filename = f'{uid}_conf.json'
            job_runtime_conf = json.dumps(conf_c)
            print(f'job runtime config: ')
            print(job_runtime_conf)
            print(f'save to {conf_c_filename}')
            with open(conf_c_filename, 'w') as conf_c_file:
                conf_c_file.write(job_runtime_conf)
            cmd = cmd.replace('{conf_c}', conf_c_filename)

        if conf_d:
            conf_d_filename = f'{uid}_dsl.json'
            job_dsl_conf = json.dumps(conf_d)
            print(f'job dsl config: ')
            print(job_dsl_conf)
            print(f'save to {conf_d_filename}')
            with open(conf_d_filename, 'w') as conf_d_file:
                conf_d_file.write(job_dsl_conf)
            cmd = cmd.replace('{conf_d}', conf_d_filename)
        print(f'get cmd: {cmd}')
        res = ''.join(osutil.calls(cmd))
        if conf_c:
            osutil.rm(conf_c_filename)
        if conf_d:
            osutil.rm(conf_d_filename)
        return json.loads(res)

    def test_data_upload(self):
        res = self.client.data_upload(file_path, partition=1, namespace=namespace,
                                      tablename=tablename)
        print(res)
        assert res['retcode'] == 0
        assert res['retmsg'] == 'success'
        res = self.exec(f'flow table info -n {namespace} -t {tablename}')
        data = res['data']
        assert data['count'] == osutil.wc(file_path) - 1
        assert data['exist'] == 1
        assert data['schema']['header'] in 'col1,col2,col3'

    def test_data_download(self):
        # 下载到服务端路径非本地路径
        savefile = f'/tmp/test_fate_client_data_download.csv'
        res = self.client.data_download(namespace=namespace, tablename=tablename,
                                        savefile=savefile)
        assert res['retcode'] == 0

    def test_table_info(self):
        res = self.client.table_info(namespace=namespace, tablename=tablename)
        data = res['data']
        assert data['count'] == osutil.wc(file_path) - 1
        assert data['exist'] == 1
        # 第一列被剔除变为sid
        assert data['schema']['header'] in 'col1,col2,col3'

    def test_job_submit(self):
        res = self.client.job_submit(conf_c=predict_job_conf, conf_d=predict_job_dsl)
        assert res['retcode'] == 0
        global job_id
        job_id = res['jobId']

    def test_wait_job(self):
        status = self.client.wait_job(role='guest', job_id=job_id)
        assert status == 'success'

    def test_job_query(self):
        res = self.client.job_query(role='guest', job_id=job_id)
        assert res['retcode'] == 0
        assert res['data'][0]['f_status'] == 'success'

    def test_job_status(self):
        status, progress = self.client.job_status(role='guest', job_id=job_id)
        assert status == 'success'
        assert progress == 100
        print(f'get job {job_id} status: {status}')

    def test_job_rerun(self):
        res = self.client.job_rerun(job_id=job_id)
        assert res['retcode'] == 0
        print(f'rerun job return: {res}')

    def test_output_data(self):
        save_path = '/tmp/test_fate_client_output_data'
        save_path = self.client.output_data(job_id=job_id, role='guest', party_id=10000, component='hetero_secure_boost_0',
                                      save_path=save_path)
        assert osutil.exists(save_path)
        data_file = f'{save_path}/{predict_output_data}.csv'
        assert osutil.exists(data_file)
        # print(f'job {job_id} predict result:')
        # print(osutil.calls(f'cat {data_file}'))

    def test_get_summary(self):
        summary = self.client.get_summary(job_id=job_id, role='guest', party_id=10000,
                                          component='hetero_secure_boost_0')
        print(f'get job {job_id} summary:')
        print(summary)
