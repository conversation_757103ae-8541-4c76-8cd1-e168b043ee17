"""
Author: xiaohei
Date: 2022/7/7
Email: <EMAIL>
Host: xiaohei.info
"""
from pyftpdlib.authorizers import DummyAuthorizer
from pyftpdlib.handlers import FTPHandler
from pyftpdlib.servers import FTPServer

from queqiao.util.comm import osutil

ftp_root = '/tmp/local_ftp_test'

if __name__ == '__main__':
    authorizer = DummyAuthorizer()
    # 参数：用户名，密码，目录，权限
    if not osutil.exists(ftp_root):
        osutil.mkdir(ftp_root)
    authorizer.add_user('queqiao', 'password', ftp_root, perm='elradfmwMT')
    handler = FTPHandler
    handler.authorizer = authorizer
    # 参数：IP，端口，handler
    server = FTPServer(('0.0.0.0', 2121), handler)
    server.serve_forever()
