"""
Author: xiaohei
Date: 2022/7/8
Email: <EMAIL>
Host: xiaohei.info
"""
import json

import pytest
from redis import StrictRedis

from queqiao.util.conn.redis import RedisClient, _get_redis_connection_config, redis_client

test_config = {
    'host': '**************',
    'port': 6379,
    'password': 'password',
    'db': 0
}


class TestRedis:
    def test_init(self):
        redis = RedisClient.get_conn()
        redis.info()
        redis.set('redis:conn:test_config', json.dumps(test_config))
        assert redis is not None
        assert json.loads(redis.get('redis:conn:test_config')) == test_config

    @pytest.mark.parametrize('key', ['test', 'test_config'])
    def test_get_config(self, key):
        config = _get_redis_connection_config(key)
        if key == 'test':
            assert config is None
        else:
            print(config)
            config.pop('connection_pool')
            assert config == test_config

    @redis_client
    def test_redis_wrapper(self, redis):
        assert isinstance(redis, StrictRedis)
        redis.info()
        value = 'from_test_wrapper'
        redis.set('k1', value)
        assert redis.get('k1') == value

    @redis_client()
    def redis_wrapper_with_key(self, key, redis=None):
        return redis.get(key)

    def test_redis_wrapper_with_key(self):
        assert self.redis_wrapper_with_key('error') is None
        assert json.loads(self.redis_wrapper_with_key('redis:conn:test_config')) == test_config
