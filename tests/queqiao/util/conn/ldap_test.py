"""
Author: xiaohei
Date: 2022/6/17
Email: <EMAIL>
Host: xiaohei.info
"""
import pytest

from queqiao.util.conn.ldap import LdapAuth, LdapEntry

ldap_configs = {
    "LDAP_SERVER_POOL": ['yp-ftplink-app-test12.mt'],
    "LDAP_SERVER_PORT": "389",
    "LDAP_SEARCH_DN": "uid=ldapbind,cn=users,cn=accounts,dc=mt,dc=com",
    "LDAP_SEARCH_PASSWORD": "password",
    "LDAP_SEARCH_BASE": "cn=accounts,dc=mt,dc=com",
    "LDAP_GROUP_SEARCH_BASE": "cn=groups,cn=accounts,dc=mt,dc=com",
    "LDAP_ALLOW_GROUPS": None
}


class TestLdap:

    @pytest.mark.parametrize('configs', [{}, {'username': 'jiangyuan<PERSON>'}])
    def test_error_init(self, configs):
        with pytest.raises(Exception) as e:
            LdapAuth(**configs)
        print(e.value)
        assert isinstance(e.value, Exception) is True
        if not configs:
            assert str(e.value) == 'ldap config not found!'
        else:
            assert str(e.value).startswith('required configs ') and str(e.value).endswith(' not found')

    def test_init(self):
        # todo: 测试 ldap_config = SystemConfig.get(LDAP_REQUIRED_CONFIGS)
        ldap = LdapAuth(**ldap_configs)
        assert ldap.config.LDAP_SERVER_POOL == ldap_configs['LDAP_SERVER_POOL']

    @pytest.mark.parametrize('username', ['jiangyuande', 'chentianzeng'])
    def test_get_user(self, username):
        ldap = LdapAuth(**ldap_configs)
        entry = ldap.get_user(username)
        assert isinstance(entry, LdapEntry)
        assert entry.uid == username

    # @pytest.mark.skip
    @pytest.mark.parametrize('username, password',
                             [('jiangyuande', 'password'), ('chentianzeng', 'password'), ('jiangyuande', '123'),
                              ('ceff', '123')])
    def test_auth_user(self, username, password):
        ldap = LdapAuth(**ldap_configs)
        flag, entry = ldap.auth_user(username, password)
        if password == '123':
            assert flag is False
        else:
            assert flag is True
            assert entry.uid == username
