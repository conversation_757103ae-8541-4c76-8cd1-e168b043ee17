"""
Author: xiaohei
Date: 2022/6/17
Email: <EMAIL>
Host: xiaohei.info
"""
from queqiao.util.comm import objutil
from tests.queqiao.util import comm


class TestObjutil:

    def test_new_instance(self):
        assert __name__ == 'tests.queqiao.util.comm.objutil_test'
        obj = objutil.new_instance(__name__, 'TestObjutil')
        assert isinstance(obj, TestObjutil) is True

    def test_find_cls_in_pkg(self):
        cls = objutil.find_cls_in_pkg(comm.__path__, comm.__package__)
        for k in cls.keys():
            v = cls[k]
            if k == __name__:
                assert len(v) == 1

    def test_find_mdl_in_pkg(self):
        mdl = objutil.find_mdl_in_pkg(comm.__path__, comm.__package__)
        print(mdl)
        assert __name__ in mdl
