"""
Author: xiaohei
Date: 2022/6/17
Email: <EMAIL>
Host: xiaohei.info
"""
import time

from queqiao.util.comm.dtutil import timer

curr_year = '2022'
curr_month = '06'
curr_day = '17'
curr_sec = '10'
curr_min = '11'
curr_hour = '12'
curr_date_bar = f'{curr_year}-{curr_month}-{curr_day}'
curr_date_nor = curr_date_bar.replace('-', '')
curr_date_bar_hm = f'{curr_date_bar} {curr_hour}:{curr_min}:{curr_sec}'
curr_date_nor_hm = curr_date_bar_hm.replace('-', '')
bar_format = '%Y-%m-%d'
bar_format_hm = '%Y-%m-%d %H:%M:%S'
nor_format = '%Y%m%d'
nor_format_hm = '%Y%m%d %H:%M:%S'


class TestTimer:

    def test_vars(self):
        now = timer.fromdt(curr_date_bar_hm)
        # for k in dir(now):
        #     if not k.startswith('__'):
        #         print(f'{k}: {getattr(now, k)}')
        assert now.date == curr_date_bar
        assert now.time == curr_date_bar_hm.split(' ')[-1]
        assert now.datetime == curr_date_bar_hm
        assert now.datekey == curr_date_nor
        assert now.second == int(curr_sec)
        assert now.minute == int(curr_min)
        assert now.hour == int(curr_hour)
        assert now.day == int(curr_date_bar.split('-')[-1])
        assert now.month == int(curr_date_bar.split('-')[1])
        assert now.year == int(curr_date_bar.split('-')[0])
        assert now.month_begin_date.date == f'{curr_year}-{curr_month}-01'
        assert now.month_end_date.date == f'{curr_year}-{curr_month}-30'
        # assert now.timestamp == timer.date2ts(timer.change_format_auto(curr_date_nor_hm, bar_format_hm))
        yestoday = now.delta(1)
        y = int(curr_date_bar.split('-')[-1]) - 1
        assert yestoday.day == y
        assert yestoday.date == f'{curr_year}-{curr_month}-{y}'
        assert yestoday.datetime == f'{curr_year}-{curr_month}-{y} {curr_hour}:{curr_min}:{curr_sec}'
        assert yestoday.datekey == f'{curr_year}{curr_month}{y}'

    def test_date_sub(self):
        start_date = '2022-02-10'
        end_date = '2022-01-01'
        diff = (timer.fromdt(start_date) - timer.fromdt(end_date)).days
        assert diff == 40

    def test_time_sub(self):
        start_time = '2022-02-10 12:10:10'
        end_time = '2022-02-10 12:00:00'
        diff = (timer.fromdt(start_time) - timer.fromdt(end_time)).seconds
        assert diff == 610

    def test_elapsed(self):
        start_time = timer.now().timestamp
        time.sleep(2)
        end_time = timer.now().timestamp
        elapsed = end_time - start_time
        print(f'start_time: {start_time}, end_time: {end_time}, elapsed: {elapsed}')
        assert elapsed == 2
