"""
Author: xiaohei
Date: 2022/6/17
Email: <EMAIL>
Host: xiaohei.info
"""
import chardet
import pytest

from instance.default import PROJECT_PATH, system_env, LOG_PATH
from queqiao.conf.enums import CompressType
from queqiao.util.comm import fileutil, osutil

test_save_path = '/tmp/test'
decompress_test_list = [
    (f'{test_save_path}/ziptest.{CompressType.ZIP.value}', CompressType.ZIP.value, '123'),
    (f'{test_save_path}/ziptest_nop.{CompressType.ZIP.value}', CompressType.ZIP.value, None),
    (f'{test_save_path}/gztest.{CompressType.GZ.value}', CompressType.GZ.value, None),
    (f'{test_save_path}/gziptest.{CompressType.GZIP.value}', CompressType.GZIP.value, None),
    (f'{test_save_path}/targztest.{CompressType.TARGZ.value}', CompressType.TARGZ.value, None),
    (f'{test_save_path}/z7test.{CompressType.Z7.value}', CompressType.Z7.value, 'passwd'),
    (f'{test_save_path}/z7test_nop.{CompressType.Z7.value}', CompressType.Z7.value, None)
]


class TestFileutil:

    def test_parse_excel(self):
        res = fileutil.parse_excel(f'{PROJECT_PATH}/docs/tiangong_batch_template.xlsx')
        assert len(res) == 3
        assert res[0]['数据源表名'] == 'tablename1'
        assert res[0]['是否缓存'] == '是'
        assert res[-1]['同步类型'] == '单次执行'

    def setup_class(self):
        if osutil.exists(test_save_path):
            osutil.rm(test_save_path)
        osutil.mkdir(test_save_path)
        print(f'create test save path: {test_save_path}')
        for i in decompress_test_list:
            compress_filename = i[0]
            compress_path = '/'.join(compress_filename.split('/')[:-1])
            compress_filename = compress_filename.split('/')[-1]
            compress_type = i[1]
            compress_passwd = i[2]
            normal_filename = compress_filename.split('.')[0] + '.txt'
            osutil.call(f'echo "123,123,123" > {compress_path}/{normal_filename}')
            if '_nop' in normal_filename:
                osutil.call(f'echo "321,321,321" > {compress_path}/{normal_filename}.nop')
            compressed_file = fileutil.compress(f'{compress_path}/{normal_filename}*', compress_filename, compress_type,
                                                compress_passwd)
            print(f'get compressed file: {compressed_file}')
            osutil.rm(f'{compress_path}/{normal_filename}')
            osutil.rm(f'{compress_path}/{normal_filename}.nop')
            print(osutil.calls(f'ls {compress_path}'))
        osutil.call(f'echo "1|+|2|+|3" > {test_save_path}/mul_sep.txt')
        osutil.call(f'echo "华丰融而出" > {test_save_path}/mul_encode.txt')
        fileutil.reset_encode(f'{test_save_path}/mul_encode.txt', curr_encode='utf-8', target_encode='GBK')

    def teardown_class(self):
        # pass
        if osutil.exists(test_save_path):
            osutil.rm(test_save_path)

    @pytest.mark.parametrize('params', decompress_test_list)
    def test_decompress(self, params):
        file_path, compress_type, compress_passdw = params
        assert not osutil.exists(f'{file_path.split(".")[0]}.txt')
        assert osutil.exists(f'{file_path}')
        normal_filename = fileutil.decompress(file_path, compress_type, compress_passdw)
        dir_path = '/'.join(normal_filename.split('/')[:-1])
        assert osutil.exists(f'{file_path.split(".")[0]}.txt')
        assert dir_path == '/tmp/test'
        assert osutil.exists(f'{normal_filename.split(".")[0]}.{compress_type}')
        content = '123,123,123' if not '_nop' in file_path else '123,123,123\n321,321,321'.split('\n')
        assert osutil.calls(f'cat {normal_filename}') == content
        print(osutil.calls(f'ls {dir_path}'))

    @pytest.mark.skipif(system_env != 'test', reason='test in test env')
    @pytest.mark.test
    def test_reset_sep(self):
        fileutil.reset_sep(f'{test_save_path}/mul_sep.txt', curr_sep='|+|')
        result = osutil.calls(f'cat {test_save_path}/mul_sep.txt')
        print(f'first change sep: {result}')
        assert len(result.split('\x01')) == 3
        fileutil.reset_sep(f'{test_save_path}/mul_sep.txt', curr_sep='\x01', target_sep=',')
        result = osutil.calls(f'cat {test_save_path}/mul_sep.txt')
        print(f'second change sep: {result}')
        assert len(result.split(',')) == 3

    @pytest.mark.skipif(system_env != 'test', reason='test in test env')
    @pytest.mark.test
    def test_reset_encode(self):
        fileutil.reset_encode(f'{test_save_path}/mul_encode.txt', curr_encode='GBK')
        with open(f'{test_save_path}/mul_encode.txt', 'rb') as f:
            data = f.read()
            tmp = chardet.detect(data)
            assert tmp['encoding'] == 'utf-8'

        fileutil.reset_encode(f'{test_save_path}/mul_encode.txt', curr_encode='utf-8', target_encode='GBK')
        with open(f'{test_save_path}/mul_encode.txt', 'rb') as f:
            data = f.read()
            tmp = chardet.detect(data)
            assert tmp['encoding'].lower() != 'utf-8'

    def test_get_meta(self):
        file_path = f'{PROJECT_PATH}/requirements.txt'
        target_sep = '=='
        colv_idx = -2
        meta = fileutil.get_meta(file_path, target_sep, colv_idx=colv_idx)
        print(meta)

        awk_cmd = f'export LC_COLLATE=POSIX;cat {file_path} | awk -F ' + f"'{target_sep}'"
        awk_coln_idx = 'NF'
        awk_colv_idx = 'NF' if colv_idx == -1 else f'(NF{str(colv_idx + 1)})'
        cmd_meta = {
            'row_num': osutil.wc(file_path),
            'col1_max': osutil.calls(awk_cmd + " '{print $1}' | " + 'sort | tail -n 1'),
            'col1_min': osutil.calls(awk_cmd + " '{print $1}' | " + 'sort | head -n 1'),
            'coln_max': osutil.calls(awk_cmd + " '{print $" + str(awk_coln_idx) + "}' | " + 'sort | tail -n 1'),
            'coln_min': osutil.calls(awk_cmd + " '{print $" + str(awk_coln_idx) + "}' | " + 'sort | head -n 1'),
            'coln_len_num': int(osutil.calls(
                awk_cmd + " '{print length($" + str(awk_coln_idx) + ")}' | " + 'sort | uniq | wc -l')),
            'colv_len_num': int(osutil.calls(
                awk_cmd + " '{print length($" + str(awk_colv_idx) + ")}' | " + 'sort | uniq | wc -l')),
        }
        for k in cmd_meta.keys():
            assert meta[k] == cmd_meta[k]

    def test_tar_zxvf(self):
        tar_dirname = 'tartest'
        osutil.mkdir(tar_dirname)
        osutil.touch(f'{tar_dirname}/1.txt')
        osutil.touch(f'{tar_dirname}/2.txt')
        tar_filename = f'{tar_dirname}.tar'
        tar_zcvf = f'tar -cvf {tar_filename} {tar_dirname}'
        ret = osutil.call(tar_zcvf)
        assert ret == 0
        osutil.rm(tar_dirname)
        assert not osutil.exists(tar_dirname)
        fileutil.untar(tar_filename)
        assert osutil.exists(tar_dirname)
        for i in osutil.calls(f'ls -al {tar_dirname}'):
            print(i)
        osutil.rm(tar_dirname)
        osutil.rm(tar_filename)

    def test_get_distinct_values(self):
        file_path = '/tmp/get_distinct_values_test.txt'
        osutil.call(f'echo 1,1,4 > {file_path}')
        osutil.call(f'echo 1,2,4 >> {file_path}')
        osutil.call(f'echo 1,3,4 >> {file_path}')
        osutil.call(f'echo 1,3,4 >> {file_path}')
        distinct_values = fileutil.get_distinct_values(file_path, ',', -2)
        assert len(distinct_values) == 3
        assert distinct_values == {'1', '2', '3'}
        distinct_values = fileutil.get_distinct_values(file_path, ',', 1)
        assert len(distinct_values) == 3
        assert distinct_values == {'1', '2', '3'}
        osutil.rm(file_path)
