"""
Author: xiaohei
Date: 2022/8/10
Email: <EMAIL>
Host: xiaohei.info
"""
import pytest

from queqiao.util.comm import strutil
from queqiao.util.comm.dtutil import timer


def test_render_vars():
    source = 'select * from {table} where partition_date="$now.delta(5).date"'
    now = timer.now()
    table = 'test_table'
    partition_date = now.delta(5).date
    target = strutil.render_vars(source, table=table, now=now)
    # table1 = target.split(' from ')[1].split(' ')[0]
    print(target)
    # assert table == table1
    assert partition_date in target


@pytest.mark.parametrize('params', [
    ('crefb_20220101.txt', 0, '20220101'),
    ('20221010_crefb_20220101.txt', 0, '20221010'),
    ('crefb_20221111_cecexe', -1, '20221111'),
    ('crefb_20221111_cecexe_20222222', None, ['20221111', '20222222']),
    ('crefb_20221111_cecexe', 1, None),
    ('select * from abc where partition_date in ("2022-09-09","2022-10-10","2022-08-01")', 1, '2022-10-10')
])
def test_find_date(params):
    s, o, r = params
    res = strutil.find_date(s, o)
    print(res)
    assert res == r


@pytest.mark.parametrize('params', [
    ('美团', 'meituan'),
    ('缘分啊', 'yuanfena'),
    ('meituan', 'meituan'),
])
def test_hanzi2pinyin(params):
    s1, s2 = params
    assert strutil.hanzi2pinyin(s1) == s2


@pytest.mark.parametrize('params', [
    '"id"',
    "'id'",
    'i!d',
    "id"
])
def test_check_special_chars_in_str(params):
    assert strutil.check_special_chars_in_str(params, '') == 'id'
