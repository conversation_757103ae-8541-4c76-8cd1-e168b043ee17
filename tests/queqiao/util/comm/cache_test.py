"""
Author: xiaohei
Date: 2022/7/8
Email: <EMAIL>
Host: xiaohei.info
"""
import pytest

from queqiao.util.comm.cache import local_cache, FromLocalCache


class TestCache:

    @local_cache(test=True)
    def use_local_cache(self, param):
        return f'{str(param)} from func'

    @pytest.mark.parametrize('param', [1, 2, 3])
    def test_local_cache(self, param):
        non_cache = self.use_local_cache(param)
        assert str(non_cache) == f'{str(param)} from func'
        assert isinstance(non_cache, str) is True
        cached = self.use_local_cache(param)
        assert str(cached) == f'{str(param)} from func with local cache'
        assert isinstance(cached, FromLocalCache)
