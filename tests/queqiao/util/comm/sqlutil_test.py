"""
Author: xiaohei
Date: 2022/7/12
Email: <EMAIL>
Host: xiaohei.info
"""
import pytest

from queqiao.util.comm import sqlutil
from queqiao.util.comm.sqlutil import convert_mysql_to_hive_type

col_sql_config1 = {
    'sql': 'select a from b',
    'cols': ['a']
}

col_sql_config2 = {
    'sql': 'select * from a',
    'cols': ['*']
}

col_sql_config3 = {
    'sql': 'select a,c,b from d',
    'cols': ['a', 'b', 'c']
}

col_sql_config4 = {
    'sql': 'select count(1) as row_num,max(col1) as col1_max,min(col1) as col1_min,max(col3) as coln_max,min(col3) as coln_min,count(distinct(length(col3))) as coln_len_num from tgdw.hive_sink_test1',
    'cols': ['row_num', 'col1_max', 'col1_min', 'coln_max', 'coln_min', 'coln_len_num']
}

col_sql_config5 = {
    'sql': 'select a as a1,b as b1 from (select a,b,c from test where a=1)t where b=2',
    'cols': ['a1', 'b1']
}

col_sql_config6 = {
    'sql': '''
    SELECT loan_no,
                partition_date,
                substr(open_date, 1, 7) AS open_month,
                floor(months_between(substr(partition_date, 1, 7), substr(open_date, 1, 7))) AS loan_open_mob,
                sum(principal_balance) principal_balance,
                sum(loan_total_principal) loan_total_principal,
                sum(if(overdue_cur_max_days >= '30', principal_balance, 0)) AS principal_balance_m2plus,
                max(CASE
                        WHEN overdue_cur_max_days >= '30' THEN 1
                        WHEN overdue_cur_max_days >= '20' THEN -1
                        ELSE 0
                    END) AS m2plus_ind /*max(if(overdue_cur_max_level>=2,1,0)) as m2plus_ind*/
         FROM mart_fspinno.dm_aggr_fin_loan_bill_d
         WHERE product_no = '00007'
           AND partition_date = last_day(partition_date)
           AND partition_date BETWEEN '2022-12-01' AND '2023-01-15'
           AND floor(months_between(substr(partition_date, 1, 7), substr(open_date, 1, 7))) = '40'
         GROUP BY 1,
                  2,
                  3,
                  4
    ''',
    'cols': ['loan_no', 'partition_date', 'open_month', 'loan_open_mob', 'principal_balance', 'loan_total_principal',
             'principal_balance_m2plus', 'm2plus_ind']
}

col_sql_config7 = {
    'sql': '''
    SELECT base.user_id,
             base.customer_no,
             apply_date,
             last_day(apply_date) AS apply_mth,
             coalesce(ln_0.m2plus_ind, 0) AS dpd30_mob8,
             coalesce(ln_1.m2plus_ind, 0) AS dpd30_mob8_v2,
             coalesce(p_user_cashloan_industry_tag_v1, -999) AS shf_industry_tag,
             coalesce(p_user_cashloan_edu_single_v1, -999) AS shf_edu_single,
             coalesce(p_user_cashloan_location_stability_lvl_v1, -999) AS shf_location_stability_lvl,
             user_successful_order_count_level_in_last_90d,
             QRY_TIMES_LN_L1ST_LEVEL,
             PS_SCORE_LEVEL,
             credit_score_level,
             RAND(666) AS randnum
      FROM
        (SELECT a.*
         FROM
           (SELECT user_id,
                   customer_no,
                   product_no,
                   serial_no,
                   apply_date,
                   creditor_name,
                   creditor_code,
                   user_type AS loan_type,
                   cust_type AS fundparty_user_type,
                   bank_capital_type AS fundparty_type,
                   risk_level AS risk_level_mt,
                   credit_score_level AS risk_level_bank,
                   credit_amount AS loan_total_amount,
                   loan_amount AS loan_success_amount,
                   flow_status_credit,
                   CASE
                       WHEN flow_status_credit = 'pass' THEN 1
                       ELSE 0
                   END AS apply_pass_ind,
                   flow_status_loan,
                   flow_status_final,
                   reject_msg_credit,
                   loan_no,
                   row_number() OVER (PARTITION BY user_id,
                                                   apply_date,
                                                   product_no
                                      ORDER BY apply_time DESC) AS rn
            FROM mart_finrisk.dm_risk_approve__ml_cooperative_process_inc_d
            WHERE apply_date BETWEEN '2022-12-01' AND '2022-12-31' --这里定参数申请日期
              AND cust_type = '机构新户'
              AND product_no = '00007' --这里定产品号
              AND loan_amount > 0
          
 ) a
         WHERE a.rn = 1) AS base
    ''',
    'cols': ['user_id', 'customer_no', 'apply_date', 'apply_mth', 'dpd30_mob8', 'dpd30_mob8_v2', 'shf_industry_tag',
             'shf_edu_single', 'shf_location_stability_lvl', 'user_successful_order_count_level_in_last_90d',
             'QRY_TIMES_LN_L1ST_LEVEL', 'PS_SCORE_LEVEL', 'credit_score_level', 'randnum']
}


class TestSqlutil:
    def test_parse_tables(self):
        sql = 'select a,b,c from (select * from a where b=1 union select * ' \
              'from b where c=1)x left join (select * from d)y on x.id=y.id limit 10'
        tables = sqlutil.get_tables(sql)
        cols = sqlutil.get_select_cols(sql)
        assert tables == {'a', 'b', 'd'}
        assert cols == ['a', 'b', 'c']

    @pytest.mark.parametrize('config', [col_sql_config1, col_sql_config2, col_sql_config3,
                                        col_sql_config4, col_sql_config5, col_sql_config6,
                                        col_sql_config7])
    def test_get_select_cols(self, config):
        sql = config['sql']
        cols = config['cols']
        select_cols = sqlutil.get_select_cols(sql)
        assert sorted(select_cols) == sorted(cols)

    def test_convert_mysql_to_hive_type(self):
        # 示例使用
        mysql_types = ['bigint', 'int', 'smallint', 'tinyint', 'decimal(10,2)', 'double', 'float', 'binary',
                       'varbinary',
                       'char(10)', 'varchar(32)', 'mediumtext', 'text', 'datetime', 'time', 'timestamp', 'date', 'json']

        for mysql_type in mysql_types:
            hive_type = convert_mysql_to_hive_type(mysql_type)
            print(f"MySQL Type: {mysql_type} -> Hive Type: {hive_type}")
