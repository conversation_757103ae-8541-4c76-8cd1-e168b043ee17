"""
Author: xiaohei
Date: 2022/7/12
Email: <EMAIL>
Host: xiaohei.info
"""
import pytest

from queqiao.util.comm import sqlutil, osutil
from queqiao.util.mt.talos import TalosClient

test_tablename = 'mart_fspinno_queqiao.pytest'
join_test_tablename = 'mart_fspinno_queqiao.pytest_parted'


# 在本地连接生产测试
class TestTalosClient:
    def setup_class(self):
        self.client = TalosClient()
        self.client.open()
        # self.client.exec(f'drop table if exists {test_tablename}')

    def teardown_class(self):
        # self.client.exec(f'drop table if exists {test_tablename}')
        self.client.close()

    # @pytest.mark.parametrize('sql', [
    #     f'create table {test_tablename}(id int, name string, age int)',
    #     f'insert into {test_tablename} values(1,"jiangyuande",20)',
    #     f'insert into {test_tablename} values(2,"xiaoh<PERSON>",22)'
    # ])
    # def test_exec(self, sql):
    #     assert self.client.exec(sql)

    @pytest.mark.parametrize('params', [
        (f'select * from {test_tablename}', None, None),
        (f'select id,age from {test_tablename}', '#', None),
        (f'select name,age from {test_tablename}', '|+|', '/tmp/pytest'),
        (f'select a.name from {test_tablename} a left join {join_test_tablename} b on a.id=b.id where b.id is null',
         None,
         None),
    ])
    def test_query(self, params):
        sql, col_sep, save_path = params
        select_cols = sqlutil.get_select_cols(sql)
        select_cols = [c.split('.')[-1] for c in select_cols]
        select_tables = sqlutil.get_tables(sql)
        table_cnt = 2 if 'join' in sql else 1
        assert len(select_tables) == table_cnt
        select_cols = select_cols if '*' not in select_cols else ['id', 'name', 'age']
        if col_sep:
            self.client.set_default_col_sep(col_sep)
        if save_path:
            self.client.set_default_save_path(save_path)
        query_result = self.client.query(sql)
        assert query_result['status'] == 0
        assert query_result['total'] == 2
        assert len(query_result['columns']) == len(select_cols)
        cols = [col['name'] for col in query_result['columns']]
        assert cols == select_cols
        assert osutil.exists(query_result['file_path'])
        with open(query_result['file_path'], 'r') as f:
            lines = f.readlines()
            if col_sep:
                assert len(lines[0].split(col_sep)) == len(select_cols)
            print(lines)

    def test_schema(self):
        self.client.query(f"select * from {test_tablename}")
        schema = self.client.schema(test_tablename)
        print(schema)
        cols = [col['name'] for col in schema]
        assert len(schema) == 3
        assert 'id' in cols and 'name' in cols and 'age' in cols
