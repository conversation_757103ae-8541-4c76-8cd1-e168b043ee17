"""
Author: xiaohei
Date: 2022/7/12
Email: <EMAIL>
Host: xiaohei.info
"""
import pytest

from instance.default import HOSTNAME, IPADDR, API_PORT, API_VERSION
from queqiao.util.comm.dtutil import timer
from queqiao.util.conn.ldap import LdapAuth
from queqiao.util.mt.daxiang import DxPush, Udb, DxAudit
from queqiao.util.mt.org import OrgClient
from tests import apply
from tests.queqiao.util.conn.ldap_test import ldap_configs


class TestDxPush:
    def test_push_msg(self):
        assert DxPush.push_msg(f'send by test case from {HOSTNAME} in {IPADDR}', 'jiangyuande')


class TestUdb:
    def test_get_uid(self):
        uid = Udb.get_uid('jiangyuande')
        print(uid)
        assert uid


class TestDxAudit:

    @pytest.mark.parametrize('param', [
        ('jiangyuande', 'queqiao_user_sys', '李大超'),
        ('jiangsh<PERSON>zhuang', 'queqiao_user_sys', '李大超'),
        ('chentianzeng', 'queqiao_user_bs', '李大超,华蔚,陆晨,刁英楠'),
        ('ancongxue', 'queqiao_visitor', '')
    ])
    def test_create_audit_batch(self, param):
        uname, ugroup, uapprovers = param
        ldap = LdapAuth(**ldap_configs)
        user = ldap.get_user(uname)
        assert user.groups
        # 获取第一个queqiao_user_用户组
        group = [g for g in user.groups if g.startswith('queqiao_user_')]
        if not group:
            assert uname == 'ancongxue'
            return
        group = group[0]
        assert group == ugroup
        org = OrgClient.from_flask_config()
        leader, org = org.get_emp_info(uname)

        content = {
            'apply_id': apply.id,
            'create_time': timer.now().datetime,
            'create_user': uname,
            'leader': leader,
            'org': org,
            'scenario': '单元测试',
            'describe': '单元测试使用，请忽略',
            'security_level': 'C1',
            'information': 'itsm:123',
            'task_detail': f'http://{HOSTNAME}:{API_PORT}/api/{API_VERSION}/apply/123',
            'project': 'unittest_project'
        }

        res = DxAudit.create_audit(uname, ugroup, content)
        assert res['code'] == 0
        detail = DxAudit.get_audit_detail(uname, res['data']['apply_id'])
        assert detail['data']['all_approvers'] == uapprovers
        push = DxAudit.push_agree(uname, res['data']['apply_id'])
        assert push['code'] == 0

    def test_create_audit_single(self):
        uname, ugroup = ('jiangyuande', 'queqiao_user_sys')
        ldap = LdapAuth(**ldap_configs)
        user = ldap.get_user(uname)
        assert user.groups
        # 获取第一个queqiao_user_用户组
        group = [g for g in user.groups if g.startswith('queqiao_user_')]
        group = group[0]
        assert group == ugroup
        org = OrgClient.from_flask_config()
        leader, org = org.get_emp_info(uname)

        content = {
            'apply_id': apply.id,
            'create_time': timer.now().datetime,
            'create_user': uname,
            'leader': leader,
            'org': org,
            'scenario': '单元测试',
            'describe': '单元测试使用，请忽略',
            'security_level': 'C1',
            'information': 'itsm:123',
            'task_name': 'task1',
            'where2where': 'ftp(光大) -> hive(美团)',
            'trans_type': '单次传输',
            'result_size': '1000000',
            'engine': 'DataX',
            'execution_meta': 'select * from abc',
            'project': 'unittest_project'
        }

        res = DxAudit.create_audit(uname, ugroup, content)
        assert res['code'] == 0
