"""
Author: xiaohei
Date: 2022/7/18
Email: <EMAIL>
Host: xiaohei.info
"""
import pytest

from queqiao.conf import Config
from queqiao.conf.errors import InternalException
from queqiao.core.auth import Certifier
from queqiao.util.comm import strutil
from tests import app


class TestCertifier:
    @pytest.mark.parametrize('params', [
        # <PERSON>ie
        ({}, None, 'CookieCertifier'),
        # Ldap-error
        ({}, 'LDAP', None),
        # LDAP
        ({'username': '', 'password': ''}, None, 'LDAPCertifier'),
        # LDAP
        ({'username': '', 'password': ''}, 'LDAP', 'LDAPCertifier'),
        # MTSSO
        # ({'sid': ''}, 'MTSSO', 'MTSSOCertifier')
    ])
    def test_get_author(self, params):
        config, atype, btype = params
        if not btype:
            with pytest.raises(InternalException) as err:
                author = Certifier.get_author(config, atype)
            assert isinstance(err.value, InternalException)
            print(f'【ERROR】author init failed: {err.value.args[0]}')
            assert 'missing' in err.value.args[0]
        else:
            author = Certifier.get_author(config, atype)
            assert author.__class__.__name__ == btype

    @pytest.mark.parametrize('params', [
        ('jiangyuande', '1234', 'queqiao_admin', 'ARD15Xtf'),
        ('jiangyuande', '4321', 'queqiao_user_bs,queqiao_user_rd', 'ARD15Xtf'),
        ('xiaohei', '1234', 'queqiao_visitor', '123')
    ])
    def test_get_user_token(self, params):
        uid, gid, groups, salt = params
        aid = Certifier.get_user_aid(groups)
        is_admin = '1' if 'queqiao_admin' in groups else '0'
        assert aid == is_admin
        token = Certifier.get_user_token(uid, gid, aid)
        print(f'user token: {token}')
        mix = uid + gid + aid + salt
        check_token = strutil.md5(mix)
        if salt == '123':
            assert token != check_token
        else:
            assert token == check_token

    @pytest.mark.parametrize('params', [
        # Cookie
        # ({'uid': 'jiangyuande', 'aid': '0', 'gid': '4321', 'token': 'c5d38b4e6fae528482fb01c326a787fa'}, None),
        # LDAP
        ({'username': 'jiangyuande', 'password': 'password'}, None),
        ({'username': 'chentianzeng', 'password': 'password'}, None),
        # MTSSO
        # ({
        #      'sid': ssoid},
        #  'MTSSO')
    ])
    def test_verify_certificate(self, params):
        config, atype = params
        author = Certifier.get_author(config, atype)
        assert author.verify_certificate()
        user = Config({'test': 1})
        author.init_user_properties(user)
        for i in ['uid', 'groups', 'gid']:
            assert hasattr(user, i)
            print(f'{i}: {getattr(user, i)}')
        with app.test_request_context():
            noresp = author.not_auth_resp()
            print(noresp)
            assert noresp
