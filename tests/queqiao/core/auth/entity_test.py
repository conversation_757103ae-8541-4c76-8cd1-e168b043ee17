"""
Author: xiaohei
Date: 2022/7/18
Email: <EMAIL>
Host: xiaohei.info
"""
import pytest

from queqiao.core.auth import Certifier
from queqiao.core.auth.entity import User
from queqiao.dba.models import Project, ProjectUserRelation
from tests import app


class TestUser:

    def setup_class(self):
        if not Project.exists(create_user='jiangyuande', update_user='jiangyuande', name='user_test1', desc='test',
                              admins='jiangyuande,chentianzeng'):
            project = Project.new(create_user='jiangyuande', update_user='jiangyuande', name='user_test1', desc='test',
                                  admins='jiangyuande,chentianzeng')
            project.save()
            relation = ProjectUserRelation.new(project_id=project.id, user_id='jiangyuande', role_type=1, is_default=0)
            relation.save()
            relation = ProjectUserRelation.new(project_id=project.id, user_id='chentianzeng', role_type=1, is_default=1)
            relation.save()

        if not Project.exists(create_user='xiaohei', update_user='xiaohei', name='user_test2', desc='test',
                              admins='xiaohei'):
            project = Project.new(create_user='xiaohei', update_user='xiaohei', name='user_test2', desc='test',
                                  admins='xiaohei')
            project.save()
            relation = ProjectUserRelation.new(project_id=project.id, user_id='jiangyuande', role_type=0, is_default=1)
            relation.save()

    # def teardown_class(self):
    #     Project.delete_by()
    #     ProjectUserRelation.delete_by()

    @pytest.mark.parametrize('params', [
        # LDAP
        ({'username': 'jiangyuande', 'password': 'password'}, {'gid': 274200008}),
        # MTSSO
        # ({
        #      'sid': ssoid},
        #  {'gid': 2843334})
    ])
    def test_init(self, params):
        config, check = params
        user = User(**config)
        assert user.uid == 'jiangyuande'
        # assert user.aid == '0'
        assert user.gid == check['gid']
        assert user.token == Certifier.get_user_token(user.uid, user.gid, user.aid)
        assert len(user.projects) == 5
        print(f'projects: {user.projects}')
        # print(f'=======groups: {user.groups}')
        assert 'queqiao_user_sys' in user.groups
        # assert user.is_admin
        assert not user.is_datard
        assert user.is_sysrd
        # init中的配置
        default_project = Project.get_one(name='pytest_project')
        assert user.project_id == default_project.id
        with app.test_request_context():
            noresp = user.unauthorized_resp
            assert noresp
