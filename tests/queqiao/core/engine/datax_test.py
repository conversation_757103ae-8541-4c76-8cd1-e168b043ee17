"""
Author: xiaohei
Date: 2022/7/31
Email: <EMAIL>
Host: xiaohei.info
"""

from queqiao.core.engine import EngineProxy
from queqiao.util.comm import osutil
from tests import execution_mysql2file, file_sink_config


class TestDataX:

    def test_run(self):
        engine = EngineProxy.get_engine(execution_mysql2file.id)
        engine.run()
        result_files = osutil.list_files(file_sink_config["file_path"], prefix=file_sink_config['file_name'])
        print(f'result_files: {result_files}')
        assert len(result_files) == 1
        result_file = result_files[0]
        result_content = osutil.calls(f'cat {result_file}')
        print(f'result_content: {result_content}')
        result_arr = result_content[0].split('\x01')
        assert len(result_arr) == 11
        assert result_arr[3] == 'Ftplink'
