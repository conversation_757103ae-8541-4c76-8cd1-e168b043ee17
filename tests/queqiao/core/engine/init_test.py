"""
Author: xiaohei
Date: 2022/7/25
Email: <EMAIL>
Host: xiaohei.info
"""

from instance.default import SERVICE_PATH
from queqiao.core.engine import engine_pool, EngineProxy
from queqiao.util.comm import osutil
from tests import execution_talos2ftp


class TestEngine:

    def test_engine_pool(self):
        cmd = f"grep 'class ' {SERVICE_PATH}/core/engine/*_engine.py | " + "awk '{print $NF}' | awk -F 'Engine' '{print $1}'"
        print(f"cmd: {cmd}")
        engines = osutil.calls(cmd)
        print(f'get engines({len(engines)}): {engines}')
        assert len(engines) == len(engine_pool)
        for key in engine_pool.keys():
            assert key in engines
            print(f'key: {key}, value: {engine_pool[key]}')

    def test_engine_proxy(self):
        engine = EngineProxy.get_engine(execution_talos2ftp.id)
        print(engine)
        assert engine
