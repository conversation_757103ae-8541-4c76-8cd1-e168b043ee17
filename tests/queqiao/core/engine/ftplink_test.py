"""
Author: xiaohei
Date: 2022/7/31
Email: <EMAIL>
Host: xiaohei.info
"""
import json

import pytest

from instance.default import system_env
from queqiao.conf.enums import ExecutionStatus
from queqiao.core.engine import EngineProxy
from queqiao.core.execute.execution import Execution
from queqiao.dba import db
from queqiao.dba.models import FtplinkHistoryFile
from queqiao.util.comm import strutil, osutil
from queqiao.util.comm.dtutil import timer
from tests import file_source_config, ftp_sink_config, ftp_source_config, hive_sink_config, hive_source_config, \
    hivesql_source_config, talos_source_config, meituan_org, tonglian_org, engine_ftplink, ftp_test_path, sftp_client, \
    file_test_content, pyhive_client, ftp_dsn, hive_dsn, hivesql_dsn, talos_dsn, task_mysql2file

self_file_source_config = file_source_config.copy()
self_file_source_config['name'] = 'file'

self_ftp_source_config = ftp_source_config.copy()
self_ftp_source_config['name'] = 'ftp'
self_ftp_source_config['dsn'] = ftp_dsn.to_dict()

self_ftp_source_config1 = {
    'ok_file': 'wait4.ready',
    'max_waiting_hours': '0.01',
    'do_not_upload_ok': 10
}
self_ftp_source_config1 = dict(self_ftp_source_config, **self_ftp_source_config1)

self_ftp_sink_config = ftp_sink_config.copy()
self_ftp_sink_config['name'] = 'ftp'
self_ftp_sink_config['dsn'] = ftp_dsn.to_dict()

self_ftp_sink_config1 = {'file_name': f'test_from_ftplink_engine_test_copyed_{timer.now().datekey}.txt'}
self_ftp_sink_config1 = dict(self_ftp_sink_config, **self_ftp_sink_config1)

local_set = [
    (self_file_source_config, self_ftp_sink_config),
    (self_ftp_source_config, self_ftp_sink_config1),
    (self_ftp_source_config1, self_ftp_sink_config1),
]
self_hive_source_config = hive_source_config.copy()
self_hive_source_config['name'] = 'hive'
self_hive_source_config['dsn'] = hive_dsn.to_dict()

self_hivesql_source_config = hivesql_source_config.copy()
self_hivesql_source_config[
    'sql'] = f"select * from tgdw.test_by_pytest_parted_date_auto where partition_date='{timer.now().delta(1).date}'"
self_hivesql_source_config['name'] = 'hivesql'
self_hivesql_source_config['dsn'] = hivesql_dsn.to_dict()

self_talos_source_config = talos_source_config.copy()
self_talos_source_config['name'] = 'talos'
self_talos_source_config['dsn'] = talos_dsn.to_dict()

self_hive_sink_config = hive_sink_config.copy()
self_hive_sink_config['name'] = 'hive'
self_hive_sink_config['dsn'] = hive_dsn.to_dict()

test_set = [
    (self_file_source_config, self_hive_sink_config),
    (self_hive_source_config, self_ftp_sink_config),
    (self_ftp_source_config, self_hive_sink_config),
    (self_hivesql_source_config, self_ftp_sink_config),
    (self_talos_source_config, self_ftp_sink_config)
]


class TestFtplink:
    # def test_talos_queue_run(self):
    #     source_configs, sink_configs = self_talos_source_config, self_ftp_sink_config
    #     execution_params = {'source': source_configs, 'sink': sink_configs, 'current': timer.now().delta(1).date}
    #     execution = Execution.new(
    #         status=ExecutionStatus.INIT.value,
    #         qid=strutil.uid(),
    #         task_id=1,
    #         project_id=1,
    #         source_org_id=meituan_org.id,
    #         sink_org_id=tonglian_org.id,
    #         params=json.dumps(execution_params),
    #         engine_id=engine_ftplink.id,
    #         create_user='jiangyuande', update_user='jiangyuande'
    #     )
    #     execution.save()
    #     engine = EngineProxy.get_engine(execution.id)
    #     engine.run()
    #     execution = Execution.get_one(qid=execution.qid)
    #     print(f'====execution id: {execution.id}')
    #     db.session.refresh(execution)

    @pytest.mark.parametrize('params', local_set)
    def test_local_run(self, params):
        source_configs, sink_configs = params
        execution_params = {'source': source_configs, 'sink': sink_configs, 'current': timer.now().delta(1).date}
        execution = Execution.new(
            status=ExecutionStatus.INIT.value,
            qid=strutil.uid(),
            task_id=1,
            project_id=1,
            source_org_id=meituan_org.id,
            sink_org_id=tonglian_org.id,
            params=json.dumps(execution_params),
            engine_id=engine_ftplink.id,
            create_user='jiangyuande', update_user='jiangyuande'
        )
        execution.save()
        engine = EngineProxy.get_engine(execution.id)
        engine.run()
        execution = Execution.get_one(qid=execution.qid)
        print(f'====execution id: {execution.id}')
        db.session.refresh(execution)
        if 'do_not_upload_ok' in source_configs:
            assert ExecutionStatus(execution.status) == ExecutionStatus.FAILED
            return
        assert ExecutionStatus(execution.status) == ExecutionStatus.SUCCESS
        remote_file = f'{sink_configs["file_path"]}/{sink_configs["file_name"]}' if sink_configs[
            'file_path'].startswith(
            '/') else f'{ftp_test_path}/{sink_configs["file_path"]}/{sink_configs["file_name"]}'
        history_file = FtplinkHistoryFile.get_one(remote_file=remote_file)
        assert history_file
        print(f'get ftplink history file: {history_file.to_dict()}')
        assert history_file.create_user == 'jiangyuande'
        assert history_file.source == source_configs['name']
        assert history_file.sink == sink_configs['name']
        # assert history_file.local_file == local_filepath
        assert history_file.file_type == 'txt'
        assert history_file.col_list == 'col1,col2,col3' if 'table_cols' not in source_configs else source_configs[
            'table_cols']
        # 结果文件
        assert sftp_client.read(remote_file).strip() == file_test_content.strip()
        ok_content = sftp_client.read(f'{remote_file}.ok')
        print(f'========ok_content')
        print(ok_content)
        ok_json = json.loads(ok_content)
        assert ok_json['row_num'] == 2
        assert ok_json['col_cnt'] == 3

    @pytest.mark.skipif(system_env != 'test', reason='test in test env')
    @pytest.mark.test
    @pytest.mark.parametrize('params', test_set)
    def test_test_run(self, params):
        source_configs, sink_configs = params
        execution_params = {'source': source_configs, 'sink': sink_configs, 'current': timer.now().delta(1).date}
        execution = Execution.new(
            status=ExecutionStatus.INIT.value,
            qid=strutil.uid(),
            task_id=1,
            project_id=1,
            source_org_id=meituan_org.id,
            sink_org_id=tonglian_org.id,
            params=json.dumps(execution_params),
            engine_id=engine_ftplink.id,
            create_user='jiangyuande', update_user='jiangyuande'
        )
        execution.save()
        engine = EngineProxy.get_engine(execution.id)
        engine.run()
        execution = Execution.get_one(qid=execution.qid)
        print(f'====execution id: {execution.id}')
        db.session.refresh(execution)
        assert ExecutionStatus(execution.status) == ExecutionStatus.SUCCESS

        if sink_configs['name'] != 'ftp':
            sql = f'select * from tgdw.{sink_configs["table_name"]}'
            query_result = pyhive_client.query(sql)
            print(f'========query_result: {query_result}')
            assert query_result['status'] == 0
            assert query_result['total'] >= 2
            print(f'===========insert into total count: {query_result["total"]}')
            partition_key = 'partition_date' if 'partition_key' not in sink_configs or not sink_configs[
                "partition_key"] else sink_configs['partition_key']
            assert len(query_result['columns']) == 3 if sink_configs['table_type'] != 'partition' else 4
            assert osutil.exists(query_result['file_path'])
            with open(query_result['file_path'], 'r') as f:
                lines = f.readlines()
                print(lines)
                if 'parted_date' in sink_configs['table_name']:
                    assert lines[-1].split('\x01')[-1].strip() == timer.now().delta(1).date
                    lines1 = ['\x01'.join(l.split('\x01')[:-1]) for l in lines]
                    # 取最后两行
                    assert '\n'.join(lines1[len(lines1) - 2:]) == file_test_content

            tabletype_sql = f'show create table tgdw.{sink_configs["table_name"]}'
            query_result = pyhive_client.query(tabletype_sql)
            with open(query_result['file_path'], 'r') as f:
                lines = f.readlines()
                print(''.join(lines))
                if 'parted' in sink_configs['table_name']:
                    assert f'`{partition_key}` string' in \
                           ''.join(lines).replace('\n', '').split('PARTITIONED BY (')[1].split(')')[0].strip()
                else:
                    assert 'partitioned by'.upper() not in ''.join(lines).replace('\n', '')
        else:
            remote_file = f'{sink_configs["file_path"]}/{sink_configs["file_name"]}' if sink_configs[
                'file_path'].startswith(
                '/') else f'{ftp_test_path}/{sink_configs["file_path"]}/{sink_configs["file_name"]}'
            history_file = FtplinkHistoryFile.get_one(remote_file=remote_file, source=source_configs['name'],
                                                      sink=sink_configs['name'])
            assert history_file
            print(f'get ftplink history file: {history_file.to_dict()}')
            assert history_file.create_user == 'jiangyuande'
            assert history_file.source == source_configs['name']
            assert history_file.sink == sink_configs['name']
            # assert history_file.local_file == local_filepath
            assert history_file.file_type == 'txt'
            table_name = source_configs['table_name'] if 'table_name' in source_configs else source_configs['sql']
            print(f'======history_file.col_list: {history_file.col_list}')
            assert history_file.col_list == 'col1,col2,col3,partition_date' if 'parted_date' in table_name else 'col1,col2,col3'
            # 结果文件
            remote_content = sftp_client.read(remote_file).strip()
            if 'talos' not in source_configs['name']:
                for i in file_test_content.split('\n'):
                    assert i in remote_content
            ok_content = sftp_client.read(f'{remote_file}.ok')
            print(f'========ok_content')
            print(ok_content)
            ok_json = json.loads(ok_content)
            assert ok_json['row_num'] == 2
            if 'talos' not in source_configs['name']:
                assert ok_json['col_cnt'] == 3 if 'parted' not in table_name else 4
