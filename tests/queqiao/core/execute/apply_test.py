"""
Author: xiaohei
Date: 2022/8/18
Email: <EMAIL>
Host: xiaohei.info
"""
import pytest

from queqiao.conf.enums import ApplyStatus
from queqiao.conf.errors import NotAvaliableException
from queqiao.core.execute.apply import Apply
from queqiao.dba import db
from tests import apply


class TestApply:

    def teardown_class(self):
        apply.status = ApplyStatus.AGREE.value
        apply.save()

    def test_agree(self):
        assert apply.status == ApplyStatus.AGREE.value
        with pytest.raises(NotAvaliableException) as why:
            Apply.agree(apply.id, 'jiangyuande')
        print(f'!!!!{why.value}!!!')
        apply1 = Apply.get(id=apply.id)
        apply1.status = ApplyStatus.APPROVING.value
        apply1.save()
        Apply.agree(apply1.id, 'jiangyuande')
        db.session.refresh(apply1)
        assert apply1.status == ApplyStatus.AGREE.value

    def test_reject(self):
        with pytest.raises(NotAvaliableException) as why:
            Apply.reject(apply.id, 'jiangyuande', '测试被拒绝')
        print(f'!!!!{why.value}!!!')
        apply1 = Apply.get(id=apply.id)
        apply1.status = ApplyStatus.APPROVING.value
        apply1.save()
        Apply.reject(apply1.id, 'jiangyuande', '测试被拒绝')
        db.session.refresh(apply1)
        assert apply1.status == ApplyStatus.REJECT.value
