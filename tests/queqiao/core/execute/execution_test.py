"""
Author: xiaohei
Date: 2022/8/1
Email: <EMAIL>
Host: xiaohei.info
"""
import time

from celery.result import AsyncResult

from queqiao.conf.enums import ExecutionStatus
from queqiao.core.execute.execution import Execution
from queqiao.dba import db
from tests import execution_talos2ftp


class TestExecution:
    def test_execute_by_id(self):
        Execution.execute(execution_talos2ftp.id, delay=False)
        execution = Execution.get(id=execution_talos2ftp.id)
        db.session.refresh(execution)
        assert ExecutionStatus(execution.status) == ExecutionStatus.SUCCESS

    def test_execute_async(self):
        Execution.execute(execution_talos2ftp.id)
        time.sleep(3)
        execution = Execution.get(id=execution_talos2ftp.id)
        db.session.refresh(execution)
        task_id = execution.qid
        print(f'get async task id: {task_id}')
        task_result = AsyncResult(task_id)
        assert task_result
        while not task_result.ready():
            print(f'task {task_id} is running now, status: {task_result.status}')
            time.sleep(1)
        assert task_result.successful()
