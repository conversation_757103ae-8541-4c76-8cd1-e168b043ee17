"""
Author: xiaohei
Date: 2022/8/1
Email: <EMAIL>
Host: xiaohei.info
"""
import time

import pytest
from celery.result import AsyncResult

from queqiao.conf.enums import ExecutionStatus, TaskStatus
from queqiao.conf.errors import NotAvaliableException
from queqiao.core.execute.task import Task
from queqiao.core.execute.execution import Execution
from queqiao.dba import db
from tests import task_talos2ftp, project, task_file2ftp_not_admin


class TestTask:

    def test_runable(self):
        project.is_delete = 1
        project.save()
        with pytest.raises(NotAvaliableException) as exc:
            Task.execute(task_talos2ftp.id, delay=False)
        print(exc.value)
        project.is_delete = 0
        project.save()
        task_talos2ftp.status = TaskStatus.APPROVING.value
        task_talos2ftp.save()
        with pytest.raises(NotAvaliableException) as exc:
            Task.execute(task_talos2ftp.id, delay=False)
        print(exc.value)
        task_talos2ftp.status = TaskStatus.SUCCESS.value
        task_talos2ftp.save()

    def test_execute(self):
        execution_id = Task.execute(task_talos2ftp.id, params={'current': '2022-01-10'}, delay=False)
        print(f'get execution_id: {execution_id}')
        execution = Execution.get(id=execution_id)
        db.session.refresh(execution)
        assert ExecutionStatus(execution.status) == ExecutionStatus.SUCCESS

    def test_execute_async(self):
        execution_id = Task.execute(task_talos2ftp.id)
        print(f'get execution_id: {execution_id}')
        time.sleep(2)
        execution = Execution.get(id=execution_id)
        db.session.refresh(execution)
        task_id = execution.qid
        print(f'get async task id: {task_id}')
        task_result = AsyncResult(task_id)
        assert task_result
        while not task_result.ready():
            print(f'task {task_id} is running now, status: {task_result.status}')
            time.sleep(1)
        assert task_result.successful()

    def test_execute_batch(self):
        execution_ids = Task.execute_batch([task_talos2ftp.id, task_file2ftp_not_admin.id])
        print(f'get execution ids: {execution_ids}')
        time.sleep(2)
        for execution_id in execution_ids:
            execution = Execution.get(id=execution_id)
            db.session.refresh(execution)
            task_id = execution.qid
            print(f'get async task id: {task_id}')
            task_result = AsyncResult(task_id)
            assert task_result
            while not task_result.ready():
                print(f'task {task_id} is running now, status: {task_result.status}')
                time.sleep(1)
            assert task_result.successful()
