"""
Author: xiaohei
Date: 2022/7/17
Email: <EMAIL>
Host: xiaohei.info
"""
import pytest

from queqiao.conf.system import SystemConfig
from queqiao.lib.push import Pusher, FilePusher
from queqiao.util.comm import osutil

alarm_log = SystemConfig.read('ALARM_FILE_PATH')


class TestPusher:
    def setup_class(self):
        osutil.rm(alarm_log)

    def teardown_class(self):
        osutil.rm(alarm_log)

    @pytest.mark.parametrize('ptype', [None, 'Daxiang', 'File'])
    def test_push(self, ptype):
        pusher = Pusher.get_pusher(ptype)
        msg = 'test from TestPusher'
        receivers = 'jiangyuande'
        pusher.push(msg, receivers)
        if isinstance(pusher, FilePusher):
            assert osutil.exists(alarm_log)
            with open(alarm_log, 'r') as log:
                lines = log.readlines()
                print(lines)
                assert len(lines) == 1
                assert msg in lines[0]
            pusher.push('last message', receivers)
            assert osutil.wc(alarm_log) == 2
