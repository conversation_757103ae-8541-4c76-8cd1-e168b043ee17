"""
Author: xiaohei
Date: 2022/7/17
Email: <EMAIL>
Host: xiaohei.info
"""
import pytest

from queqiao.lib.approve import Approver
from tests import apply


class TestApprover:

    @pytest.mark.parametrize('ptype', [None, 'Daxiang', 'Inner'])
    def test_single_approver(self, ptype):
        approver = Approver.get_approver(apply, ptype)
        assert approver.create()
        detail = approver.detail()
        approvers = 'jiangyuande' if ptype == 'Inner' else '李大超'
        assert detail['all_approvers'] == approvers
        approver.hurry_up()

    def test_batch_approver(self):
        apply.create_user = 'chentianzeng'
        apply.save()
        approver = Approver.get_approver(apply)
        assert approver.create()
        detail = approver.detail()
        assert detail['all_approvers'] == '李大超,华蔚,陆晨,刁英楠'
        approver.hurry_up()
