"""
Author: xiaohei
Date: 2022/7/17
Email: <EMAIL>
Host: xiaohei.info
"""
import pytest

from queqiao.lib.schedule import Scheduler
from tests import task_talos2ftp


class TestScheduler:
    @pytest.mark.parametrize('ptype', [None, 'Cantor', 'Azkaban'])
    def test_get_cmd(self, ptype):
        scheduler = Scheduler.get_scheduler(task_talos2ftp, ptype)
        cmd = scheduler.gen_cmd()
        print(f'get scheduler cmd: {cmd}')
        assert cmd
        assert task_talos2ftp.etl_name in cmd
