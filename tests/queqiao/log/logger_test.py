"""
Author: xiaohei
Date: 2022/7/9
Email: <EMAIL>
Host: xiaohei.info
"""

import pytest

from instance.default import LOG_PATH, SYSTEM_USER
from queqiao.conf.env import curr_queqiao_app
from queqiao.log import LogFactory
from queqiao.util.comm import osutil
from queqiao.util.comm.dtutil import timer


class TestLogger:
    def test_get_curr_app(self):
        app = curr_queqiao_app
        assert app == 'console'

    @pytest.mark.parametrize('app', ['api', 'exe', 'wac', 'console'])
    def test_get_app_logger(self, app):
        curr_date = timer.now()
        log_file = f'{LOG_PATH}/queqiao-{app}.{SYSTEM_USER}.log.{curr_date.date}'
        logger = LogFactory.get_logger(app)
        assert logger.name == app
        test_msg = 'from test'
        logger.info(test_msg)
        if app != 'console':
            assert osutil.exists(log_file)
            with open(log_file, 'r') as l:
                lines = l.readlines()
                print(lines)
                assert lines[-1].strip().endswith(test_msg)
            osutil.rm(log_file)

    def test_get_self_logger(self):
        log_name = 'from_test'
        logger1 = LogFactory.get_logger(log_name)
        logger1.info('from test')
        logger1_hash = logger1.__hash__()
        logger2 = LogFactory.get_logger(log_name)
        logger2_hash = logger2.__hash__()
        assert logger1_hash == logger2_hash
