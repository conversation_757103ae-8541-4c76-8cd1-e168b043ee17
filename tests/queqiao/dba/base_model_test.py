"""
Author: xiaohei
Date: 2022/7/11
Email: <EMAIL>
Host: xiaohei.info
"""
import pytest

import tests
from queqiao.dba.extend_model import Apply, Alarm, TaskType
from queqiao.dba.models import Engine, ComponentConfig, Org, Component

test_by_xhh = 'xhh'
test_orgs = [('org1', test_by_xhh), ('org2', test_by_xhh), ('org3', test_by_xhh)]


class TestBaseModel:
    @pytest.mark.parametrize('instance', [
        (Apply, ['scenario', 'describe', 'security_level', 'status', 'project_id'], ['information']),
        (Engine, ['name', 'enable'], ['cmd', 'comment', 'params']),
        (Alarm, ['receivers', 'keep_quiet', 'execution_id'], ['content']),
        (ComponentConfig, ['name', 'name_cn', 'required', 'cid'], ['type', 'default', 'demo', 'comment'])
    ])
    def test_get_required_and_optional_fields(self, instance):
        cls, rf, of = instance
        required_fileds, optional_fields = cls.get_required_and_optional_fields()
        assert sorted(required_fileds) == sorted(rf)
        assert sorted(optional_fields) == sorted(of)

    def setup_class(self):
        print('clean at class start')
        Org.delete_by(create_user=test_by_xhh)
        Component.delete_by(create_user=test_by_xhh)
        TaskType.delete_by(create_user=test_by_xhh)

    def teardown_class(self):
        print('clean at class end')
        Org.delete_by(create_user=test_by_xhh)
        Component.delete_by(create_user=test_by_xhh)
        TaskType.delete_by(create_user=test_by_xhh)

    def test_delete_by(self):
        assert len(Org.get(see_delete=1)) == 0
        assert len(Component.get(see_delete=1)) == 0
        assert len(TaskType.get(see_delete=1)) == 0

    def test_new(self):
        for org_name, org_user in test_orgs:
            org = Org.new(create_user=org_user, update_user=org_user, name=org_name, ad_users=org_user)
            org.save()
        assert len(Org.get(create_user=test_by_xhh)) == len(test_orgs)

    def test_exists(self):
        assert Org.exists(name='org1')
        assert Org.exists(name='org1', create_user=test_by_xhh)
        assert not Org.exists(name='org1', create_user='xhhh')

    def test_get_only_one(self):
        org = Org.get_one(name='org1')
        assert isinstance(org, Org)
        assert org.create_user == test_by_xhh

    def test_to_dict(self):
        org = Org.get_one(name='org1')
        d1 = org.to_dict()
        d2 = org.to_dict(includes=['name', 'ad_users'])
        d3 = org.to_dict(excludes=['name', 'ad_users'])

        assert len(d1) == len(org.columns)
        assert len(d2) == 2
        assert list(d2.keys()) == ['name', 'ad_users']
        assert len(d3) == len(set(org.columns).difference(['name', 'ad_users']))
        assert 'name' not in d3

    def test_safety_delete(self):
        org = Org.get_one(name='org1')
        org.safety_delete()
        assert org.is_delete == 1

    def test_get_see_delete(self):
        org = Org.get_one(name='org1')
        assert not org
        org = Org.get_one(name='org1', see_delete=1)
        assert org.is_delete == 1
        org = Org.get_one(name='org2')
        assert org.is_delete == 0

    def test_safety_delete_by(self):
        Org.safety_delete_by(create_user=test_by_xhh)
        orgs = Org.get(create_user=test_by_xhh)
        assert not orgs
        orgs = Org.get(see_delete=1, create_user=test_by_xhh)
        assert len(orgs) == len(test_orgs)

    def test_foreign_relation(self):
        source_component = Component.new(operator='source', datasource='testhive', create_user=test_by_xhh,
                                         update_user=test_by_xhh)
        source_component.save()
        sink_component = Component.new(operator='sink', datasource='testftp', create_user=test_by_xhh,
                                       update_user=test_by_xhh)
        sink_component.save()
        task_type = TaskType.new(code=123, name='test_source', engines='DataX,Ftplink', create_user=test_by_xhh,
                                 update_user=test_by_xhh)
        task_type.source_id = source_component.id
        task_type.sink_id = sink_component.id
        task_type.save()
        assert task_type.source_component.operator == source_component.operator
        assert task_type.sink_component.datasource == sink_component.datasource

    def test_get_with(self):
        task_type1 = TaskType.new(code=150, name='test_source', engines='DataX,Ftplink', create_user=test_by_xhh,
                                  update_user=test_by_xhh, source_id=150, sink_id=150)
        task_type1.save()
        task_type2 = TaskType.new(code=250, name='test_source', engines='DataX,Ftplink', create_user=test_by_xhh,
                                  update_user=test_by_xhh, source_id=250, sink_id=250)
        task_type2.save()
        task_types = TaskType.get_with(TaskType.code >= 100, TaskType.code < 200)
        assert len(task_types) == 2
        task_types = TaskType.get_with(TaskType.code > 200)
        assert len(task_types) == len(tests.task_types) + 1
        task_types = TaskType.get_with(TaskType.code == 250)
        assert len(task_types) == 1
        assert task_types[0].source_id == 250

    def test_delete(self):
        org = Org.get_one(name='org1', see_delete=1)
        org.delete()
        org = Org.get_one(name='org1', see_delete=1)
        assert not org
