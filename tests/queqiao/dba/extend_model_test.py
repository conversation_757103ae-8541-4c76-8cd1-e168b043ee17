"""
Author: xiaohei
Date: 2022/7/12
Email: <EMAIL>
Host: xiaohei.info
"""
import json
import time

import pytest

import tests
from queqiao.conf import Config
from queqiao.conf.enums import TaskStatus
from queqiao.core.execute.task import Task
from queqiao.dba.extend_model import TaskType, TaskPermission, Alarm
from queqiao.dba.models import TaskConfig, ComponentConfig, Org
from tests import import_task_types, task_type_ftp2hive, task_ftp2hive, ftp_source_config, hive_sink_config, \
    meituan_org, tonglian_org, project, apply, tasks


class TestAlarm:
    def test_success(self):
        assert not Alarm.exists(execution_id=tests.execution_talos2ftp.id)
        # 非success状态不告警
        Alarm.success(tests.execution_talos2ftp.id)
        assert len(Alarm.get(execution_id=tests.execution_talos2ftp.id)) == 0

    def test_failed(self):
        Alarm.failed(tests.execution_talos2ftp.id)
        assert len(Alarm.get(execution_id=tests.execution_talos2ftp.id)) == 1

    def test_alarm(self):
        Alarm.alarm(alarm_content='alarm from test', execution_id=tests.execution_talos2ftp.id)
        assert len(Alarm.get(execution_id=tests.execution_talos2ftp.id)) == 2

    def test_info(self):
        Alarm._info(tests.execution_talos2ftp.id, 'test from test_info', 'jiangyuande')
        assert len(Alarm.get(execution_id=tests.execution_talos2ftp.id)) == 3

    def test_warn(self):
        Alarm._warn(tests.execution_talos2ftp.id, 'test from test_warn', 'jiangyuande')
        assert len(Alarm.get(execution_id=tests.execution_talos2ftp.id)) == 4

    def test_keep_quiet(self):
        last_alarm_record = Alarm.get(execution_id=tests.execution_talos2ftp.id)
        last_alarm_record = last_alarm_record[-1]
        assert 'test from test_warn' in last_alarm_record.content
        last_alarm_record.keep_quiet = 0.02
        last_alarm_record.save()
        time.sleep(1)
        Alarm._warn(tests.execution_talos2ftp.id, 'test from test_warn not received', 'jiangyuande')
        time.sleep(2)
        Alarm._warn(tests.execution_talos2ftp.id, 'test from test_warn received', 'jiangyuande')
        assert len(Alarm.get(execution_id=tests.execution_talos2ftp.id)) == 5


class TestTaskType:
    def test_get_import_types(self):
        import_types = TaskType.get_import_types()
        assert len(import_types) == len(import_task_types)
        assert import_types[0].code == task_type_ftp2hive.code


class TestTaskPermission:
    def test_readable_permissions(self):
        task_permission = TaskPermission.get_one(user_id='jiangyuande_7')
        assert task_permission.readable_permissions == '读,写,执行'
        task_permission = TaskPermission.get_one(user_id='jiangyuande_5')
        assert task_permission.readable_permissions == '读,执行'

    @pytest.mark.skip
    def test_send_approve(self):
        pass

    @pytest.mark.skip
    def test_send_approve_result(self):
        pass

    @pytest.mark.parametrize('idx', [1, 2, 3, 4, 5, 6, 7])
    def test_permission_transfer(self, idx):
        task_permission = TaskPermission.get_one(user_id=f'jiangyuande_{idx}')
        assert task_permission.permission == idx
        decode_ps = TaskPermission.decode_permission(task_permission.permission)
        print(f'decode: {decode_ps}')
        encode_ps = TaskPermission.encode_permission(**decode_ps)
        print(f'encode: {encode_ps}')
        assert encode_ps == idx

    def test_to_dict(self):
        task_permission = TaskPermission.get_one(user_id='jiangyuande_4')
        result = task_permission.to_dict()
        assert result['read'] == 1
        assert result['write'] == 0


class TestTask:
    def test_permission_users(self):
        permission_users = task_ftp2hive.permission_users
        assert len(permission_users) == 3
        readable = set([int(u.split('_')[-1]) for u in permission_users['read']])
        writable = set([int(u.split('_')[-1]) for u in permission_users['write']])
        executable = set([int(u.split('_')[-1]) for u in permission_users['execute']])
        assert readable == {4, 5, 6, 7}
        assert writable == {2, 3, 6, 7}
        assert executable == {1, 3, 5, 7}

    def test_readable_users(self):
        readable = set([int(u.split('_')[-1]) for u in task_ftp2hive.readable_users])
        assert readable == {4, 5, 6, 7}

    def test_writable_users(self):
        writable = set([int(u.split('_')[-1]) for u in task_ftp2hive.writable_users])
        assert writable == {2, 3, 6, 7}

    def test_executable_users(self):
        executable = set([int(u.split('_')[-1]) for u in task_ftp2hive.executable_users])
        assert executable == {1, 3, 5, 7}

    def test_task_configs(self):
        task_configs = task_ftp2hive.task_configs
        print(f'task configs: {task_configs}')
        assert len(task_configs) == 2
        # dsn
        assert len(task_configs['source']) == len(ftp_source_config) + 1
        assert len(task_configs['sink']) == len(hive_sink_config) + 1
        assert task_configs['source']['target_sep'] == ftp_source_config['target_sep']
        assert task_configs['sink']['table_name'] == hive_sink_config['table_name']

    def test_task_component_configs(self):
        task_component_configs = task_ftp2hive.task_component_configs
        print(f'task component configs: {task_component_configs}')
        assert len(task_component_configs) == 2
        assert len(task_component_configs['source']) == len(ftp_source_config) 
        assert len(task_component_configs['sink']) == len(hive_sink_config)
        assert task_component_configs['source'][0]['name'] == 'file_path'
        assert task_component_configs['sink'][1]['value'] == hive_sink_config['target_sep']

    def test_source_org(self):
        assert task_ftp2hive.source_org.name == meituan_org.name

    def test_sink_org(self):
        assert task_ftp2hive.sink_org == Org.get_one(name=tonglian_org.name)

    def test_source_org_id(self):
        assert task_ftp2hive.source_org_id == meituan_org.id

    def test_sink_org_id(self):
        assert task_ftp2hive.sink_org_id == tonglian_org.id

    def test_get_auto_import_tasks(self):
        assert len(Task.get_auto_import_tasks()) == 0
        task_config = TaskConfig.new(key='auto_import', value=1, task_id=task_ftp2hive.id,
                                     cid=task_ftp2hive.task_type.source_id,
                                     create_user='jiangyuande')
        task_config.save()
        component_config = ComponentConfig.new(name=f'auto_import', name_cn=f'自动入库', type='string',
                                               default='default', required=1,
                                               demo='demo', cid=task_ftp2hive.task_type.source_id)
        component_config.save()
        assert len(Task.get_auto_import_tasks()) == 1

    @pytest.mark.parametrize('configs', [
        ({"is_admin": True, "uid": "xiaohei"}, len(tests.tasks)),
        ({"is_admin": False, "uid": "jiangyuande"}, len(tests.tasks) - 1),
        ({"is_admin": False, "uid": "xhh1"}, 0),
        ({"is_admin": False, "uid": "jiangyuande_1"}, 0),
        ({"is_admin": False, "uid": "jiangyuande_4"}, len(tests.tasks) - 1),
        ({"is_admin": False, "uid": "jiangyuande_7"}, len(tests.tasks) - 1)
    ])
    def test_get_tasks_by_user(self, configs):
        params, cnt = configs
        user = Config(params)
        user.project_id = project.id
        tasks = Task.get_tasks_by_user(user)
        assert len(tasks) == cnt

    @pytest.mark.parametrize('params', [
        {"is_admin": True, "uid": "xiaohei"},
        {"is_admin": False, "uid": "jiangyuande"},
        {"is_admin": False, "uid": "xhh"},
        {"is_admin": False, "uid": "jiangyuande_1"},
        {"is_admin": False, "uid": "jiangyuande_4"},
        {"is_admin": False, "uid": "jiangyuande_7"}
    ])
    def test_check_permission(self, params):
        user = Config(params)
        user.project_id = project.id
        task = Task.get_one(create_user='jiangyuande')
        if user.is_admin:
            assert task.check_permission(user, 'read')
            assert task.check_permission(user, 'write')
            assert task.check_permission(user, 'execute')
        if user.uid == 'jiangyuande':
            assert task.check_permission(user, 'read')
            assert task.check_permission(user, 'write')
            assert task.check_permission(user, 'execute')
        if user.uid == 'xhh':
            assert not task.check_permission(user, 'read')
            assert not task.check_permission(user, 'write')
            assert not task.check_permission(user, 'execute')
        if user.uid == 'jiangyuande_1':
            assert not task.check_permission(user, 'read')
            assert not task.check_permission(user, 'write')
            assert task.check_permission(user, 'execute')
        if user.uid == 'jiangyuande_4':
            assert task.check_permission(user, 'read')
            assert not task.check_permission(user, 'write')
            assert not task.check_permission(user, 'execute')
        if user.uid == 'jiangyuande_7':
            assert task.check_permission(user, 'read')
            assert task.check_permission(user, 'write')
            assert task.check_permission(user, 'execute')

    def test_runable(self):
        assert task_ftp2hive.runable
        task_ftp2hive.status = TaskStatus.OFFLINE.value
        task_ftp2hive.save()
        assert not task_ftp2hive.runable

    @pytest.mark.parametrize('params', [
        {'component_configs': False, 'task_permissions': False},
        {'component_configs': True, 'task_permissions': True}
    ])
    def test_to_dict(self, params):
        result = task_ftp2hive.to_dict(**params)
        print(json.dumps(result))
        assert result[
                   'where2where'] == f'{task_type_ftp2hive.source_component.datasource}(org_meituan) ' \
                   f'-> {task_type_ftp2hive.sink_component.datasource}(org_tonglian)'
        assert result['engine'] == 'Ftplink'
        assert result['task_type'] == task_ftp2hive.task_type.name
        if params['task_permissions']:
            assert len(result['permissions']) == 7
        else:
            assert 'permissions' not in result


class TestApply:
    @pytest.mark.parametrize('with_tasks', [True, False])
    def test_to_dict(self, with_tasks):
        result = apply.to_dict(with_tasks=with_tasks)
        print(json.dumps(result))
        assert result['security_level'] == 'C3'
        assert result['project'] == project.name
        if with_tasks:
            assert len(result['tasks']) == len(tasks)
        else:
            assert 'tasks' not in result
