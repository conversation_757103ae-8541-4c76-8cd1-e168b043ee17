"""
Author: xiaohei
Date: 2022/8/3
Email: <EMAIL>
Host: xiaohei.info
"""
import json

import pytest

from queqiao.conf.ApiResponse import SUCCESS, RESOURCE_NOT_FOUND, RESOURCE_EXISTS, NO_PRIVILEGE
from queqiao.conf.enums import PermissionStatus, LinuxPermission
from queqiao.dba import db
from queqiao.dba.extend_model import TaskPermission
from queqiao.dba.models import Project
from tests import test_client, task_talos2ftp, task_file2ftp_not_admin

task_permission_id = 0
project_id = 0


@pytest.mark.parametrize('params', [(0, 0), (task_talos2ftp.id, 7)])
def test_get_permissions(params):
    task_id, permission_cnt = params
    resp = test_client.get(f'/api/v1/task/permission/', data={'task_id': task_id})
    assert resp.status_code == 200
    print(json.dumps(resp.json))
    assert resp.json['code'] == SUCCESS.code
    assert len(resp.json['data']['permissions']) == permission_cnt


@pytest.mark.parametrize('params', [
    (9999, RESOURCE_NOT_FOUND.code, PermissionStatus.APPROVING.value, {'read': True, 'write': False, 'execute': True}),
    (task_talos2ftp.id, SUCCESS.code, PermissionStatus.ENTABLE.value, {'read': True, 'write': False, 'execute': True}),
    (task_talos2ftp.id, RESOURCE_EXISTS.code, PermissionStatus.APPROVING.value,
     {'read': True, 'write': False, 'execute': True}),
    (task_file2ftp_not_admin.id, SUCCESS.code, PermissionStatus.APPROVING.value,
     {'read': True, 'write': False, 'execute': True})
])
def test_add_permission(params):
    task_id, code, status, permission_params = params
    resp = test_client.post(f'/api/v1/task/permission/', json={'task_id': task_id, 'permission': permission_params})
    assert resp.status_code == 200
    print(json.dumps(resp.json))
    assert resp.json['code'] == code
    if code == SUCCESS.code:
        pid = resp.json['data']['id']
        permission = TaskPermission.get(id=pid)
        assert permission.task_id == task_id
        assert permission.status == status
        assert permission.permission == TaskPermission.encode_permission(**permission_params)
        global task_permission_id
        task_permission_id = pid


def test_get_permission():
    resp = test_client.get(f'/api/v1/task/permission/{task_permission_id}')
    assert resp.status_code == 200
    print(json.dumps(resp.json))
    assert resp.json['code'] == SUCCESS.code
    assert resp.json['data']['status'] == PermissionStatus.APPROVING.value


def test_approve():
    resp = test_client.post(f'/api/v1/task/permission/approve/{task_permission_id}',
                            data={'action': PermissionStatus.REJECT.value})
    assert resp.status_code == 200
    print(json.dumps(resp.json))
    assert resp.json['code'] == NO_PRIVILEGE.code
    task_permission = TaskPermission.get(id=task_permission_id)
    db.session.refresh(task_permission)
    assert task_permission.status == PermissionStatus.APPROVING.value
    project = task_permission.task.project
    project.admins = 'chentianzeng,jiangyuande'
    project.save()

    resp = test_client.post(f'/api/v1/task/permission/approve/{task_permission_id}',
                            data={'action': PermissionStatus.REJECT.value})
    assert resp.status_code == 200
    print(json.dumps(resp.json))
    assert resp.json['code'] == SUCCESS.code
    task_permission = TaskPermission.get(id=task_permission_id)
    db.session.refresh(task_permission)
    assert task_permission.status == PermissionStatus.REJECT.value

    project.admins = 'chentianzeng'
    project.save()
    global project_id
    project_id = project.id


def test_mod_permission():
    permission = {'read': False, 'write': True, 'execute': True}
    resp = test_client.post(f'/api/v1/task/permission/{task_permission_id}',
                            json={'permission': permission})
    assert resp.status_code == 200
    print(json.dumps(resp.json))
    assert resp.json['code'] == SUCCESS.code
    task_permission = TaskPermission.get(id=task_permission_id)
    db.session.refresh(task_permission)
    assert task_permission.status == PermissionStatus.APPROVING.value
    assert LinuxPermission(task_permission.permission) == LinuxPermission.WRITE_EXECUTE


def test_del_permission():
    resp = test_client.delete(f'/api/v1/task/permission/{task_permission_id}')
    assert resp.status_code == 200
    print(json.dumps(resp.json))
    assert resp.json['code'] == NO_PRIVILEGE.code

    project = Project.get(id=project_id)
    project.admins = 'chentianzeng,jiangyuande'
    project.save()

    resp = test_client.delete(f'/api/v1/task/permission/{task_permission_id}')
    assert resp.status_code == 200
    print(json.dumps(resp.json))
    assert resp.json['code'] == SUCCESS.code
    assert not TaskPermission.exists(id=task_permission_id)
    project.admins = 'chentianzeng'
    project.save()
