"""
Author: xiaohei
Date: 2023/1/14
Email: <EMAIL>
Host: xiaohei.info
"""
import json

import pytest

from queqiao.conf.ApiResponse import SUCCESS, RESOURCE_NOT_FOUND
from queqiao.dba import db
from queqiao.dba.models import Org
from tests import test_client

org_id = 1


def test_get_all_orgs():
    resp = test_client.get(f'/api/v1/org/')
    assert resp.status_code == 200
    print(json.dumps(resp.json))
    orgs = Org.get()
    assert len(resp.json['data']) == len(orgs)


def test_add_org():
    data = {
        'name': 'add_by_org_test',
        'params': json.dumps({'key': 'value'}),
        'comment': 'use for test'
    }
    resp = test_client.post(f'/api/v1/org/', json=data)
    assert resp.status_code == 200
    print(resp.json)
    assert resp.json['code'] == SUCCESS.code
    id = resp.json['data']['id']
    org = Org.get(id=id)
    assert org.comment == data['comment']
    assert org.name == data['name']
    global org_id
    org_id = id


def test_get_org():
    resp = test_client.get(f'/api/v1/org/{org_id}')
    assert resp.status_code == 200
    print(json.dumps(resp.json))
    org = Org.get(id=org_id)
    assert resp.json['data']['params'] == org.params
    assert resp.json['data']['comment'] == org.comment


@pytest.mark.parametrize('data', [
    {'name': 'add_by_org_test', 'code': SUCCESS.code},
    {'name': '11111', 'code': RESOURCE_NOT_FOUND.code}
])
def test_get_dsn_by_name(data):
    resp = test_client.get(f'/api/v1/org/{data["name"]}')
    assert resp.status_code == 200
    assert resp.json['code'] == data['code']
    print(json.dumps(resp.json))
    if data['code'] == SUCCESS.code:
        org = Org.get(id=org_id)
        assert resp.json['data']['params'] == org.params
        assert resp.json['data']['comment'] == org.comment


def test_update_org():
    data = {
        'name': 'add_by_org_test11',
        'params': json.dumps({'key1': 'value1'}),
    }
    resp = test_client.post(f'/api/v1/org/{org_id}', json=data)
    assert resp.status_code == 200
    print(resp.json)
    assert resp.json['code'] == SUCCESS.code
    id = resp.json['data']['id']
    org = Org.get(id=id)
    db.session.refresh(org)
    assert org.params == data['params']
    assert org.name == data['name']
