"""
Author: xiaohei
Date: 2022/8/3
Email: <EMAIL>
Host: xiaohei.info
"""
import json
import time

import pytest
from celery.result import AsyncResult

from queqiao.conf.ApiResponse import SUCCESS
from queqiao.conf.enums import ExecutionStatus
from queqiao.dba import db
from queqiao.dba.models import Execution
from tests import test_client, execution_talos2ftp

status_map = {
    'INIT': 101,
    'QUEUED': 102,
    'RUNNING_PRE': 201,
    'READING': 202,
    'WRITING': 203,
    'RUNNING_POST': 204,
    'RETRY': 205,
    'SUCCESS': 301,
    'FAILED': 401,
    'KILLED': 402,
}


def test_get_execution():
    resp = test_client.get(f'/api/v1/execution/{execution_talos2ftp.id}')
    assert resp.status_code == 200
    assert resp.json['code'] == SUCCESS.code
    assert resp.json['data']['qid'] == execution_talos2ftp.qid
    assert resp.json['data']['task_id'] == execution_talos2ftp.task_id
    assert resp.json['data']['source_org_id'] == execution_talos2ftp.source_org_id


def test_get_execution_status():
    resp = test_client.get(f'/api/v1/execution/status/{execution_talos2ftp.id}')
    assert resp.status_code == 200
    print(json.dumps(resp.json))
    assert resp.json['code'] == SUCCESS.code
    assert resp.json['data']['status'] == ExecutionStatus.WRITING.name


@pytest.mark.parametrize('async_', [0, 1])
def test_exec_execution(async_):
    resp = test_client.get(f'/api/v1/execution/exec/{execution_talos2ftp.id}?async={async_}')
    assert resp.status_code == 200
    print(json.dumps(resp.json))
    assert resp.json['code'] == SUCCESS.code
    if async_ == 1:
        while True:
            execution = Execution.get(id=execution_talos2ftp.id)
            db.session.refresh(execution)
            if 300 < execution.status < 400:
                print('execute success')
                break
            elif execution.status > 400:
                print('execute failed')
                break
            else:
                print('sleep 3')
                time.sleep(3)
    execution = Execution.get(id=execution_talos2ftp.id)
    db.session.refresh(execution)
    assert ExecutionStatus(execution.status) == ExecutionStatus.SUCCESS


def test_get_execution_log():
    resp = test_client.get(f'/api/v1/execution/log/{execution_talos2ftp.id}')
    assert resp.status_code == 200


@pytest.mark.skip(reason='暂时不测kill，有缓存问题随机无法拿到正确qid')
def test_kill_execution():
    resp = test_client.get(f'/api/v1/execution/exec/{execution_talos2ftp.id}?async=1')
    assert resp.status_code == 200
    print(json.dumps(resp.json))
    assert resp.json['code'] == SUCCESS.code

    time.sleep(1)
    db.session.refresh(execution_talos2ftp)
    resp = test_client.get(f'/api/v1/execution/kill/{execution_talos2ftp.id}')
    assert resp.status_code == 200
    print(json.dumps(resp.json))
    assert resp.json['code'] == SUCCESS.code

    time.sleep(2)
    execution = Execution.get(id=execution_talos2ftp.id)
    db.session.refresh(execution)
    time.sleep(2)
    db.session.refresh(execution)
    print('[test_kill_execution] execution status: %s' % execution.status)
    assert execution.status == ExecutionStatus.KILLED.value

    task = AsyncResult(execution.qid)
    print(f'celery task {execution.qid} status: {task.status}')
    assert task.state == 'REVOKED'
