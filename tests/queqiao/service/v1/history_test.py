"""FTP历史文件记录接口测试用例

Author: xiaohei
Date: 2025/01/20
Email: <EMAIL>
Host: xiaohei.info
"""
import pytest
from unittest.mock import MagicMock

from queqiao.dba.models import FtplinkHistoryFile
from queqiao.conf.ApiResponse import (
    SUCCESS,
    RESOURCE_NOT_FOUND,
    ILLEGAL_PARAMS,
    INTERVAL_ERROR
)
from tests import test_client

@pytest.fixture(scope='module')
def mock_user():
    """模拟已登录用户"""
    user = MagicMock()
    user.uid = "test_user"
    return user

@pytest.fixture(scope='module')
def history_file():
    """创建测试用历史文件记录"""
    file = FtplinkHistoryFile(
        create_user="test_user",
        source="ftp",
        sink="local",
        remote_file="/test/test.txt",
        local_file="/tmp/test.txt",
        bytes=1024,
        md5="d41d8cd98f00b204e9800998ecf8427e",
        row_num=10,
        col_num=5,
        col_list="col1,col2,col3,col4,col5",
        execution_id=100,
        task_id=1,
        source_org_id=1,
        sink_org_id=2,
        file_type="csv",
        project_id=1
    )
    file.save()
    yield file
    file.delete()

def test_get_history_file_success(mock_user, history_file):
    """测试成功获取单个历史文件记录"""
    resp = test_client.get(f'/api/v1/history/file/{history_file.id}')
    assert resp.status_code == 200
    assert resp.json['code'] == SUCCESS.code
    assert resp.json['data'] == history_file.to_dict()

def test_get_history_file_not_found(mock_user, monkeypatch):
    """测试获取不存在的历史文件记录"""
    monkeypatch.setattr('queqiao.dba.models.FtplinkHistoryFile.get', lambda id=None: None)
    
    resp = test_client.get('/api/v1/history/file/999')
    assert resp.status_code == 200
    assert resp.json['code'] == RESOURCE_NOT_FOUND.code
    assert '文件记录 999 不存在' in resp.json['message']

def test_list_history_files_success(mock_user, history_file):
    """测试成功获取历史文件记录列表"""
    resp = test_client.get('/api/v1/history/file?page=1&per_page=10')
    assert resp.status_code == 200
    assert resp.json['code'] == SUCCESS.code
    assert resp.json['data'] == {
        'items': [history_file.to_dict()],
        'total': 1,
        'page': 1,
        'per_page': 10
    }

def test_get_history_files_by_execution_success(mock_user, history_file):
    """测试成功通过execution_id获取历史文件记录列表"""
    resp = test_client.get(f'/api/v1/history/file/execution/{history_file.execution_id}')
    assert resp.status_code == 200
    assert resp.json['code'] == SUCCESS.code
    assert resp.json['data'] == [history_file.to_dict()]

def test_get_history_files_by_execution_not_found(mock_user, monkeypatch):
    """测试通过不存在的execution_id获取历史文件记录列表"""
    mock_query = MagicMock()
    mock_query.all.return_value = []
    monkeypatch.setattr('queqiao.dba.models.FtplinkHistoryFile.query.filter_by', lambda **kwargs: mock_query)
    
    resp = test_client.get('/api/v1/history/file/execution/999')
    assert resp.status_code == 200
    assert resp.json['code'] == RESOURCE_NOT_FOUND.code
    assert '未找到execution_id为 999 的文件记录' in resp.json['message']