"""
Author: xiaohei
Date: 2022/8/2
Email: <EMAIL>
Host: xiaohei.info
"""
import json

from queqiao.conf.ApiResponse import SUCCESS
from queqiao.dba import db
from queqiao.dba.extend_model import TaskType
from tests import test_client, task_types, task_type_talos2ftp

task_type_id = 0


def test_get_all_task_types():
    resp = test_client.get(f'/api/v1/task/type/')
    assert resp.status_code == 200
    print(json.dumps(resp.json))
    assert len(resp.json['data']) == len(task_types)


def test_get_task_type():
    resp = test_client.get(f'/api/v1/task/type/{task_type_talos2ftp.id}')
    assert resp.status_code == 200
    print(json.dumps(resp.json))
    assert resp.json['data']['code'] == task_type_talos2ftp.code
    assert resp.json['data']['name'] == task_type_talos2ftp.name


def test_add_task_type():
    data = {
        'code': 99999,
        'name': 'add_by_task_type_test',
        'source_id': -1,
        'sink_id': -1,
        'engines': 'Ftplink,DataX',
        'comment': 'use for test'
    }
    resp = test_client.post(f'/api/v1/task/type/', json=data)
    assert resp.status_code == 200
    print(resp.json)
    assert resp.json['code'] == SUCCESS.code
    id = resp.json['data']['id']
    task_type = TaskType.get(id=id)
    assert task_type.code == data['code']
    assert task_type.name == data['name']
    global task_type_id
    task_type_id = id


def test_update_task_type():
    data = {
        'code': 000000,
        'name': 'add_by_task_type_test1',
        'source_id': -1,
        'sink_id': -1,
        'engines': 'Ftplink,DataX',
        'comment': 'use for test'
    }
    resp = test_client.post(f'/api/v1/task/type/{task_type_id}', json=data)
    assert resp.status_code == 200
    print(resp.json)
    assert resp.json['code'] == SUCCESS.code
    id = resp.json['data']['id']
    task_type = TaskType.get(id=id)
    db.session.refresh(task_type)
    assert task_type.code == data['code']
    assert task_type.name == data['name']
