"""
Author: xiaohei
Date: 2022/8/5
Email: <EMAIL>
Host: xiaohei.info
"""
import pytest
from celery.result import AsyncResult

from queqiao.conf.ApiResponse import SUCCESS, EXECUTE_FAILED
from tests import test_client_not_cookie, project


def test_health_check():
    resp = test_client_not_cookie.get(f'/api/v1/health/check')
    assert resp.status_code == 200
    print(resp.json)
    assert resp.json['code'] == SUCCESS.code


def test_alart_msg():
    resp = test_client_not_cookie.get(
        f'/api/v1/health/alert?msg=test_alart_msg&receivers=jiangyuande')
    assert resp.status_code == 200
    print(resp.json)
    assert resp.json['code'] == SUCCESS.code

    resp = test_client_not_cookie.post(
        f'/api/v1/health/alert', json={'msg': 'hhhh', 'receivers': 'jiangyuande'})
    assert resp.status_code == 200
    print(resp.json)
    assert resp.json['code'] == SUCCESS.code


def test_talos_table_schema():
    name = 'mart_fspinno_queqiao.pytest'
    resp = test_client_not_cookie.get(
        f'/api/v1/health/talos/table/schema?name={name}')
    assert resp.status_code == 200
    print(resp.json)
    assert resp.json['code'] == SUCCESS.code
#
# async_task_id = ''
#
#
# @pytest.mark.parametrize('params', [
#     (project.id, 'test_by_celery_project_wait', 3, SUCCESS.code, 1),
#     (999, 'not_found', 10, EXECUTE_FAILED.code, 1),
#     (project.id, 'test_by_celery_project_nowait', 3, SUCCESS.code, 0),
# ])
# def test_async_submit(params):
#     project_id, project_name, sleep_time, code, wait = params
#     resp = test_client_not_cookie.get(
#         f'/api/v1/health/async/submit?project_id={project_id}&project_name={project_name}&sleep_time={sleep_time}&wait={wait}')
#     assert resp.status_code == 200
#     print(resp.json)
#     assert resp.json['code'] == code
#     if code == SUCCESS.code:
#         if wait == 1:
#             result = resp.json['data']['result']
#             assert result['name'] == project_name
#         else:
#             global async_task_id
#             async_task_id = resp.json['data']['id']
#
#
# def test_async_kill():
#     resp = test_client_not_cookie.get(
#         f'/api/v1/health/async/kill?task_id={async_task_id}')
#     assert resp.status_code == 200
#     print(resp.json)
#     assert resp.json['code'] == SUCCESS.code
#     task_result = AsyncResult(async_task_id)
#     assert not task_result.successful()
#     assert not task_result.ready()
#     assert task_result.state == 'REVOKED'
