"""
Author: xiaohei
Date: 2022/8/2
Email: <EMAIL>
Host: xiaohei.info
"""
import json

import pytest

from queqiao.conf.ApiResponse import SERVER_NOT_FOUND, SUCCESS, MISSING_PARAMS, RESOURCE_EXISTS
from queqiao.dba.models import Component
from tests import test_client, task_type_talos2ftp, task_type_hive2ftp

component_id = 0


@pytest.mark.parametrize('params', [
    (None, SUCCESS.code),
    ({'operator': 'source', 'datasource': 'hhh'}, SERVER_NOT_FOUND.code),
    ({'operator': 'source', 'datasource': 'talos'}, SUCCESS.code),
])
def test_get_components(params):
    data, code = params
    resp = test_client.get(f'/api/v1/component/', data=data)
    assert resp.status_code == 200
    print(json.dumps(resp.json))
    assert resp.json['code'] == code


@pytest.mark.parametrize('params', [
    (None, MISSING_PARAMS.code),
    ({'operator': 'sink', 'datasource': 'test_from_component_test'}, SUCCESS.code),
    ({'operator': 'source', 'datasource': 'talos'}, RESOURCE_EXISTS.code),
])
def test_add_component(params):
    data, code = params
    resp = test_client.post(f'/api/v1/component/', data=data)
    assert resp.status_code == 200
    print(json.dumps(resp.json))
    assert resp.json['code'] == code
    if code == SUCCESS.code:
        global component_id
        component_id = resp.json['data']['id']
        print(f'get component_id: {component_id}')


def test_update_component():
    data, code = {'datasource': 'testhhh'}, SUCCESS.code
    resp = test_client.post(f'/api/v1/component/{component_id}', data=data)
    assert resp.status_code == 200
    print(json.dumps(resp.json))
    assert resp.json['code'] == code
    component = Component.get(id=component_id)
    assert component.datasource == data['datasource']


def test_match_source_components():
    resp = test_client.get(f'/api/v1/component/match/sources', data={'sink_id': task_type_talos2ftp.sink_id})
    assert resp.status_code == 200
    print(json.dumps(resp.json))
    assert resp.json['code'] == SUCCESS.code
    assert len(resp.json['data']) == 3
    hive_component = Component.get(id=task_type_hive2ftp.source_id)
    assert str(hive_component.id) in ','.join([str(i['cid']) for i in resp.json['data']])


def test_match_sink_components():
    resp = test_client.get(f'/api/v1/component/match/sinks', data={'source_id': task_type_talos2ftp.source_id})
    assert resp.status_code == 200
    print(json.dumps(resp.json))
    assert resp.json['code'] == SUCCESS.code
    assert len(resp.json['data']) == 1
    ftp_component = Component.get(id=task_type_talos2ftp.sink_id)
    assert ftp_component.id == resp.json['data'][0]['cid']
    assert ftp_component.operator == resp.json['data'][0]['operator']
    assert ftp_component.datasource == resp.json['data'][0]['datasource']
