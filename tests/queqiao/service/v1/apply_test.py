"""
Author: xiaohei
Date: 2022/8/1
Email: <EMAIL>
Host: xiaohei.info
"""
import json

import pytest

from queqiao.conf.ApiResponse import MISSING_PARAMS, SUCCESS, RESOURCE_NOT_AVAILABLE
from queqiao.conf.enums import TransType, ApplyStatus
from queqiao.dba import db
from queqiao.dba.models import Apply
from tests import test_client, project, engine_ftplink, task_type_talos2ftp, \
    talos_source_config, ftp_sink_config, talos_dsn, ftp_dsn

source = talos_source_config.copy()
source['cid'] = task_type_talos2ftp.source_id
source['dsn'] = talos_dsn.id
sink = ftp_sink_config.copy()
sink['cid'] = task_type_talos2ftp.sink_id
sink['dsn'] = ftp_dsn.id

apply_id = 0


@pytest.mark.parametrize('params', [
    ({}, MISSING_PARAMS.code),
    ({
         'scenario': 'test_by_apply_test',
         'describe': 'test data',
         'security_level': 3,
         'tasks': [
             {
                 'name': f'{project.name}.test_create_apply_task1',
                 'trans_type': TransType.SINGLE.value,
                 'alarm_receivers': 'jiangyuande',
                 'engine_id': engine_ftplink.id,
                 'source': source,
                 'sink': sink,
                 'exec_immediately': False
             }
         ],
     }, SUCCESS.code)
])
def test_create_apply(params):
    data, code = params
    resp = test_client.post(f'/api/v1/apply/', json=data)
    assert resp.status_code == 200
    print(resp.json)
    assert resp.json['code'] == code
    if code == SUCCESS.code:
        id = resp.json['data']['id']
        apply = Apply.get(id=id)
        assert apply
        assert ApplyStatus(apply.status) == ApplyStatus.APPROVING
        global apply_id
        apply_id = id
        print(f'get apply_id: {apply_id}')


def test_apply_progress():
    resp = test_client.get(f'/api/v1/apply/progress/{apply_id}')
    print(resp.json['data'])
    assert resp.json['code'] == SUCCESS.code


def test_push_apply():
    resp = test_client.get(f'/api/v1/apply/push/{apply_id}')
    assert resp.json['code'] == SUCCESS.code


def test_get_apply():
    resp = test_client.get(f'/api/v1/apply/{apply_id}')
    print(json.dumps(resp.json['data']))
    assert resp.json['code'] == SUCCESS.code


def test_agree_apply():
    resp = test_client.get(f'/api/v1/apply/agree/{apply_id}')
    assert resp.json['code'] == SUCCESS.code
    apply = Apply.get(id=apply_id)
    assert ApplyStatus(apply.status) == ApplyStatus.AGREE


def test_reject_apply1():
    resp = test_client.get(f'/api/v1/apply/reject/{apply_id}')
    assert resp.json['code'] == RESOURCE_NOT_AVAILABLE.code
    apply = Apply.get(id=apply_id)
    apply.status = ApplyStatus.APPROVING.value
    apply.save()


def test_reject_apply2():
    apply = Apply.get(id=apply_id)
    resp = test_client.get(f'/api/v1/apply/reject/{apply_id}')
    assert resp.json['code'] == SUCCESS.code
    db.session.refresh(apply)
    assert ApplyStatus(apply.status) == ApplyStatus.REJECT
