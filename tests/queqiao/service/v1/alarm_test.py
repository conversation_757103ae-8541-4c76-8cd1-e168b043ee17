"""
Author: xiaohei
Date: 2022/8/1
Email: <EMAIL>
Host: xiaohei.info
"""

from queqiao.conf.ApiResponse import RESOURCE_NOT_FOUND, SUCCESS
from queqiao.dba.extend_model import <PERSON>arm
from tests import test_client


def test_keep_quiet():
    keep_quiet_n_mins = 10

    resp = test_client.post(f'/api/v1/alarm/keep_quiet/0', data={'keep_quiet_n_mins': keep_quiet_n_mins})
    assert resp.status_code == 200
    print(resp.json)
    assert resp.json['code'] == RESOURCE_NOT_FOUND.code

    alarm = Alarm.new(receivers='jiangyuande', execution_id=1)
    alarm.save()
    alarm_id = alarm.id
    resp = test_client.post(f'/api/v1/alarm/keep_quiet/{alarm_id}', data={'keep_quiet_n_mins': keep_quiet_n_mins})
    assert resp.status_code == 200
    print(resp.json)
    assert resp.json['code'] == SUCCESS.code
    alarm = Alarm.get(id=alarm_id)
    assert alarm.keep_quiet == keep_quiet_n_mins
