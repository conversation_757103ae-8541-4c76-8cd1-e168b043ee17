"""
Author: xiaohei
Date: 2022/8/3
Email: <EMAIL>
Host: xiaohei.info
"""
import json

import pytest

from queqiao.conf.ApiResponse import NO_PRIVILEGE, RESOURCE_NOT_FOUND, SUCCESS, INTERVAL_ERROR, ILLEGAL_PARAMS, \
    RESOURCE_EXISTS
from queqiao.conf.enums import TaskStatus, ExecutionStatus
from queqiao.dba import db
from queqiao.dba.extend_model import Task
from queqiao.dba.models import Execution, Project
from queqiao.util.comm.dtutil import timer
from tests import test_client, tasks, test_client_not_admin, app, project, project_not_admin, \
    engine_ftplink, task_talos2ftp, task_type_talos2ftp, task_ftp2hive, \
    task_type_ftp2hive, meituan_org, tonglian_org

execution_id = 0


@pytest.mark.skip
def test_parse_doc():
    pass


@pytest.mark.skip
def test_download_doc():
    pass


@pytest.mark.skip
def test_download_doc_by_id():
    pass


@pytest.mark.parametrize('params', [
    # new task
    (test_client, len(tasks) + 1),
    (test_client_not_admin, 0),
    (None, 1)
])
def test_get_tasks(params):
    client, cnt = params
    if not client:
        with app.test_client() as test_client_tmp:
            test_client_tmp.set_cookie('localhost', 'uid', 'chentianzeng')
            test_client_tmp.set_cookie('localhost', 'aid', '0')
            test_client_tmp.set_cookie('localhost', 'gid', '274200007')
            test_client_tmp.set_cookie('localhost', 'token', '63c3446ab52cdca7da18d059f8bd0e62')
            test_client_tmp.set_cookie('localhost', 'groups', json.dumps(['ipausers', 'queqiao_user_bs']))
            test_client_tmp.set_cookie('localhost', 'projects',
                                       json.dumps([
                                           {'id': project.id, 'name': project.name, 'selected': 0},
                                           {'id': project_not_admin.id, 'name': project_not_admin.name, 'selected': 1}
                                       ]))
            client = test_client_tmp
    resp = client.get(f'/api/v1/task/')
    assert resp.status_code == 200
    print(json.dumps(resp.json))
    assert len(resp.json['data']) == cnt


@pytest.mark.parametrize('params', [
    ({'create_user': 'jiangyuande', 'project_id': project_not_admin.id}, 0),
    ({'create_user': 'jiangyuande', 'engine_id': engine_ftplink.id}, 5),
    ({'name': task_talos2ftp.name}, 1),
    # new task
    ({'task_type_id': task_type_talos2ftp.id}, 1 + 1),
    ({'create_time': timer.now().date}, len(tasks) + 1),
    ({'source_org_id': meituan_org.id, 'sink_org_id': tonglian_org.id}, len(tasks) + 1),
    ({'source_org_id': 9999, 'sink_org_id': tonglian_org.id}, 0),
])
def test_search_tasks(params):
    data, cnt = params
    resp = test_client.post(f'/api/v1/task/search', json=data)
    assert resp.status_code == 200
    print(json.dumps(resp.json))
    assert len(resp.json['data']) == cnt


def test_get_task():
    resp = test_client.get(f'/api/v1/task/{task_talos2ftp.id}')
    assert resp.status_code == 200
    print(json.dumps(resp.json))
    assert resp.json['data']['name'] == task_talos2ftp.name
    assert resp.json['data']['task_type_id'] == task_talos2ftp.task_type_id


def test_get_task_by_name():
    resp = test_client.get(f'/api/v1/task/{task_talos2ftp.name}')
    assert resp.status_code == 200
    print(json.dumps(resp.json))
    assert resp.json['data']['id'] == task_talos2ftp.id
    assert resp.json['data']['task_type_id'] == task_talos2ftp.task_type_id


@pytest.mark.parametrize('params', [
    (test_client, 999, RESOURCE_NOT_FOUND.code, {}),
    (test_client_not_admin, task_ftp2hive.id, NO_PRIVILEGE.code, {}),
    (test_client, task_ftp2hive.id, SUCCESS.code, {'name': 'uuftp2hive', 'alarm_receivers': 'xh,xiaohei'}),
    (test_client, task_ftp2hive.id, SUCCESS.code,
     {'source_configs':
          {"cid": task_type_ftp2hive.source_id, "ftp_polling_sec": 3600},
      'sink_configs':
          {"cid": task_type_ftp2hive.sink_id, "write_mode": "illegal"}
      }
     ),
])
def test_update_task(params):
    client, task_id, code, data = params
    resp = client.post(f'/api/v1/task/{task_id}', json=data)
    assert resp.status_code == 200
    print(json.dumps(resp.json))
    assert resp.json['code'] == code
    if code == SUCCESS.code:
        task = Task.get(id=task_ftp2hive.id)
        db.session.refresh(task)
        if 'source_configs' not in data:
            assert task.name == data['name']
            assert task.alarm_receivers == data['alarm_receivers']
        else:
            # assert task.task_configs['source']['ok_file'] == data['source_configs']['ok_file']
            assert int(task.task_configs['source']['ftp_polling_sec']) == data['source_configs']['ftp_polling_sec']
            # assert task.task_configs['sink']['partition_key'] == data['sink_configs']['partition_key']
            assert task.task_configs['sink']['write_mode'] == data['sink_configs']['write_mode']


@pytest.mark.parametrize('params', [
    (test_client, task_ftp2hive, SUCCESS.code),
    (test_client_not_admin, task_ftp2hive, NO_PRIVILEGE.code),
])
def test_task_offline(params):
    client, task, code = params
    resp = client.get(f'/api/v1/task/offline/{task.id}')
    assert resp.status_code == 200
    print(json.dumps(resp.json))
    assert resp.json['code'] == code
    if code == SUCCESS.code:
        db_task = Task.get(id=task.id)
        db.session.refresh(db_task)
        assert db_task.status == TaskStatus.OFFLINE.value


@pytest.mark.parametrize('params', [
    (test_client, task_ftp2hive, SUCCESS.code),
    (test_client_not_admin, task_ftp2hive, NO_PRIVILEGE.code),
])
def test_task_online(params):
    client, task, code = params
    resp = client.get(f'/api/v1/task/online/{task.id}')
    assert resp.status_code == 200
    print(json.dumps(resp.json))
    assert resp.json['code'] == code
    if code == SUCCESS.code:
        db_task = Task.get(id=task.id)
        db.session.refresh(db_task)
        assert db_task.status == TaskStatus.SUCCESS.value


@pytest.mark.parametrize('params',
                         [('farfar', INTERVAL_ERROR.code), ('Cantor', SUCCESS.code), ('Azkaban', SUCCESS.code)])
def test_get_task_cmd(params):
    etl_system, code = params
    resp = test_client.get(f'/api/v1/task/cmd/{task_talos2ftp.id}', data={'etl_system': etl_system})
    assert resp.status_code == 200
    print(json.dumps(resp.json))
    assert resp.json['code'] == code


def test_get_task_apply():
    resp = test_client.get(f'/api/v1/task/apply/{task_talos2ftp.id}')
    assert resp.status_code == 200
    print(json.dumps(resp.json))
    assert resp.json['code'] == SUCCESS.code


@pytest.mark.parametrize('params', [
    (task_talos2ftp.name, ILLEGAL_PARAMS.code),
    (f'hhh.{task_talos2ftp.name}', RESOURCE_NOT_FOUND.code),
    (f'{project.name}.{task_talos2ftp.name}', SUCCESS.code)
])
def test_execute_task(params):
    name, code = params
    resp = test_client.get(f'/api/v1/task/exec/{name}?etl_date=2022-01-01&params=async:0')
    assert resp.status_code == 200
    print(json.dumps(resp.json))
    assert resp.json['code'] == code
    if code == SUCCESS.code:
        global execution_id
        execution_id = resp.json['data']['execution_id']
        execution = Execution.get(id=execution_id)
        assert ExecutionStatus(execution.status) == ExecutionStatus.SUCCESS


def test_get_executions():
    resp = test_client.get(f'/api/v1/task/execution/{task_talos2ftp.id}')
    assert resp.status_code == 200
    print(json.dumps(resp.json))
    assert resp.json['code'] == SUCCESS.code


tmp_task_config1 = {
    'whoami': 'pytest',
    'engine': 'Ftplink',
    'name': 'from_pytest_create',
    'type': 'talos2ftp',
    'source_configs': {
        'dsn': 'dsn_talos',
        'sql': 'select a from b',
        'bucket_col': 'id_group'
    },
    'sink_configs': {
        'dsn': 'dsn_hive',
        'party_id': 10000,
        'partition': 16,
        'namespace': 'tianwen_data',
        'tablename': 'data_set_xxx',
        'work_mode': 1
    },
    'result': 0
}

tmp_task_config2 = {
    'whoami': 'pytest',
    'engine': 'Ftplink1',
    'name': 'from_pytest_create',
    'result': 201003
}

tmp_task_config3 = {
    'whoami': 'pytest',
    'engine': 'Ftplink111',
    'name': 'from_pytest_create',
    'type': 'talos2fate111',
    'source_configs': {
        'dsn': 'meituan-talos',
        'sql': 'select a from b',
        'bucket_col': 'id_group'
    },
    'sink_configs': {
        'dsn': 'tianwen',
        'party_id': 10000,
        'partition': 16,
        'namespace': 'tianwen_data',
        'tablename': 'data_set_xxx',
        'work_mode': 1
    },
    'result': RESOURCE_NOT_FOUND.code
}


@pytest.mark.parametrize('data', [tmp_task_config1, tmp_task_config2, tmp_task_config3])
def test_create_tmp_task(data):
    resp = test_client.post(f'/api/v1/task/tmp', json=data)
    assert resp.status_code == 200
    print(f'==={resp.json["message"]}===')
    assert resp.json['code'] == data['result']
    if data['result'] == 0:
        print(f'!!{resp.json["data"]}!!!')


@pytest.mark.parametrize('data', [
    {'project': None, 'result': 201003},
    {'project': 'quick_project', 'result': 0, 'delete_project': True},
    {'project': 'quick_project', 'result': RESOURCE_EXISTS.code}
])
def test_create_task(data):
    if 'delete_project' in data:
        Project.delete_by(name=data['project'])
        assert not Project.exists(name=data['project'])
    tmp_task_config1['project'] = data['project']
    resp = test_client.post(f'/api/v1/task/create', json=tmp_task_config1)
    assert resp.status_code == 200
    print(f'==={resp.json["message"]}===')
    assert resp.json['code'] == data['result']
    if data['result'] == 0:
        print(f'!!{resp.json["data"]}!!!')
        assert Project.exists(name=data['project'])
