"""
Author: xiaohei
Date: 2023/1/14
Email: <EMAIL>
Host: xiaohei.info
"""
import json

import pytest

from queqiao.conf.ApiResponse import SUCCESS, RESOURCE_NOT_FOUND, RESOURCE_NOT_AVAILABLE
from queqiao.dba import db
from queqiao.dba.models import Dsn
from tests import test_client

dsn_id = 1


def test_get_all_dsns():
    resp = test_client.get(f'/api/v1/dsn/')
    assert resp.status_code == 200
    print(json.dumps(resp.json))
    dsns = Dsn.get()
    assert len(resp.json['data']) == len(dsns)


def test_add_dsn():
    data = {
        'name': 'add_by_dsn_test',
        'connect': json.dumps({'key': 'value'}),
        'dsn_type': 'hive',
        'org_id': 0,
        'comment': 'use for test'
    }
    resp = test_client.post(f'/api/v1/dsn/', json=data)
    assert resp.status_code == 200
    print(resp.json)
    assert resp.json['code'] == SUCCESS.code
    id = resp.json['data']['id']
    dsn = Dsn.get(id=id)
    assert dsn.dsn_type == data['dsn_type']
    assert dsn.name == data['name']
    global dsn_id
    dsn_id = id


def test_get_dsn():
    resp = test_client.get(f'/api/v1/dsn/{dsn_id}')
    assert resp.status_code == 200
    print(json.dumps(resp.json))
    dsn = Dsn.get(id=dsn_id)
    assert resp.json['data']['connect'] == dsn.connect
    assert resp.json['data']['org_id'] == dsn.org_id


@pytest.mark.parametrize('data', [
    {'name': 'add_by_dsn_test', 'code': SUCCESS.code},
    {'name': '11111', 'code': RESOURCE_NOT_FOUND.code}
])
def test_get_dsn_by_name(data):
    resp = test_client.get(f'/api/v1/dsn/{data["name"]}')
    assert resp.status_code == 200
    assert resp.json['code'] == data['code']
    print(json.dumps(resp.json))
    if data['code'] == SUCCESS.code:
        dsn = Dsn.get(id=dsn_id)
        assert resp.json['data']['connect'] == dsn.connect
        assert resp.json['data']['org_id'] == dsn.org_id


def test_update_dsn():
    data = {
        'name': 'add_by_dsn_test11',
        'dsn_type': 'hivehhh',
        'comment': 'use for test11'
    }
    resp = test_client.post(f'/api/v1/dsn/{dsn_id}', json=data)
    assert resp.status_code == 200
    print(resp.json)
    assert resp.json['code'] == SUCCESS.code
    id = resp.json['data']['id']
    dsn = Dsn.get(id=id)
    db.session.refresh(dsn)
    assert dsn.dsn_type == data['dsn_type']
    assert dsn.name == data['name']


def test_update_old_dsn():
    """测试2023年之前创建的DSN不可更新的逻辑"""
    # 创建一个2023年之前的DSN记录
    old_dsn = Dsn(
        name='old_dsn_test',
        connect=json.dumps({'key': 'value'}),
        dsn_type='hive',
        org_id=0,
        comment='old dsn for test',
        create_user='test_user',
        update_user='test_user'
    )
    # 修改创建时间为2022年
    from datetime import datetime
    old_dsn.create_time = datetime(2022, 12, 31, 23, 59, 59)
    db.session.add(old_dsn)
    db.session.commit()
    
    # 尝试更新这个旧的DSN
    data = {
        'name': 'old_dsn_test_updated',
        'dsn_type': 'hive_updated',
        'comment': 'try to update old dsn'
    }
    resp = test_client.post(f'/api/v1/dsn/{old_dsn.id}', json=data)
    assert resp.status_code == 200
    print(resp.json)
    assert resp.json['code'] == RESOURCE_NOT_AVAILABLE.code
    
    # 清理测试数据
    db.session.delete(old_dsn)
    db.session.commit()
