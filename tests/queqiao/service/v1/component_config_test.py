"""
Author: xiaohei
Date: 2022/8/3
Email: <EMAIL>
Host: xiaohei.info
"""
import json

import pytest

from queqiao.conf.ApiResponse import SUCCESS, MISSING_PARAMS
from queqiao.dba import db
from queqiao.dba.models import ComponentConfig
from tests import test_client, task_type_talos2ftp, talos_source_config, ftp_sink_config

component_config_id = 0


@pytest.mark.parametrize('params', [
    (0, 0),
    (task_type_talos2ftp.source_id, len(talos_source_config) + 1),
    (task_type_talos2ftp.sink_id, len(ftp_sink_config) + 1)
])
def test_get_config(params):
    cid, length = params
    resp = test_client.get(f'/api/v1/component/config/{cid}')
    assert resp.status_code == 200
    print(json.dumps(resp.json))
    assert resp.json['code'] == SUCCESS.code
    assert len(resp.json['data']) == length


@pytest.mark.parametrize('params', [
    ({
         'name': 'test_from_component_config_test',
         'name_cn': '测试组件',
         'type': 'int',
         'default': '1',
         'required': '1',
         'demo': '1',
         'cid': task_type_talos2ftp.source_id
     }, SUCCESS.code),
    ({}, MISSING_PARAMS.code)
])
def test_add_config(params):
    data, code = params
    resp = test_client.post(f'/api/v1/component/config/', json=data)
    assert resp.status_code == 200
    print(json.dumps(resp.json))
    assert resp.json['code'] == code
    if code == SUCCESS.code:
        global component_config_id
        component_config_id = resp.json['data']['id']
        component_config = ComponentConfig.get(id=component_config_id)
        assert component_config.name == data['name']
        assert component_config.type == data['type']


def test_update_config():
    data = {'type': 'list'}
    resp = test_client.post(f'/api/v1/component/config/{component_config_id}', json=data)
    assert resp.status_code == 200
    print(json.dumps(resp.json))
    assert resp.json['code'] == SUCCESS.code
    component_config = ComponentConfig.get(id=component_config_id)
    db.session.refresh(component_config)
    assert component_config.type == data['type']


def test_delete_config():
    cid, code = component_config_id, SUCCESS.code
    resp = test_client.delete(f'/api/v1/component/config/{cid}')
    assert resp.status_code == 200
    print(json.dumps(resp.json))
    assert resp.json['code'] == code
    if code == SUCCESS.code:
        assert resp.json['data']['id'] == cid
        component_config = ComponentConfig.get(id=component_config_id)
        assert not component_config
        component_config = ComponentConfig.get(id=component_config_id, see_delete=1)
        db.session.refresh(component_config)
        assert component_config.is_delete == 1
