"""
Author: xiaohei
Date: 2022/8/3
Email: <EMAIL>
Host: xiaohei.info
"""
import json

import pytest

from queqiao.conf.ApiResponse import DO_NOTHING, LOGIN_FAILED
from tests import test_client_not_cookie


@pytest.mark.parametrize('params', [('jiangyuande', 'password', 0), ('xiaohei', '123', LOGIN_FAILED.code)])
def test_login(params):
    username, password, code = params
    resp = test_client_not_cookie.get(f'/api/v1/sso/login', json={'username': username, 'password': password})
    assert resp.status_code == 200
    print(json.dumps(resp.json))
    assert resp.json['code'] == code


def test_logincallback():
    resp = test_client_not_cookie.get(f'/api/v1/sso/logincallback')
    assert resp.status_code == 200
    print(json.dumps(resp.json))
    assert resp.json['code'] == DO_NOTHING.code


def test_logout():
    resp = test_client_not_cookie.get(f'/api/v1/sso/logout')
    assert resp.status_code == 200
    print(json.dumps(resp.json))
    assert resp.json['code'] == DO_NOTHING.code

