"""FTP服务相关测试用例

Author: xiaohei
Date: 2025/01/20
Email: <EMAIL>
Host: xiaohei.info
"""
import json
import pytest
from unittest.mock import patch

from queqiao.conf.ApiResponse import SUCCESS, RESOURCE_NOT_FOUND, ILLEGAL_PARAMS, MISSING_PARAMS
from queqiao.dba import db
from queqiao.dba.models import Dsn
from queqiao.util.conn.ftp import RemoteFileServer
from tests import test_client

# 测试数据
test_dsn_data = {
    'name': 'test_sftp_dsn',
    'connect': json.dumps({
        'protocol': 'sftp',
        'ip': 'one-sftp.vip.sankuai.com',
        'port': 2222,
        'username': 'queqiao',
        'password': 'Xkh5bgcY60rBHU',
        'work_dir': '/one-sftp-xy-bank/queqiao'
    }),
    'dsn_type': 'sftp',
    'org_id': 0,
    'comment': 'SFTP test connection',
    'create_user': 'test_user',
    'update_user': 'test_user'
}

test_direct_data = {
    'protocol': 'sftp',
    'ip': 'one-sftp.vip.sankuai.com',
    'port': 2222,
    'username': 'queqiao',
    'password': 'Xkh5bgcY60rBHU',
    'work_dir': '/one-sftp-xy-bank/queqiao'
}

@pytest.fixture(scope='module')
def setup_test_dsn():
    # 创建测试用DSN
    dsn = Dsn(**test_dsn_data)
    db.session.add(dsn)
    db.session.commit()
    yield dsn
    # 清理测试数据
    db.session.delete(dsn)
    db.session.commit()

def test_test_dsn_connection_success(setup_test_dsn):
    """测试通过DSN名称测试连接成功的情况"""
    resp = test_client.get(f'/api/v1/ftp/test/{test_dsn_data["name"]}')
    assert resp.status_code == 200
    assert resp.json['code'] == SUCCESS.code
    assert resp.json['data']['connected'] is True
    assert resp.json['data']['error'] is None

def test_test_dsn_connection_failure(setup_test_dsn):
    """测试通过DSN名称测试连接失败的情况"""
    # 修改密码以触发认证失败
    wrong_connect = json.loads(test_dsn_data['connect'])
    wrong_connect['password'] = 'wrong_password'
    setup_test_dsn.connect = json.dumps(wrong_connect)
    db.session.commit()

    resp = test_client.get(f'/api/v1/ftp/test/{test_dsn_data["name"]}')
    assert resp.status_code == 200
    assert resp.json['code'] == SUCCESS.code
    assert resp.json['data']['connected'] is False
    assert "Invalid username or password" in resp.json['data']['error']

    # 恢复正确的密码
    setup_test_dsn.connect = test_dsn_data['connect']
    db.session.commit()

def test_test_dsn_connection_not_found():
    """测试不存在的DSN名称"""
    resp = test_client.get('/api/v1/ftp/test/nonexistent_dsn')
    assert resp.status_code == 200
    assert resp.json['code'] == RESOURCE_NOT_FOUND.code

def test_test_dsn_connection_by_id_success(setup_test_dsn):
    """测试通过DSN ID测试连接成功的情况"""
    resp = test_client.get(f'/api/v1/ftp/test/id/{setup_test_dsn.id}')
    assert resp.status_code == 200
    assert resp.json['code'] == SUCCESS.code
    assert resp.json['data']['connected'] is True
    assert resp.json['data']['error'] is None

def test_test_dsn_connection_by_id_failure(setup_test_dsn):
    """测试通过DSN ID测试连接失败的情况"""
    # 修改端口以触发连接失败
    wrong_connect = json.loads(test_dsn_data['connect'])
    wrong_connect['port'] = 1234
    setup_test_dsn.connect = json.dumps(wrong_connect)
    db.session.commit()

    resp = test_client.get(f'/api/v1/ftp/test/id/{setup_test_dsn.id}')
    assert resp.status_code == 200
    assert resp.json['code'] == SUCCESS.code
    assert resp.json['data']['connected'] is False
    assert "SSH connection error" in resp.json['data']['error']

    # 恢复正确的端口
    setup_test_dsn.connect = test_dsn_data['connect']
    db.session.commit()

def test_test_dsn_connection_by_id_not_found():
    """测试不存在的DSN ID"""
    resp = test_client.get('/api/v1/ftp/test/id/99999')
    assert resp.status_code == 200
    assert resp.json['code'] == RESOURCE_NOT_FOUND.code

def test_test_direct_connection_success():
    """测试直接提供FTP信息连接成功的情况"""
    resp = test_client.post('/api/v1/ftp/test', json=test_direct_data)
    assert resp.status_code == 200
    assert resp.json['code'] == SUCCESS.code
    assert resp.json['data']['connected'] is True
    assert resp.json['data']['error'] is None

def test_test_direct_connection_failure():
    """测试直接提供FTP信息连接失败的情况"""
    wrong_data = test_direct_data.copy()
    wrong_data['password'] = 'wrong_password'
    
    resp = test_client.post('/api/v1/ftp/test', json=wrong_data)
    assert resp.status_code == 200
    assert resp.json['code'] == SUCCESS.code
    assert resp.json['data']['connected'] is False
    assert "Invalid username or password" in resp.json['data']['error']

@pytest.mark.parametrize('invalid_data,expected_detail,expected_code', [
    (
        {'protocol': 'sftp', 'username': 'user', 'password': 'pass'},
        '下列参数缺失: port,ip',
        MISSING_PARAMS.code
    ),
    (
        {'protocol': 'sftp', 'ip': 'test.com', 'port': 'invalid', 'username': 'user', 'password': 'pass'},
        '非法的参数: Invalid port number',
        ILLEGAL_PARAMS.code
    )
])
def test_test_direct_connection_invalid_params(invalid_data, expected_detail, expected_code):
    """测试直接提供无效的FTP信息的情况"""
    resp = test_client.post('/api/v1/ftp/test', json=invalid_data)
    assert resp.status_code == 200
    assert resp.json['code'] == expected_code

# Mock文件列表数据
mock_file_list = [
    {'name': 'zip_demo_2.zip', 'type': 'file'},
    {'name': 'zhgf_test', 'type': 'file'},
    {'name': 'sink', 'type': 'dir'}
]

@pytest.fixture
def mock_ftp_client():
    with patch('queqiao.util.conn.ftp.RemoteFileServer') as mock_server:
        mock_instance = mock_server.get_connect.return_value
        mock_instance.list_dir_info.return_value = mock_file_list
        yield mock_instance

def test_list_ftp_content_success(setup_test_dsn, mock_ftp_client):
    """测试通过DSN ID获取FTP目录内容成功的情况"""
    resp = test_client.get(f'/api/v1/ftp/list/id/{setup_test_dsn.id}', query_string={'ftp_path': '/one-sftp-xy-bank/queqiao/test/mock'})
    assert resp.status_code == 200
    assert resp.json['code'] == SUCCESS.code
    # 由于文件列表顺序可能不一致，我们需要比较排序后的列表
    assert sorted(resp.json['data']['files'], key=lambda x: x['name']) == sorted(mock_file_list, key=lambda x: x['name'])

def test_list_ftp_content_dsn_not_found():
    """测试DSN ID不存在的情况"""
    resp = test_client.get('/api/v1/ftp/list/id/99999', query_string={'ftp_path': '/test'})
    assert resp.status_code == 200
    assert resp.json['code'] == RESOURCE_NOT_FOUND.code

def test_list_ftp_content_missing_path(setup_test_dsn):
    """测试缺少ftp_path参数的情况"""
    resp = test_client.get(f'/api/v1/ftp/list/id/{setup_test_dsn.id}')
    assert resp.status_code == 200
    assert resp.json['code'] == MISSING_PARAMS.code

def test_list_ftp_content_connection_error(setup_test_dsn, mock_ftp_client):
    """测试FTP连接失败的情况"""
    mock_ftp_client.open.side_effect = Exception('Connection failed')
    resp = test_client.get(f'/api/v1/ftp/list/id/{setup_test_dsn.id}', query_string={'ftp_path': '/one-sftp-xy-bank/queqiao/test/mock'})
    assert resp.status_code == 200
    assert resp.json['code'] == SUCCESS.code

def test_list_ftp_content_by_name_success(setup_test_dsn, mock_ftp_client):
    """测试通过DSN名称获取FTP目录内容成功的情况"""
    resp = test_client.get(f'/api/v1/ftp/list/{test_dsn_data["name"]}', query_string={'ftp_path': '/one-sftp-xy-bank/queqiao/test/mock'})
    assert resp.status_code == 200
    assert resp.json['code'] == SUCCESS.code
    # 由于文件列表顺序可能不一致，我们需要比较排序后的列表
    assert sorted(resp.json['data']['files'], key=lambda x: x['name']) == sorted(mock_file_list, key=lambda x: x['name'])

def test_list_ftp_content_by_name_not_found():
    """测试DSN名称不存在的情况"""
    resp = test_client.get('/api/v1/ftp/list/nonexistent_dsn', query_string={'ftp_path': '/test'})
    assert resp.status_code == 200
    assert resp.json['code'] == RESOURCE_NOT_FOUND.code

def test_list_ftp_content_direct_success(mock_ftp_client):
    """测试直接提供FTP信息获取目录内容成功的情况"""
    params = test_direct_data.copy()
    params['ftp_path'] = '/one-sftp-xy-bank/queqiao/test/mock'
    resp = test_client.get('/api/v1/ftp/list/direct', query_string=params)
    assert resp.status_code == 200
    assert resp.json['code'] == SUCCESS.code
    # 由于文件列表顺序可能不一致，我们需要比较排序后的列表
    assert sorted(resp.json['data']['files'], key=lambda x: x['name']) == sorted(mock_file_list, key=lambda x: x['name'])

def test_list_ftp_content_direct_invalid_port():
    """测试直接提供无效端口号的情况"""
    params = test_direct_data.copy()
    params['port'] = 'invalid'
    params['ftp_path'] = '/one-sftp-xy-bank/queqiao/test/mock'
    resp = test_client.get('/api/v1/ftp/list/direct', query_string=params)
    assert resp.status_code == 200
    assert resp.json['code'] == ILLEGAL_PARAMS.code
    assert '非法的参数: Invalid port number' in resp.json['message']
