"""
Author: xiaohei
Date: 2022/8/1
Email: <EMAIL>
Host: xiaohei.info
"""
import json
import sys
import time
from concurrent.futures.thread import ThreadPoolExecutor

from instance.default import system_env, IPADDR, PROJECT_PATH
from queqiao.api import app
from queqiao.conf.enums import LinuxPermission, PermissionStatus, TaskStatus, ApplyStatus, ExecutionStatus, TransType
from queqiao.conf.system import SystemConfig
from queqiao.core.execute.task import Task
from queqiao.core.execute.execution import Execution
from queqiao.dba import db
from queqiao.dba.extend_model import TaskType, TaskPermission, Apply
from queqiao.dba.models import Project, Engine, Component, TaskConfig, ComponentConfig, Org, Dsn, ProjectUserRelation
from queqiao.util.comm import strutil, osutil
from queqiao.util.comm.dtutil import timer
from queqiao.util.conn.fate import FateClient
from queqiao.util.conn.ftp import RemoteFileServer
from queqiao.util.hadoop.pyhive import PyhiveClient

if system_env not in ['test', 'local']:
    print(f'current env is: {system_env}, not local or test mode, exit!')
    sys.exit(1)

print('cleaning db tables...')
db.get_engine().execute('SET FOREIGN_KEY_CHECKS = 0')
for table_name in db.metadata.tables:
    if table_name == 'system_config':
        continue
    cmd = f"truncate table {table_name}"
    print(cmd)
    db.get_engine().execute(cmd)
db.get_engine().execute('SET FOREIGN_KEY_CHECKS = 1')
print('table truncate success')
# db.drop_all()
# db.create_all()

ftp_test_path = '/one-sftp-xy-bank/queqiao/test'


def __add_component(opt, ds):
    component = Component.get_one(operator=opt, datasource=ds, create_user='jiangyuande', update_user='jiangyuande')
    if not component:
        component = Component.new(operator=opt, datasource=ds, create_user='jiangyuande', update_user='jiangyuande')
        component.save()
    return component


def __add_task_permission(task_id):
    task_permission4 = TaskPermission.new(task_id=task_id, user_id='jiangyuande_4',
                                          permission=LinuxPermission.READ.value,
                                          status=PermissionStatus.ENTABLE.value)
    task_permission2 = TaskPermission.new(task_id=task_id, user_id='jiangyuande_2',
                                          permission=LinuxPermission.WRITE.value,
                                          status=PermissionStatus.ENTABLE.value)
    task_permission1 = TaskPermission.new(task_id=task_id, user_id='jiangyuande_1',
                                          permission=LinuxPermission.EXECUTE.value,
                                          status=PermissionStatus.ENTABLE.value)
    task_permission3 = TaskPermission.new(task_id=task_id, user_id='jiangyuande_3',
                                          permission=LinuxPermission.WRITE_EXECUTE.value,
                                          status=PermissionStatus.ENTABLE.value)
    task_permission6 = TaskPermission.new(task_id=task_id, user_id='jiangyuande_6',
                                          permission=LinuxPermission.READ_WRITE.value,
                                          status=PermissionStatus.ENTABLE.value)
    task_permission5 = TaskPermission.new(task_id=task_id, user_id='jiangyuande_5',
                                          permission=LinuxPermission.READ_EXECUTE.value,
                                          status=PermissionStatus.ENTABLE.value)
    task_permission7 = TaskPermission.new(task_id=task_id, user_id='jiangyuande_7',
                                          permission=LinuxPermission.READ_WRITE_EXECUTE.value,
                                          status=PermissionStatus.ENTABLE.value)
    task_permission1.save()
    task_permission2.save()
    task_permission3.save()
    task_permission4.save()
    task_permission5.save()
    task_permission6.save()
    task_permission7.save()


def __add_project_permission(project_id, user_id, role_type, is_default):
    r = ProjectUserRelation.new(project_id=project_id, user_id=user_id, role_type=role_type, is_default=is_default)
    r.save()


def add_project(name, is_admin=1, admins='jiangyuande'):
    project = Project.new(create_user='jiangyuande', update_user='jiangyuande', name=name,
                          desc=f'describe project {name}',
                          admins=admins)
    project.save()
    __add_project_permission(project.id, 'jiangyuande', is_admin, is_admin)
    __add_project_permission(project.id, 'chentianzeng', 0, is_admin)
    return project


def add_engine(name, cmd):
    engine = Engine.new(create_user='jiangyuande', update_user='jiangyuande', name=name, enable=1, cmd=cmd,
                        params='{}')
    engine.save()
    return engine


def add_apply(project_id):
    apply = Apply.new(create_user='jiangyuande', update_user='jiangyuande', scenario='pytest', describe='from pytest',
                      security_level=3, status=ApplyStatus.AGREE.value, project_id=project_id)
    apply.save()
    return apply


def add_org(name):
    org = Org.new(create_user='jiangyuande', update_user='jiangyuande', name=name, ad_users='jiangyuande')
    org.save()
    return org


def add_dsn(org_id, name, connect, dsn_type):
    dsn = Dsn.new(name=name, connect=json.dumps(connect), dsn_type=dsn_type, create_user='jiangyuande',
                  update_user='jiangyuande',
                  org_id=org_id)
    dsn.save()
    return dsn


def _add_task_type(code, name, source, sink):
    source_component = __add_component('source', source)
    sink_component = __add_component('sink', sink)
    task_type = TaskType.new(code=code, name=name, engines='Ftplink', create_user='jiangyuande',
                             update_user='jiangyuande')
    task_type.source_id = source_component.id
    task_type.sink_id = sink_component.id
    task_type.save()
    return task_type


def _add_task(task_name, apply_id, task_type_id, engine_id, project_id, create_user='jiangyuande'):
    params = {'k1': 'v1', 'k2': 'v2'}
    task = Task.new(create_user=create_user, update_user=create_user, name=task_name,
                    alarm_receivers='jiangyuande', status=TaskStatus.SUCCESS.value, apply_id=apply_id,
                    task_type_id=task_type_id, engine_id=engine_id, params=json.dumps(params),
                    project_id=project_id, trans_type=TransType.SINGLE.value)
    task.save()
    __add_task_permission(task.id)
    return task


def _add_task_config(key, value, task_id, cid):
    task_config = TaskConfig.new(key=key, value=value, task_id=task_id, cid=cid,
                                 create_user='jiangyuande')
    task_config.save()
    if not ComponentConfig.exists(name=key, cid=cid):
        component_config = ComponentConfig.new(name=key, name_cn=f'cn for key {key}', type='string',
                                               default='default', required=1,
                                               demo='demo', cid=cid)
        component_config.save()


print('initing db testcase...')
add_project('tmp', is_admin=0, admins='chentianzeng')
project = add_project('pytest_project')
project_not_admin = add_project('pytest_project_not_admin', is_admin=0, admins='chentianzeng')
engine_ftplink = add_engine('Ftplink', None)
engine_datax = add_engine('DataX',
                          'python %s/bin/datax.py {config_file} > {log_file} 2>&1' % SystemConfig.read('DATAX_HOME'))
apply = add_apply(project.id)
meituan_org = add_org('org_meituan')
tonglian_org = add_org('org_tonglian')
talos_dsn = add_dsn(meituan_org.id, 'dsn_talos', {}, 'talos')
talos_dsn_with_passwd = add_dsn(meituan_org.id, 'talos_dsn_with_passwd',
                                {"uname": "talos_algo_ftplink", "passwd": "VDsbbd#877", "engine": "onesql"}, 'talos')
ftp_dsn = add_dsn(tonglian_org.id, 'dsn_ftp', {
    "ip": "one-sftp.vip.sankuai.com",
    "port": 2222,
    "protocol": "sftp",
    "username": "queqiao",
    "password": "Xkh5bgcY60rBHU",
    "work_dir": ftp_test_path
}, 'ftp')
mysql_dsn = add_dsn(meituan_org.id, 'dsn_mysql',
                    {'ip': 'localhost', 'port': 3306, 'db': 'queqiao', 'username': 'root', 'password': 'root'}, 'mysql')

pyhive_connect = {"pyhive_host": SystemConfig.read('PYHIVE_HOST'),
                  "pyhive_port": SystemConfig.read('PYHIVE_PORT'),
                  "kerberos_service_name": SystemConfig.read('KERBEROS_SERVICE_NAME'),
                  "kerberos_user": SystemConfig.read('KERBEROS_USER'),
                  "beeline_u": SystemConfig.read('BEELINE_U'),
                  "keytab": SystemConfig.read('KERBEROS_KEYTAB')}
hive_dsn = add_dsn(meituan_org.id, 'dsn_hive', {
    "db": "tgdw",
    "hdfs_path": "/warehouse/tablespace/external/hive",
    **pyhive_connect
}, 'hive')
hivesql_dsn = add_dsn(meituan_org.id, 'dsn_hivesql', {
    **pyhive_connect
}, 'hivesql')


def init_task_testcase(task_type_code, source, source_dict, sink, sink_dict, project_=project,
                       create_user='jiangyuande'):
    task_type = _add_task_type(task_type_code, f'{source}2{sink}', source, sink)
    task = _add_task(task_name=f'{source}2{sink}', apply_id=apply.id,
                     task_type_id=task_type.id,
                     engine_id=engine_ftplink.id,
                     project_id=project_.id, create_user=create_user)
    for key in source_dict.keys():
        value = source_dict[key]
        _add_task_config(key=key, value=value, task_id=task.id, cid=task_type.source_id)
    for key in sink_dict.keys():
        value = sink_dict[key]
        _add_task_config(key=key, value=value, task_id=task.id, cid=task_type.sink_id)

    _add_task_config(key='dsn', value=talos_dsn.id, task_id=task.id, cid=task_type.source_id)
    _add_task_config(key='dsn', value=ftp_dsn.id, task_id=task.id, cid=task_type.sink_id)
    return task_type, task


file_source_config = {
    'file': '/tmp/test_data_header.txt', 'has_header': True, 'target_sep': 'special',
    'current': timer.now().delta(1).date
}
file_test_content = "9999\x011\x010000\n0000\x012\x019999"

osutil.call(f'echo col1\x01col2\x01col3 > {file_source_config["file"]}')
osutil.call(f'echo "{file_test_content}" >> {file_source_config["file"]}')

ftp_source_config = {
    'file_path': f'{ftp_test_path}/sink/{timer.now().datekey}',
    'file_name': f'test_by_pytest_{timer.now().datekey}.txt',
    # 'ok_file': None,
    'lastest_ready_time': timer.now().hourmin,
    'alarm_receivers': 'jiangyuande',
    'max_waiting_hours': '1',
    'ftp_polling_sec': '3',
    'target_date': timer.now().delta(1).datekey,
    'target_sep': 'special',
    'has_header': False,
    'table_cols': 'col1,col2,col3',
    # 'compress_type': None,
    # 'compress_passwd': None,
    'current': timer.now().delta(1).date
}

sftp_client = RemoteFileServer.get_connect(json.loads(ftp_dsn.connect))
sftp_client.open()

hive_source_config = {
    'table_name': 'test_by_pytest_parted_date_auto',
    # 'table_cols': None,
    'data_range': 'partition',
    'partition_key': 'partition_date',
    'partition_value': timer.now().delta(1).date,
    'current': timer.now().delta(1).date
}

pyhive_client = None
if system_env == 'test' and not IPADDR.startswith('10.2'):
    pyhive_client = PyhiveClient(**pyhive_connect)
    pyhive_client.open()

hivesql_source_config = {
    'sql': 'select * from tgdw.ftplink_hive_sink_test_parted_date',
    'target_date': timer.now().delta(1).datekey,
    'current': timer.now().delta(1).date
}

talos_source_config = {
    # 'sql': 'select id,age from {table_name} where partition_date="$now.delta(10).datekey"',
    'sql': 'select id,age from {table_name}',
    'target_date': timer.now().delta(1).datekey,
    'uname': 'talos_algo_ftplink',
    'passwd': 'VDsbbd#877',
    'engine': 'hive',
    'table_name': 'mart_fspinno_queqiao.pytest',
    'dt_key10': '$now.delta(10).datekey',
    'dt_key5': '$now.delta(5).date',
    'current': timer.now().delta(1).date
}

mysql_source_config = {
    'source_key1': 'source_value1',
    'sql': 'select * from engine',
    'split_pk': 'id',
    'speed_parallelism': 1,
    'error_percentage': 0,
    'current': timer.now().delta(1).date
}

# ===sink====

hive_sink_config = {
    'table_name': 'test_by_pytest_parted_date_auto',
    # none/special
    'target_sep': 'special',
    # none/partition
    'table_type': 'partition',
    # none/partition_date
    # 'partition_key': None,
    # into/overwrite
    'write_mode': 'overwrite',
    'null_format': '\\N',
    'current': timer.now().delta(1).date
}

ftp_sink_config = {
    # 全路径/相对路径
    'file_path': f'/one-sftp-xy-bank/queqiao/test/sink/{timer.now().datekey}',
    'file_name': f'test_by_pytest_{timer.now().datekey}.txt',
    # none/.ok/abc.ready/
    'ok_file': '.ok',
    # none/true
    # 'retlist_file': None,
    # none/true
    # 'delete_localfile': None,
    # none/zip/tar.gz/7z
    # 'compress_type': None,
    # none/meituan
    # 'compress_passwd': None,
    # 测试存储在db中的格式
    'null_format': '\\N',
    'current': timer.now().delta(1).date
}

file_sink_config = {'target_sep': 'special', 'file_path': '/tmp/queqiao_file_sink', 'file_name': 'queqiao_test',
                    'name': 'file'}

# https://km.sankuai.com/page/**********
task_type_ftp2hive, task_ftp2hive = init_task_testcase(111101, 'ftp', ftp_source_config, 'hive', hive_sink_config)
task_type_hive2ftp, task_hive2ftp = init_task_testcase(111201, 'hive', hive_source_config, 'ftp', ftp_sink_config)
task_type_talos2ftp, task_talos2ftp = init_task_testcase(111203, 'talos', talos_source_config, 'ftp', ftp_sink_config)
task_type_file2ftp_not_admin, task_file2ftp_not_admin = init_task_testcase(111204, 'file', file_source_config, 'ftp',
                                                                           ftp_sink_config,
                                                                           project_=project_not_admin,
                                                                           create_user='chentianzeng')
task_type_mysql2file, task_mysql2file = init_task_testcase(111206, 'mysql', mysql_source_config, 'file',
                                                           file_sink_config)
task_types = [task_type_ftp2hive, task_type_hive2ftp, task_type_talos2ftp, task_type_file2ftp_not_admin,
              task_type_mysql2file]
import_task_types = [task_type_ftp2hive]
tasks = [task_ftp2hive, task_hive2ftp, task_talos2ftp, task_file2ftp_not_admin, task_mysql2file]

# ---execution---
execution_talos2ftp = Execution.new(create_user='jiangyuande', update_user='jiangyuande',
                                    status=ExecutionStatus.WRITING.value, qid=strutil.uid(),
                                    start_time=timer.now().delta(-1).datetime, end_time=timer.now().datetime,
                                    task_id=task_talos2ftp.id, project_id=project.id, source_org_id=meituan_org.id,
                                    sink_org_id=tonglian_org.id, engine_id=engine_ftplink.id,
                                    params=json.dumps(
                                        {
                                            "source": {
                                                **talos_source_config,
                                                'dsn': talos_dsn.to_dict(),
                                                'name': 'talos',
                                                'org': meituan_org.to_dict()
                                            },
                                            "sink": {
                                                **ftp_sink_config,
                                                'dsn': ftp_dsn.to_dict(),
                                                "name": "ftp",
                                                "org": tonglian_org.to_dict(),
                                            }, "k11": "v11", "k22": "v22",
                                            "async": False,
                                            'current': timer.now().delta(1).date,
                                            'alarm_receivers': 'jiangyuande',
                                            'task_name': f'{project.name}.{task_talos2ftp.name}'
                                        })
                                    )
execution_talos2ftp.save()

execution_mysql2file = Execution.new(create_user='jiangyuande', update_user='jiangyuande',
                                     status=ExecutionStatus.INIT.value, qid=strutil.uid(),
                                     start_time=timer.now().delta(-1).datetime, end_time=timer.now().datetime,
                                     task_id=task_mysql2file.id, project_id=project.id, source_org_id=meituan_org.id,
                                     sink_org_id=tonglian_org.id, engine_id=engine_datax.id,
                                     params=json.dumps(
                                         {
                                             "source": {
                                                 **mysql_source_config,
                                                 'dsn': mysql_dsn.to_dict(),
                                                 'name': 'mysql',
                                                 'org': meituan_org.to_dict()
                                             },
                                             "sink": {
                                                 **file_sink_config,
                                                 "name": "file",
                                                 "org": tonglian_org.to_dict(),
                                             }, "k11": "v11", "k22": "v22",
                                             "async": False,
                                             'current': timer.now().delta(1).date,
                                             'alarm_receivers': 'jiangyuande',
                                             'task_name': f'{project.name}.{task_mysql2file.name}'
                                         })
                                     )
execution_mysql2file.save()
print('db testcase init success')

print('initing flask test client...')
with app.test_client() as test_client:
    test_client.set_cookie('localhost', 'uid', 'jiangyuande')
    test_client.set_cookie('localhost', 'aid', '1')
    test_client.set_cookie('localhost', 'gid', '274200008')
    test_client.set_cookie('localhost', 'token', '5aedc262038502407fff9bcbf4461280')
    test_client.set_cookie('localhost', 'groups', json.dumps(['ipausers', 'queqiao_admin']))
    test_client.set_cookie('localhost', 'projects',
                           json.dumps([
                               {'id': project.id, 'name': project.name, 'selected': 1},
                               {'id': project_not_admin.id, 'name': project_not_admin.name, 'selected': 0}
                           ]))

with app.test_client() as test_client_not_admin:
    test_client_not_admin.set_cookie('localhost', 'uid', 'chentianzeng')
    test_client_not_admin.set_cookie('localhost', 'aid', '0')
    test_client_not_admin.set_cookie('localhost', 'gid', '274200007')
    test_client_not_admin.set_cookie('localhost', 'token', '63c3446ab52cdca7da18d059f8bd0e62')
    test_client_not_admin.set_cookie('localhost', 'groups', json.dumps(['ipausers', 'queqiao_user_bs']))
    test_client_not_admin.set_cookie('localhost', 'projects',
                                     json.dumps([
                                         {'id': project.id, 'name': project.name, 'selected': 1},
                                         {'id': project_not_admin.id, 'name': project_not_admin.name, 'selected': 0}
                                     ]))
test_client_not_cookie = app.test_client()
print('test client init success')

# ==================Fate============
# 新集群需要重新训练模型
model_id = 'guest-10000#host-9999#model'
model_version = '202212210838252172230'
model_component_name = 'heterosecureboost_0'
predict_component_name = 'hetero_secure_boost_0'
predict_output_data = 'train'
guest_party_id = 10000
host_party_id = 9999
host_server_ip = '*************'
guest_server_ip = '**************'
predict_job_conf = {
    "dsl_version": "2",
    "initiator": {
        "role": "guest",
        "party_id": guest_party_id
    },
    "role": {
        "guest": [
            guest_party_id
        ],
        "host": [
            host_party_id
        ]
    },
    "job_parameters": {
        "common": {
            "task_parallelism": 1,
            "computing_partitions": 16,
            "eggroll_run": {
                "eggroll.session.processors.per.node": 16
            }
        }
    },
    "component_parameters": {
        "common": {
            "model_loader_0": {
                # 新集群替换
                "model_id": model_id,
                # 新集群替换
                "model_version": model_version,
                "component_name": model_component_name,
                "model_alias": "model"
            },
            "intersection_0": {
                "intersect_method": "raw",
                "sync_intersect_ids": True,
                "join_role": "host"
            }
        },
        "role": {
            "guest": {
                "0": {
                    "reader_0": {
                        "table": {
                            "name": "breast_hetero_guest",
                            "namespace": "experiment"
                        }
                    },
                    "dataio_0": {
                        "with_label": True,
                        "label_name": 'y',
                        "label_type": "int",
                        "output_format": "dense"
                    }
                }
            },
            "host": {
                "0": {
                    "reader_0": {
                        "table": {
                            "name": "breast_hetero_host",
                            "namespace": "experiment"
                        }
                    },
                    "dataio_0": {
                        "with_label": False,
                        "output_format": "dense"
                    },
                    "evaluation_0": {
                        "need_run": False
                    }
                }
            }
        }
    }
}
predict_job_dsl = {
    "components": {
        "reader_0": {
            "module": "Reader",
            "output": {
                "data": [
                    "table"
                ]
            }
        },
        "dataio_0": {
            "module": "DataIO",
            "input": {
                "data": {
                    "data": [
                        "reader_0.table"
                    ]
                }
            },
            "output": {
                "data": [
                    "data"
                ],
                "model": [
                    "dataio"
                ]
            }
        },
        "intersection_0": {
            "module": "Intersection",
            "input": {
                "data": {
                    "data": [
                        "dataio_0.data"
                    ]
                }
            },
            "output": {
                "data": [
                    "data"
                ]
            }
        },
        "model_loader_0": {
            "module": "ModelLoader",
            "output": {
                "model": ["model"]
            }
        },
        predict_component_name: {
            "module": "HeteroSecureBoost",
            "input": {
                "model": [
                    "model_loader_0.model"
                ],
                "data": {
                    "test_data": [
                        "intersection_0.data"
                    ]
                }
            },
            "output": {
                "data": [
                    predict_output_data
                ]
            }
        }
    }
}
test_bucket_cnt = 5
# if system_env == 'local':
#     client = FateClient(host_server_ip, 9380)
#     client.open()
#     client.data_upload(f'{PROJECT_PATH}/ext/fate/test_data_host_feature.csv', 1, 'experiment',
#                        'breast_hetero_host')
#     for i in range(0, test_bucket_cnt):
#         client.data_upload(f'{PROJECT_PATH}/ext/fate/test_data_host_feature.csv', 1, 'experiment',
#                            f'breast_hetero_host_{i}')
#     client = FateClient(guest_server_ip, 9380)
#     client.open()
#     client.data_upload(f'{PROJECT_PATH}/ext/fate/test_data_guest_feature_y.csv', 1, 'experiment',
#                        'breast_hetero_guest')
#     for i in range(0, test_bucket_cnt):
#         client.data_upload(f'{PROJECT_PATH}/ext/fate/test_data_guest_feature_y.csv', 1, 'experiment',
#                            f'breast_hetero_guest_{i}')
#     print('======fate case init success')

#
# def start_celery():
#     worker_cmd = f'celery -A queqiao.core.engine worker --loglevel=info -Q normal -c 2'
#     osutil.call(worker_cmd)
#     print('======celery worker started======')
#
#
# time.sleep(0.5)
# cnt = osutil.calls('ps -ef | grep celery | grep -v "grep" | wc -l')
# if int(cnt) == 0:
#     executor = ThreadPoolExecutor(max_workers=2)
#     executor.submit(start_celery)
