#!/bin/bash
curr_path=$(cd "$(dirname $0)";pwd)
source $curr_path/.envrc

max_day=30
curr_date=`date +"%Y-%m-%d %H:%M:%S"`
echo "[$curr_date] check $file_path for $max_day days"
for i in `find $file_path -mtime +$max_day -name "*"`
do
    if [ ! -d $i ]
    then
        continue
    fi
    echo "delete $i"
    rm -rf $i
done

tmp_path="/opt/meituan/tmp/queqiao"
echo "[$curr_date] check $tmp_path for $max_day days"
for i in `find $tmp_path -mtime +$max_day -name "*"`
do
    if [ ! -d $i ]
    then
        continue
    fi
    echo "delete $i"
    rm -rf $i
done