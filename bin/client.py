# -*- coding: utf-8 -*-

"""
Author: xiaohei
Date: 2020/12/28
Email: <EMAIL>
Host: xiaohei.info
"""
import json
import sys
import time
from imp import reload

import requests


def is_py3():
    import sys
    if sys.version > '3':
        return True
    return False


print('current python version is: %s' % sys.version)
if not is_py3():
    reload(sys)
    sys.setdefaultencoding('utf-8')


def get_current_date(f='%Y-%m-%d %H:%M:%S'):
    return time.strftime(f, time.localtime(time.time()))


if len(sys.argv) < 2:
    print("Useage [table_name] [exec_date]")
    sys.exit(1)

SLEEP_TIME = 120

table_name = sys.argv[1]
exec_date = sys.argv[2] if len(sys.argv) > 2 else None
# cantor use python2
print("table_name: %s,exec_date: %s" % (table_name, exec_date))
if len(table_name.split('.')) != 2:
    print('error table_name: %s, usage $project.$table' % table_name)
    sys.exit(1)
api_version = 'v1'
url_prefix = "http://***********:8090/api/%s" % api_version


def _do_request(request_url):
    print("request_url:%s" % request_url)
    res = requests.get(request_url)
    json_result = json.loads(res.content)
    if res.status_code != 200 or json_result['code'] != 0:
        print(json_result['message'])
        print("error return code, exit!")
        sys.exit(1)
    return json_result


def submit_task(task_name, etl_date):
    url = "%s/task/etl/%s?from_etl=1" % (url_prefix, task_name)
    if etl_date:
        url = url + '&etl_date=' + etl_date
    json_result = _do_request(url)
    return json_result['data']['execution_id']


def get_execution_status(id):
    url = "%s/execution/status/%s?from_etl=1" % (url_prefix, id)
    json_result = _do_request(url)
    return json_result['data']['status']


execution_id = submit_task(table_name, exec_date)
status_map = {
    'INIT': 101,
    'QUEUED': 102,
    'RUNNING_PRE': 201,
    'READING': 202,
    'WRITING': 203,
    'RUNNING_POST': 204,
    'RETRY': 205,
    'SUCCESS': 301,
    'FAILED': 401,
    'KILLED': 402,
}

while True:
    status = get_execution_status(execution_id)
    print('execution status: %s' % status)
    status_num = status_map[status]
    if 300 < status_num < 400:
        print('%s execute success' % table_name)
        break
    elif status_num > 400:
        print('%s execute failed' % table_name)
        sys.exit(1)
    else:
        print('sleep %s' % SLEEP_TIME)
        time.sleep(SLEEP_TIME)
