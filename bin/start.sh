#!/bin/bash

curr_path=$(cd "$(dirname $0)";pwd)
source $curr_path/.envrc

if [ $# -lt 1 ]
then
    echo "please enter $all_apps"
    exit 1
fi

apps=$1

echo "QUEQIAO_ENV:$QUEQIAO_ENV"
if [ -z $QUEQIAO_ENV ]
then
    echo "QUEQIAO_ENV dose not set!"
    exit 1
fi

user=`whoami`
# 服务启动名
gunicorn_app="api"
gunicorn_conf=${conf_path}/gunicorn.py

mkdir -p ${project_path}/logs/execution
curr_date=`date +"%Y-%m-%d"`

if [[ $QUEQIAO_ENV =~ "test" ]]
then
    test_env=1
else
    test_env=0
fi

echo "=====start====="

pyenv_cnt=`cat ${conf_path}/env/${QUEQIAO_ENV}.py | grep PYTHON_ENV | wc -l`
if [ $pyenv_cnt -lt 1 ]
then
    PYTHON_ENV="${project_path}/venv/bin"
    echo "can not found any PYTHON_ENV vars, use default pyenv:${PYTHON_ENV}"
else
    PYTHON_ENV=`cat ${conf_path}/${QUEQIAO_ENV}.py | grep PYTHON_ENV | awk -F ' = ' '{print $NF}' | sed "s/'//g" | sed 's/"//g'`
    echo "get PYTHON_ENV:${PYTHON_ENV} from ${conf_path}/${QUEQIAO_ENV}.py"
fi

start() {
    for app in `echo $apps | awk -F , '{for(i=1;i<=NF;i++){printf "%s\n",$i}}'`
    do
        if [[ ! "$all_apps" =~ "$app" ]]
        then
            echo "$app not in bootable app list: ${all_apps}"
            continue
        fi

        echo "[starting ${app}]..."
        me=${app}.${user}
        if [ "$app" == "$gunicorn_app" ]
        then
            # 去除preload参数，gunicorn启动失败没有详细信息时可以使用该参数看到具体异常，但是需要及时去除
            # 因为该参数会让所有worker共享资源，包括数据库连接，故当同时发起的任务过多时会出现连接丢失、连接已关闭、NoneType等错误
            gunicorn_cmd="nohup ${PYTHON_ENV}/gunicorn"
            if [ $test_env -gt 0 ]
            then
                gunicorn_cmd="${gunicorn_cmd} --preload"
                echo "in test env, use --preload mode"
            fi
            start_cmd="${gunicorn_cmd} -c $gunicorn_conf --chdir $project_path queqiao.$app:app >> ${project_path}/logs/queqiao-${me}.log.$curr_date 2>&1 &"
        else
            start_cmd="nohup ${PYTHON_ENV}/python ${service_path}/${app}.py >> ${project_path}/logs/queqiao-${me}.log.$curr_date 2>&1 &"
        fi
        echo "start command: ${start_cmd}"
        eval ${start_cmd}
        echo $! > ${project_path}/${me}.pid
        echo "${app} started."
    done
}

start