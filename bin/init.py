import json
import os
import sys

sys.path.append(os.path.dirname(sys.path[0]))

import click
from queqiao.conf.enums import ApplyStatus, TransType, TaskStatus

from queqiao.conf.system import SystemConfig
from queqiao.dba import db

from instance.default import DATAX_HOME, CONF_PATH, system_env
from queqiao import plugin
from queqiao.api import app
from queqiao.dba.models import Component, ComponentConfig, Engine, TaskType, Org, Dsn, Project, Apply, Task, TaskConfig
from queqiao.util.comm import objutil, osutil


def create_component(clean=True):
    if clean:
        print(f'clean table component and component_config')
        Component.delete_by()
        ComponentConfig.delete_by()
    mdl = objutil.find_mdl_in_pkg(plugin.__path__, plugin.__package__)
    for key in mdl.keys():
        mdl_obj = mdl[key]
        print(f'mdl: {key}, content: {mdl_obj}')
        if hasattr(mdl_obj, 'component'):
            component_info = getattr(mdl_obj, 'component')
            print(f'component info: {component_info}')
            operator = key.split('.')[-2]
            datasource = key.split('.')[-1]
            comment = component_info['comment']
            if Component.exists(operator=operator, datasource=datasource):
                print(f'{operator}{datasource} already exists, skip current component')
                component = Component.get_one(operator=operator, datasource=datasource)
            else:
                kwargs = {'operator': operator, 'datasource': datasource, 'create_user': 'system', 'comment': comment}
                print(f'new component with kwargs: {kwargs}')
                component = Component.new(**kwargs)
                component.save()
            component_configs = component_info['configs']
            for config_name in component_configs.keys():
                if ComponentConfig.exists(name=config_name, cid=component.id):
                    print(
                        f'component config {config_name} already exists in component {component.operator}{component.datasource}')
                else:
                    kwargs = {
                        'name': config_name,
                        'cid': component.id,
                        **component_configs[config_name],
                        'create_user': 'system',
                        'update_user': 'system',
                    }
                    print(f'new component config with kwargs: {kwargs}')
                    component_config = ComponentConfig.new(**kwargs)
                    component_config.save()

            if not ComponentConfig.exists(name='dsn', cid=component.id):
                kwargs = {
                    'name': 'dsn',
                    'name_cn': '操作数据源',
                    'required': 1,
                    'cid': component.id,
                    'create_user': 'system',
                    'update_user': 'system',
                }
                print(f'new dsn component config with kwargs: {kwargs}')
                component_config = ComponentConfig.new(**kwargs)
                component_config.save()
            print('--')


def create_engine(clean=True):
    if clean:
        print(f'clean table engine')
        Engine.delete_by()

    engines = {
        'Ftplink': {},
        'DataX': {'cmd': 'python %s/bin/datax.py {config_file} > {log_file} 2>&1' % DATAX_HOME},
        'Flink': {},
        'Spark': {},
    }

    for key in engines.keys():
        kwargs = {
            'name': key,
            'create_user': 'system',
            'update_user': 'system',
            **engines[key]
        }
        print(f'new engine with kwargs :{kwargs}')
        engine = Engine.new(**kwargs)
        engine.save()


def create_task_type(clean=True):
    if clean:
        print(f'clean table task_type')
        TaskType.delete_by()
    task_types = [
        {'code': 111101, 'source': 'ftp', 'sink': 'hive', 'engines': 'Ftplink,DataX',
         'comment': '外部数据接入（单机批量模式）-ftp数据写入hive库'},
        {'code': 111102, 'source': 'ftp', 'sink': 'mthive', 'engines': 'Ftplink',
         'comment': '外部数据接入（单机批量模式）-ftp数据写入美团hive库'},
        {'code': 111103, 'source': 'ftp', 'sink': 'tgcbchive', 'engines': 'Ftplink',
         'comment': '外部数据接入（单机批量模式）-ftp数据写入天宫（联名卡多租户）hive库'},
        {'code': 111104, 'source': 'ftp', 'sink': 'mysql', 'engines': 'Ftplink,DataX',
         'comment': '外部数据接入（单机批量模式）-ftp数据写入mysql库'},
        {'code': 111105, 'source': 'wtp', 'sink': 'mthive', 'engines': 'Ftplink',
         'comment': '外部数据接入（单机批量模式）-浦发wtp数据写入美团hive库'},
        {'code': 111106, 'source': 'fate', 'sink': 'mthive', 'engines': 'Ftplink',
         'comment': '外部数据接入（单机批量模式）-fate数据写入美团hive库'},
        {'code': 111107, 'source': 'fatetask', 'sink': 'mthive', 'engines': 'Ftplink',
         'comment': '外部数据接入（单机批量模式）-fate任务数据写入美团hive库'},
        {'code': 111108, 'source': 'file', 'sink': 'mthive', 'engines': 'Ftplink',
         'comment': '外部数据接入（单机批量模式）-本地文件写入美团hive库'},
        {'code': 111109, 'source': 'http', 'sink': 'mthive', 'engines': 'Ftplink',
         'comment': '外部数据接入（单机批量模式）-HTTP远程文件写入美团hive库'},
        {'code': 111201, 'source': 'hive', 'sink': 'ftp', 'engines': 'Ftplink,DataX',
         'comment': '内部数据导出（单机批量模式）-hive数据导出至ftp'},
        {'code': 111202, 'source': 'hivesql', 'sink': 'ftp', 'engines': 'Ftplink,DataX',
         'comment': '内部数据导出（单机批量模式）-hive数据导出至ftp（自定义sql）'},
        {'code': 111203, 'source': 'talos', 'sink': 'ftp', 'engines': 'Ftplink,DataX',
         'comment': '内部数据导出（单机批量模式）-hive数据导出至ftp（美团talos）'},
        {'code': 111204, 'source': 'file', 'sink': 'ftp', 'engines': 'Ftplink,DataX',
         'comment': '内部数据导出（单机批量模式）-本地文件导出至ftp'},
        {'code': 111205, 'source': 'mysql', 'sink': 'ftp', 'engines': 'Ftplink,DataX',
         'comment': '内部数据导出（单机批量模式）-mysql数据导出至ftp'},
        {'code': 111206, 'source': 'talos', 'sink': 'hive', 'engines': 'Ftplink',
         'comment': '内部数据导出（单机批量模式）-hive数据（美团talos）导出至hive（外部集群）'},
        {'code': 111207, 'source': 'talos', 'sink': 'fate', 'engines': 'Ftplink',
         'comment': '内部数据导出（单机批量模式）-hive数据（美团talos）导出至fate'},
        {'code': 111208, 'source': 'talos', 'sink': 'fatetask', 'engines': 'Ftplink',
         'comment': '内部数据导出（单机批量模式）-hive数据（美团talos）导出至fate（后执行任务）'},
        {'code': 111209, 'source': 'talos', 'sink': 'mthive', 'engines': 'Ftplink',
         'comment': '内部数据导出（单机批量模式）-hive数据（美团talos）导出至美团hive库'},
        {'code': 111210, 'source': 'file', 'sink': 'fatetask', 'engines': 'Ftplink',
         'comment': '内部数据导出（单机批量模式）-本地文件导出至fate服务'},
        # 111212 ftp2fate
        {'code': 111212, 'source': 'talosctas', 'sink': 'ftp', 'engines': 'Ftplink,DataX',
         'comment': '内部数据导出（单机批量模式）-hive数据导出至ftp（美团talos创建临时表）'},
        {'code': 111213, 'source': 'mysql', 'sink': 'mthive', 'engines': 'Ftplink',
         'comment': '内部数据导出（单机批量模式）-mysql数据导出至美团hive库'},
        # {'code': 112201, 'source': 'binlog', 'sink': 'ftp', 'engines': 'Flink',
        #  'comment': '内部数据导出（单机流处理模式）-binlog数据导出至ftp'},
        # {'code': 212001, 'source': 'binlog', 'sink': 'csv', 'engines': 'Flink,Spark',
        #  'comment': 'cdc数据同步（单机流处理模式）-binlog数据写入本地csv'},
        # {'code': 221001, 'source': 'binlog', 'sink': 'hive', 'engines': 'Flink,Spark',
        #  'comment': 'cdc数据同步（分布式批处理模式）-binlog数据写入hive库'},
        # {'code': 222001, 'source': 'binlog', 'sink': 'hudi', 'engines': 'Flink,Spark',
        #  'comment': 'cdc数据同步（分布式流处理模式）-binlog数据写入hudi数据湖'},
        {'code': 311001, 'source': 'mysql', 'sink': 'mysql', 'engines': 'DataX',
         'comment': '异构数据同步（单机批量模式）-mysql数据同步至mysql库'},
        {'code': 311002, 'source': 'mysql', 'sink': 'file', 'engines': 'DataX',
         'comment': '异构数据同步（单机批量模式）-mysql数据同步至本地文件'},
        # {'code': 311003, 'source': 'es', 'sink': 'mysql', 'engines': 'DataX',
        #  'comment': '异构数据同步（单机批量模式）-es数据同步至mysql库'},
        {'code': 321001, 'source': 'mysql', 'sink': 'hive', 'engines': 'Flink,Spark,DataX',
         'comment': '异构数据同步（分布式批量模式）-mysql数据同步至hive库'},
        # {'code': 322001, 'source': 'kafka', 'sink': 'hudi', 'engines': 'Flink,Spark',
        #  'comment': '异构数据同步（分布式流处理模式）-kakfa数据写入hudi数据湖'}
    ]

    for task_type_info in task_types:
        source_name = task_type_info.pop('source')
        sink_name = task_type_info.pop('sink')
        source_component = Component.get_one(datasource=source_name, operator='source')
        sink_component = Component.get_one(datasource=sink_name, operator='sink')
        kwargs = {
            'name': f'{source_name}2{sink_name}',
            'source_id': source_component.id,
            'sink_id': sink_component.id,
            **task_type_info,
            'create_user': 'system',
            'update_user': 'system'
        }
        print(f'new task_type with kwargs :{kwargs}')
        task_type = TaskType.new(**kwargs)
        task_type.save()


# todo: 统计数据使用dsn维度,dsn以最细粒度创建
orgs = {
    '通联金融': [
        {
            "name": "cbc_2tl_mtout",
            "dsn_type": "ftp",
            "connect": {
                "ip": "one-sftp.vip.sankuai.com",
                "port": 2222,
                "protocol": "sftp",
                "username": "mt_b_aif",
                "password": "Rah1zrZtAdJUk4",
                "work_dir": "/xy-cbc/cbcproduct/cbcproduct/mtout"
            }
        },
        {
            "name": "cbc_2mt_mtout",
            "dsn_type": "ftp",
            "connect": {
                "ip": "one-sftp.vip.sankuai.com",
                "port": 2222,
                "protocol": "sftp",
                "username": "mt_b_aif",
                "password": "Rah1zrZtAdJUk4",
                "work_dir": "/xy-cbc/cbcproduct/aif/mtout"
            }
        },
        {
            "name": "loan_tljr_gdyh",
            "dsn_type": "ftp",
            "connect": {
                "ip": "one-sftp.vip.sankuai.com",
                "port": 2222,
                "protocol": "sftp",
                "username": "queqiao",
                "password": "Xkh5bgcY60rBHU",
                "work_dir": "/one-sftp-xy-bank/queqiao/loan/gd"
            }
        }
    ],
    '银联数据': [
        {
            "name": "loan_ylsj_jsyh",
            "dsn_type": "ftp",
            "connect": {
                "ip": "one-sftp.vip.sankuai.com",
                "port": 2222,
                "protocol": "sftp",
                "username": "queqiao",
                "password": "Xkh5bgcY60rBHU",
                "work_dir": "/one-sftp-xy-bank/queqiao/loan/ylsj_jsyh"
            }
        },
        {
            "name": "loan_ylsj_tjyh",
            "dsn_type": "ftp",
            "connect": {
                "ip": "one-sftp.vip.sankuai.com",
                "port": 2222,
                "protocol": "sftp",
                "username": "queqiao",
                "password": "Xkh5bgcY60rBHU",
                "work_dir": "/one-sftp-xy-bank/queqiao/loan/ylsj_tjyh"
            }
        },
    ],
    '光大银行': [
        {
            "name": "cbc_gdyh",
            "dsn_type": "ftp",
            "connect": {
                "ip": "one-sftp.vip.sankuai.com",
                "port": 2222,
                "protocol": "sftp",
                "username": "mt_card",
                "password": "6l0DxOViQGNfRC",
                "work_dir": "/one-sftp-xy-bank/card/gd"
            }
        },
    ],
    '58同城': [
        {
            "name": "58tc_risk",
            "dsn_type": "ftp",
            "connect": {
                "ip": "**************",
                "port": 22,
                "protocol": "sftp",
                "username": "MT",
                "password": "UD8c7Cuul3mocK0kRXvB4fnf",
                "work_dir": "/upload"
            }
        },
    ],
    '房价网': [
        {
            "name": "fjw_risk",
            "dsn_type": "ftp",
            "connect": {
                "ip": "**************",
                "port": 17495,
                "protocol": "sftp",
                "username": "Mtjr",
                "password": "Mtjr123!@#",
                "work_dir": "/output"
            }
        },
    ],
    '浦发银行': [
        {
            "name": "spdb_loan",
            "dsn_type": "wtp",
            "connect": {
                "protocol": "wtp"
            }
        }
    ],
    '银联智策': [
        {
            "name": "ylzc_pay",
            "dsn_type": "ftp",
            "connect": {
                "ip": "yun.boxincredit.com",
                "port": 54188,
                "protocol": "sftp",
                "username": "MTZF_ymAGILRe",
                "password": "4cGB6doD",
                "work_dir": "upload/mtoutylzc"
            }
        },
    ],
    '银联智慧': [
        {
            "name": "pay_ylzh",
            "dsn_type": "ftp",
            "connect": {
                "ip": "one-sftp.vip.sankuai.com",
                "port": 2222,
                "protocol": "sftp",
                "username": "queqiao",
                "password": "Xkh5bgcY60rBHU",
                "work_dir": "/one-sftp-xy-bank/queqiao/pay/yl"
            }
        }
    ],
    '金融云测试环境': [
        {
            "name": "loan_fincloud_test",
            "dsn_type": "ftp",
            "connect": {
                "ip": "one-sftp.vip.sankuai.com",
                "port": 2222,
                "protocol": "sftp",
                "username": "queqiao",
                "password": "Xkh5bgcY60rBHU",
                "work_dir": "/one-sftp-xy-bank/queqiao/test/loan"
            }
        }
    ],
    '美团金服': [
        {
            "name": "mart_fspinno_queqiao",
            "dsn_type": "hive",
            "connect": {
                "db": "mart_fspinno_queqiao",
                "hdfs_path": "/zw02nn55/warehouse/mart_fspinno_queqiao.db",
                # "db": "tgdw",
                # "hdfs_path": "/warehouse/tablespace/external/hive/tgdw.db",
            }
        },
        {
            "name": "mart_fspinno_queqiao_test",
            "dsn_type": "hive",
            "connect": {
                "db": "mart_fspinno_queqiao_test",
                "hdfs_path": "/zw02nn55/warehouse/mart_fspinno_queqiao_test.db",
            }
        },
        {
            "name": "mart_fspinno_queqiao_stg",
            "dsn_type": "hive",
            "connect": {
                "db": "mart_fspinno_queqiao_stg",
                "hdfs_path": "/zw02nn55/warehouse/mart_fspinno_queqiao_stg.db",
                # todo: default_fs
            }
        },
        {
            "name": "meituan-talos",
            "dsn_type": "talos",
            "connect": {
                "uname": "talos_algo_ftplink",
                "passwd": "VDsbbd#877",
                "engine": "onesql"
            }
        },
        {
            "name": "meituan-queqiao-sftp",
            "dsn_type": "ftp",
            "connect": {
                "ip": "one-sftp.vip.sankuai.com",
                "port": 2222,
                "protocol": "sftp",
                "username": "queqiao",
                "password": "Xkh5bgcY60rBHU",
                "work_dir": "/one-sftp-xy-bank/queqiao"
            }
        }
    ],
    '机构测算中心集群': [
        {
            "name": "tgdw",
            "dsn_type": "hive",
            "connect": {
                "db": "tgdw",
                "hdfs_path": "/tgwarehouse/tgdw.db",
                "pyhive_host": "tggateway.vip.sankuai.com",
                "pyhive_port": "9011",
                "kerberos_service_name": "scm",
                "kerberos_user": "hive",
                "keytab": "/opt/hadoop/keytabs/hive.keytab",
                "beeline_u": '**********************************************************/<EMAIL>',
            }
        },
        {
            "name": "m03070010",
            "dsn_type": "hive",
            "connect": {
                "db": "m03070010",
                "hdfs_path": "/tgwarehouse/m03070010.db",
                "pyhive_host": "tggateway.vip.sankuai.com",
                "pyhive_port": "9011",
                "kerberos_service_name": "scm",
                "kerberos_user": "hive",
                "keytab": "/opt/hadoop/keytabs/hive.keytab",
                "beeline_u": '**********************************************************/<EMAIL>',
            }
        },
        {
            "name": "a04721460",
            "dsn_type": "hive",
            "connect": {
                "db": "a04721460",
                "hdfs_path": "/tgwarehouse/a04721460.db",
                "pyhive_host": "tggateway.vip.sankuai.com",
                "pyhive_port": "9011",
                "kerberos_service_name": "scm",
                "kerberos_user": "hive",
                "keytab": "/opt/hadoop/keytabs/hive.keytab",
                "beeline_u": '**********************************************************/<EMAIL>',
            }
        },
        {
            "name": "m04544240",
            "dsn_type": "hive",
            "connect": {
                "db": "m04544240",
                "hdfs_path": "/tgwarehouse/m04544240.db",
                "pyhive_host": "tggateway.vip.sankuai.com",
                "pyhive_port": "9011",
                "kerberos_service_name": "scm",
                "kerberos_user": "hive",
                "keytab": "/opt/hadoop/keytabs/hive.keytab",
                "beeline_u": '**********************************************************/<EMAIL>',
            }
        }
    ],
    '数科fate服务': [
        {
            "name": "fate-shuke",
            "dsn_type": "fate",
            "connect": {
                'ip': '************',
                'port': 9380
            }
        }
    ],
    '天问': [
        {
            "name": "tianwen",
            "dsn_type": "fate",
            "connect": {
                'ip': '***********',
                'port': 9380
            }
        }
    ],
    '银联智策fate服务': [
        {
            "name": "fate-ylzc",
            "dsn_type": "fate",
            "connect": {
                'ip': '*************',
                'port': 9380
            }
        }
    ],
}


def create_dsn(clean=True):
    if clean:
        print(f'clean table org and dsn')
        Org.delete_by()
        Dsn.delete_by()
    for o in orgs.keys():
        kwargs = {
            'create_user': 'system',
            'update_user': 'system',
            'ad_users': 'jiangyuande',
            'name': o
        }
        print(f'save org with kwargs: {kwargs}')
        if not Org.exists(**kwargs):
            org = Org.new(**kwargs)
            org.save()
        else:
            org = Org.get_one(**kwargs)

        dsns = orgs[o]
        for d in dsns:
            d['connect'] = json.dumps(d['connect'])
            d['org_id'] = org.id
            kwargs = {
                **d,
                'create_user': 'system',
                'update_user': 'system',
            }
            print(f'save dsn with kwargs: {kwargs}')
            dsn = Dsn.new(**kwargs)
            dsn.save()


projects = ['cbc', 'loan', 'risk', 'pay', 'tmp']


def create_project(clean=True):
    if clean:
        print(f'clean table project')
        Project.delete_by()
    for p in projects:
        kwargs = {
            'create_user': 'system',
            'update_user': 'system',
            'admins': 'jiangyuande',
            'name': p
        }
        project = Project.new(**kwargs)
        project.save()


model_id = 'guest-20003#host-20002#model'
model_version = '202207210747294257380'
component_name = 'hetero_secure_boost_0'
label_name = 'label'
output_data = 'train'
predict_job_conf = {
    "dsl_version": "2",
    "initiator": {
        "role": "guest",
        "party_id": 20003
    },
    "role": {
        "guest": [
            20003
        ],
        "host": [
            20002
        ]
    },
    "job_parameters": {
        "common": {
            "task_parallelism": 1,
            "computing_partitions": 16,
            "eggroll_run": {
                "eggroll.session.processors.per.node": 16
            }
        }
    },
    "component_parameters": {
        "common": {
            "model_loader_0": {
                # 新集群替换
                "model_id": model_id,
                # 新集群替换
                "model_version": model_version,
                "component_name": component_name,
                "model_alias": "model"
            },
            "intersection_0": {
                "intersect_method": "raw",
                "sync_intersect_ids": True,
                "join_role": "host"
            }
        },
        "role": {
            "guest": {
                "0": {
                    "reader_0": {
                        "table": {
                            "name": "queqiao_mt_de_pred_bucket_${now.datekey}_{bucket_id}",
                            "namespace": "mt_platform"
                        }
                    },
                    "dataio_0": {
                        "with_label": True,
                        "label_name": label_name,
                        "label_type": "int",
                        "output_format": "dense"
                    }
                }
            },
            "host": {
                "0": {
                    "reader_0": {
                        "table": {
                            "name": "meituan_ss_predict_${now.datekey}_{bucket_id}",
                            "namespace": "meituan_ss_predict_sep"
                        }
                    },
                    "dataio_0": {
                        "with_label": False,
                        "output_format": "dense"
                    },
                    "evaluation_0": {
                        "need_run": False
                    }
                }
            }
        }
    }
}
predict_job_dsl = {
    "components": {
        "reader_0": {
            "module": "Reader",
            "output": {
                "data": [
                    "table"
                ]
            }
        },
        "dataio_0": {
            "module": "DataIO",
            "input": {
                "data": {
                    "data": [
                        "reader_0.table"
                    ]
                }
            },
            "output": {
                "data": [
                    "data"
                ],
                "model": [
                    "dataio"
                ]
            }
        },
        "intersection_0": {
            "module": "Intersection",
            "input": {
                "data": {
                    "data": [
                        "dataio_0.data"
                    ]
                }
            },
            "output": {
                "data": [
                    "data"
                ]
            }
        },
        "model_loader_0": {
            "module": "ModelLoader",
            "output": {
                "model": ["model"]
            }
        },
        "hetero_secure_boost_0": {
            "module": "HeteroSecureBoost",
            "input": {
                "model": [
                    "model_loader_0.model"
                ],
                "data": {
                    "test_data": [
                        "intersection_0.data"
                    ]
                }
            },
            "output": {
                "data": [
                    output_data
                ]
            }
        }
    }
}
psi_job_conf = {
    "dsl_version": "2",
    "initiator": {
        "role": "guest",
        "party_id": 20003
    },
    "role": {
        "guest": [
            20003
        ],
        "host": [
            20002
        ]
    },
    "job_parameters": {
        "common": {
            "task_parallelism": 1,
            "auto_retries": 0,
            "computing_partitions": 8,
            "task_cores": 9
        }
    },
    "component_parameters": {
        "common": {
            "intersection_0": {
                "intersect_method": "raw",
                "sync_intersect_ids": True,
                "join_role": "host"
            }
        },
        "role": {
            "guest": {
                "0": {
                    "reader_0": {
                        "table": {
                            "name": "queqiao_de_model_psi_sample_${now.datekey}",
                            "namespace": "mt_platform"
                        }
                    },
                    "data_transform_0": {
                        "with_label": False,
                        "output_format": "dense"
                    }
                }
            },
            "host": {
                "0": {
                    "reader_0": {
                        "table": {
                            "name": "meituan_ht_dn_for_psi_hash_bunket_{bucket_id}",
                            "namespace": "meituan_ss_predict"
                        }
                    },
                    "data_transform_0": {
                        "with_label": False,
                        "output_format": "dense"
                    }
                }
            }
        }
    }
}
psi_job_dsl = {
    "components": {
        "reader_0": {
            "module": "Reader",
            "output": {
                "data": [
                    "data"
                ]
            }
        },
        "data_transform_0": {
            "module": "DataTransform",
            "input": {
                "data": {
                    "data": [
                        "reader_0.data"
                    ]
                }
            },
            "output": {
                "data": [
                    "data"
                ],
                "model": [
                    "model"
                ]
            }
        },
        "intersection_0": {
            "module": "Intersection",
            "input": {
                "data": {
                    "data": [
                        "data_transform_0.data"
                    ]
                }
            },
            "output": {
                "data": [
                    "data"
                ]
            }
        }
    }
}

applys = [
    {
        'scenario': '助贷账务数据接入-通联光大',
        'describe': '光大SaaS账务数据',
        'project_id': 'loan',
        'source_dsn': 'loan_tljr_gdyh',
        'sink_dsn': 'mart_fspinno_queqiao',
        'tasks': [
            {"name": "loan_tljr_ccpaccount_account", "task_type_name": "ftp2mthive",
             "source_configs": {"file_path": "/one-sftp-xy-bank/queqiao/loan/gd/$now.datekey",
                                "file_name": "account.account_${now.datekey}.txt", "target_sep": "special",
                                "table_cols": "id,account_no,customer_no,product_no,org_no,currency,open_date,bill_day,due_day,auth_product_index_id,auth_person_index_id,status,frozen_status,account_balance,principal_balance,interest_balance,interest_penalty_balance,fee_balance,deserved_principal_amount,deserved_interest_amount,deserved_interest_penalty_amount,deserved_fee_amount,paid_principal_amount,paid_interest_amount,paid_interest_penalty_amount,paid_fee_amount,version,add_time,update_time",
                                "has_header": False, "ok_file": ".ok", "lastest_ready_time": "0020",
                                "ftp_polling_sec": 120, "compress_type": None, "compress_passwd": None,
                                "file_encode": None, "alarm_receivers": "jiangyuande", "max_waiting_hours": "1"},
             "sink_configs": {"table_name": "loan_tljr_ccpaccount_account", "target_sep": "special",
                              "table_type": "partition", "partition_key": None, "write_mode": "overwrite"}},
            {"name": "loan_tljr_ccpaccount_loan_bill", "task_type_name": "ftp2mthive",
             "source_configs": {"file_path": "/one-sftp-xy-bank/queqiao/loan/gd/$now.datekey",
                                "file_name": "account.loan_bill_${now.datekey}.txt", "target_sep": "special",
                                "table_cols": "id,union_loan_bill_id,is_union,account_id,customer_no,product_no,loan_no,ext_id,loan_type,rate_index_id,advance_repay_fee_rate,loan_total_principal,loan_periods,advance_repayable_period,repay_type,interval_type,total_terms,start_interest_day,open_date,expire_date,status,write_off_status,interest_bearing_status,settle_date,loan_balance,planed_principal,non_planed_principal,principal_balance,interest_balance,interest_penalty_balance,fee_balance,advance_repay_fee_balance,deserved_principal_amount,deserved_interest_amount,deserved_interest_penalty_amount,deserved_fee_amount,deserved_advance_repay_fee_amount,paid_principal_amount,paid_interest_amount,paid_interest_penalty_amount,paid_fee_amount,paid_advance_repay_fee_amount,freed_interest_amount,freed_fee_amount,overdue_level,overdue_days,version,add_time,update_time,creditor_code,creditor_proportion,creditors",
                                "has_header": False, "ok_file": ".ok", "lastest_ready_time": "0020",
                                "ftp_polling_sec": 120, "compress_type": None, "compress_passwd": None,
                                "file_encode": None, "alarm_receivers": "jiangyuande", "max_waiting_hours": "1"},
             "sink_configs": {"table_name": "loan_tljr_ccpaccount_loan_bill", "target_sep": "special",
                              "table_type": "partition", "partition_key": None, "write_mode": "overwrite"}},
            {"name": "loan_tljr_ccpaccount_loan_bill_after_batch", "task_type_name": "ftp2mthive",
             "source_configs": {"file_path": "/one-sftp-xy-bank/queqiao/loan/gd/$now.datekey",
                                "file_name": "account.loan_bill_after_batch_${now.datekey}.txt",
                                "target_sep": "special",
                                "table_cols": "id,union_loan_bill_id,is_union,account_id,customer_no,product_no,loan_no,ext_id,loan_type,rate_index_id,advance_repay_fee_rate,loan_total_principal,loan_periods,advance_repayable_period,repay_type,interval_type,total_terms,start_interest_day,open_date,expire_date,status,write_off_status,interest_bearing_status,settle_date,loan_balance,planed_principal,non_planed_principal,principal_balance,interest_balance,interest_penalty_balance,fee_balance,advance_repay_fee_balance,deserved_principal_amount,deserved_interest_amount,deserved_interest_penalty_amount,deserved_fee_amount,deserved_advance_repay_fee_amount,paid_principal_amount,paid_interest_amount,paid_interest_penalty_amount,paid_fee_amount,paid_advance_repay_fee_amount,freed_interest_amount,freed_fee_amount,overdue_level,overdue_days,version,add_time,update_time,creditor_code,creditor_proportion,creditors",
                                "has_header": False, "ok_file": ".ok", "lastest_ready_time": "0330",
                                "ftp_polling_sec": 120, "compress_type": None, "compress_passwd": None,
                                "file_encode": None, "alarm_receivers": "jiangyuande", "max_waiting_hours": "1"},
             "sink_configs": {"table_name": "loan_tljr_ccpaccount_loan_bill_after_batch", "target_sep": "special",
                              "table_type": "partition", "partition_key": None, "write_mode": "overwrite"}},
            {"name": "loan_tljr_ccpaccount_period_bill", "task_type_name": "ftp2mthive",
             "source_configs": {"file_path": "/one-sftp-xy-bank/queqiao/loan/gd/$now.datekey",
                                "file_name": "account.period_bill_${now.datekey}.txt", "target_sep": "special",
                                "table_cols": "id,union_period_bill_id,account_id,customer_no,product_no,loan_id,union_loan_bill_id,is_union,type,generate_type,start_interest_day,end_interest_day,status,period_plan_date,plan_repay_date,settle_date,overdue_level,overdue_days,period_balance,principal_balance,interest_balance,interest_penalty_balance,fee_balance,advance_repay_fee_balance,deserved_principal_amount,deserved_interest_amount,new_deserved_interest_amount,deserved_interest_penalty_amount,deserved_fee_amount,new_deserved_fee_amount,deserved_advance_repay_fee_amount,paid_principal_amount,paid_interest_amount,paid_interest_penalty_amount,paid_fee_amount,paid_advance_repay_fee_amount,freed_interest_amount,freed_fee_amount,plan_interest_amount,plan_fee_amount,coupon_can_free_interest_amount,coupon_can_free_fee_amount,add_time,update_time,version",
                                "has_header": False, "ok_file": ".ok", "lastest_ready_time": "0020",
                                "ftp_polling_sec": 120, "compress_type": None, "compress_passwd": None,
                                "file_encode": None, "alarm_receivers": "jiangyuande", "max_waiting_hours": "1"},
             "sink_configs": {"table_name": "loan_tljr_ccpaccount_period_bill", "target_sep": "special",
                              "table_type": "partition", "partition_key": None, "write_mode": "overwrite"}},
            {"name": "loan_tljr_ccpaccount_period_bill_after_batch", "task_type_name": "ftp2mthive",
             "source_configs": {"file_path": "/one-sftp-xy-bank/queqiao/loan/gd/$now.datekey",
                                "file_name": "account.period_bill_after_batch_${now.datekey}.txt",
                                "target_sep": "special",
                                "table_cols": "id,union_period_bill_id,account_id,customer_no,product_no,loan_id,union_loan_bill_id,is_union,type,generate_type,start_interest_day,end_interest_day,status,period_plan_date,plan_repay_date,settle_date,overdue_level,overdue_days,period_balance,principal_balance,interest_balance,interest_penalty_balance,fee_balance,advance_repay_fee_balance,deserved_principal_amount,deserved_interest_amount,new_deserved_interest_amount,deserved_interest_penalty_amount,deserved_fee_amount,new_deserved_fee_amount,deserved_advance_repay_fee_amount,paid_principal_amount,paid_interest_amount,paid_interest_penalty_amount,paid_fee_amount,paid_advance_repay_fee_amount,freed_interest_amount,freed_fee_amount,plan_interest_amount,plan_fee_amount,coupon_can_free_interest_amount,coupon_can_free_fee_amount,add_time,update_time,version",
                                "has_header": False, "ok_file": ".ok", "lastest_ready_time": "0330",
                                "ftp_polling_sec": 120, "compress_type": None, "compress_passwd": None,
                                "file_encode": None, "alarm_receivers": "jiangyuande", "max_waiting_hours": "1"},
             "sink_configs": {"table_name": "loan_tljr_ccpaccount_period_bill_after_batch", "target_sep": "special",
                              "table_type": "partition", "partition_key": None, "write_mode": "overwrite"}},
            {"name": "loan_tljr_ccpaccount_period_bill_status_trace", "task_type_name": "ftp2mthive",
             "source_configs": {"file_path": "/one-sftp-xy-bank/queqiao/loan/gd/$now.datekey",
                                "file_name": "account.period_bill_status_trace_${now.datekey}.txt",
                                "target_sep": "special",
                                "table_cols": "id,sys_ref_no,trade_time,trade_sys_date,customer_no,product_no,account_id,loan_id,period_id,event_code,before_status,after_status,business_ref_no,product_trade_code,creditor_code,add_time,update_time,is_union",
                                "has_header": False, "ok_file": ".ok", "lastest_ready_time": "0030",
                                "ftp_polling_sec": 120, "compress_type": None, "compress_passwd": None,
                                "file_encode": None, "alarm_receivers": "jiangyuande", "max_waiting_hours": "1"},
             "sink_configs": {"table_name": "loan_tljr_ccpaccount_period_bill_status_trace", "target_sep": "special",
                              "table_type": "partition", "partition_key": None, "write_mode": "overwrite"}},
            {"name": "loan_tljr_ccpaccount_account_trade", "task_type_name": "ftp2mthive",
             "source_configs": {"file_path": "/one-sftp-xy-bank/queqiao/loan/gd/$now.datekey",
                                "file_name": "account.account_trade_${now.datekey}.txt",
                                "target_sep": "special",
                                "table_cols": "id,sys_ref_no,sys_ref_seq,trade_time,trade_sys_date,account_id,loan_id,period_id,trade_code,debit_credit_flag,trade_description,trade_amount,currency,abs_no,org_no,product_no,customer_no,business_ref_no,orig_sys_ref_no,add_time,update_time,creditor_code,product_trade_code,is_union",
                                "has_header": False, "ok_file": ".ok", "lastest_ready_time": "0030",
                                "ftp_polling_sec": 120, "compress_type": None, "compress_passwd": None,
                                "file_encode": None, "alarm_receivers": "jiangyuande", "max_waiting_hours": "1"},
             "sink_configs": {"table_name": "loan_tljr_ccpaccount_account_trade", "target_sep": "special",
                              "table_type": "partition", "partition_key": None, "write_mode": "overwrite"}},
            {"name": "loan_tljr_ccpaccount_account_trade_after_batch", "task_type_name": "ftp2mthive",
             "source_configs": {"file_path": "/one-sftp-xy-bank/queqiao/loan/gd/$now.datekey",
                                "file_name": "account.account_trade_after_batch_${now.datekey}.txt",
                                "target_sep": "special",
                                "table_cols": "id,sys_ref_no,sys_ref_seq,trade_time,trade_sys_date,account_id,loan_id,period_id,trade_code,debit_credit_flag,trade_description,trade_amount,currency,abs_no,org_no,product_no,customer_no,business_ref_no,orig_sys_ref_no,add_time,update_time,creditor_code,product_trade_code,is_union",
                                "has_header": False, "ok_file": ".ok", "lastest_ready_time": "0330",
                                "ftp_polling_sec": 120, "compress_type": None, "compress_passwd": None,
                                "file_encode": None, "alarm_receivers": "jiangyuande", "max_waiting_hours": "1"},
             "sink_configs": {"table_name": "loan_tljr_ccpaccount_account_trade_after_batch", "target_sep": "special",
                              "table_type": "partition", "partition_key": None, "write_mode": "overwrite"}},
            {"name": "loan_tljr_ccpaccount_loan_bill_status_trace", "task_type_name": "ftp2mthive",
             "source_configs": {"file_path": "/one-sftp-xy-bank/queqiao/loan/gd/$now.datekey",
                                "file_name": "account.loan_bill_status_trace_${now.datekey}.txt",
                                "target_sep": "special",
                                "table_cols": "id,sys_ref_no,trade_time,trade_sys_date,customer_no,product_no,account_id,loan_id,event_code,before_status,after_status,business_ref_no,product_trade_code,creditor_code,add_time,update_time,is_union",
                                "has_header": False, "ok_file": ".ok", "lastest_ready_time": "0030",
                                "ftp_polling_sec": 120, "compress_type": None, "compress_passwd": None,
                                "file_encode": None, "alarm_receivers": "jiangyuande", "max_waiting_hours": "1"},
             "sink_configs": {"table_name": "loan_tljr_ccpaccount_loan_bill_status_trace", "target_sep": "special",
                              "table_type": "partition", "partition_key": None, "write_mode": "overwrite"}},
            {"name": "loan_tljr_ccpaccount_period_account_trade", "task_type_name": "ftp2mthive",
             "source_configs": {"file_path": "/one-sftp-xy-bank/queqiao/loan/gd/$now.datekey",
                                "file_name": "account.period_account_trade_${now.datekey}.txt",
                                "target_sep": "special",
                                "table_cols": "id,sys_ref_no,sys_ref_seq,trade_time,trade_sys_date,account_id,loan_id,period_id,trade_code,trade_description,trade_amount,currency,abs_no,org_no,product_no,customer_no,business_ref_no,orig_sys_ref_no,product_trade_code,add_time,update_time,is_union,creditor_code",
                                "has_header": False, "ok_file": ".ok", "lastest_ready_time": "0030",
                                "ftp_polling_sec": 120, "compress_type": None, "compress_passwd": None,
                                "file_encode": None, "alarm_receivers": "jiangyuande", "max_waiting_hours": "1"},
             "sink_configs": {"table_name": "loan_tljr_ccpaccount_period_account_trade", "target_sep": "special",
                              "table_type": "partition", "partition_key": None, "write_mode": "overwrite"}},
            {"name": "loan_tljr_ccpaccount_coupon", "task_type_name": "ftp2mthive",
             "source_configs": {"file_path": "/one-sftp-xy-bank/queqiao/loan/gd/$now.datekey",
                                "file_name": "account.coupon_${now.datekey}.txt", "target_sep": "special",
                                "table_cols": "id,account_id,customer_no,product_no,coupon_type,coupon_value,invalidate_scene,invalid_recall_period,effective_periods,can_free_interest_days,loan_bill_id,period_bill_id,is_used,can_free_interest_amount,freed_interest_amount,recall_freed_amount,freed_interest_days,discount_rate,period_discount_rates,max_amount_of_coupon_available,min_amount_of_coupon_available,top_write_off_principal,add_time,update_time",
                                "has_header": False, "ok_file": ".ok", "lastest_ready_time": "0020",
                                "ftp_polling_sec": 120, "compress_type": None, "compress_passwd": None,
                                "file_encode": None, "alarm_receivers": "jiangyuande", "max_waiting_hours": "1"},
             "sink_configs": {"table_name": "loan_tljr_ccpaccount_coupon", "target_sep": "special",
                              "table_type": "partition", "partition_key": None, "write_mode": "overwrite"}},
            {"name": "loan_tljr_ccpaccount_refund_record", "task_type_name": "ftp2mthive",
             "source_configs": {"file_path": "/one-sftp-xy-bank/queqiao/loan/gd/$now.datekey",
                                "file_name": "account.refund_record_${now.datekey}.txt",
                                "target_sep": "special",
                                "table_cols": "id,account_id,customer_no,product_no,business_ref_no,refund_amount,retreating_amount,add_time,update_time",
                                "has_header": False, "ok_file": ".ok", "lastest_ready_time": "0020",
                                "ftp_polling_sec": 120, "compress_type": None, "compress_passwd": None,
                                "file_encode": None, "alarm_receivers": "jiangyuande", "max_waiting_hours": "1"},
             "sink_configs": {"table_name": "loan_tljr_ccpaccount_refund_record", "target_sep": "special",
                              "table_type": "partition", "partition_key": None, "write_mode": "overwrite"}},
            {"name": "loan_tljr_ccpaccount_interest_control", "task_type_name": "ftp2mthive",
             "source_configs": {"file_path": "/one-sftp-xy-bank/queqiao/loan/gd/$now.datekey",
                                "file_name": "account.interest_control_${now.datekey}.txt",
                                "target_sep": "special",
                                "table_cols": "id,product_no,currency,control_index,org_no,compute_method,principal_compute_flag,interest_compute_flag,penalty_compute_flag,fee_compute_flag,rate,penalty_rate,add_time,update_time",
                                "has_header": False, "ok_file": ".ok", "lastest_ready_time": "0020",
                                "ftp_polling_sec": 120, "compress_type": None, "compress_passwd": None,
                                "file_encode": None, "alarm_receivers": "jiangyuande", "max_waiting_hours": "1"},
             "sink_configs": {"table_name": "loan_tljr_ccpaccount_interest_control", "target_sep": "special",
                              "table_type": "partition", "partition_key": None, "write_mode": "overwrite"}},
            {"name": "loan_tljr_ccpaccount_fund_loan_bill_status_trace", "task_type_name": "ftp2mthive",
             "source_configs": {"file_path": "/one-sftp-xy-bank/queqiao/loan/gd/$now.datekey",
                                "file_name": "account_fund.loan_bill_status_trace_${now.datekey}.txt",
                                "target_sep": "special",
                                "table_cols": "id,sys_ref_no,trade_time,trade_sys_date,customer_no,product_no,account_id,loan_id,event_code,before_status,after_status,business_ref_no,product_trade_code,creditor_code,add_time,update_time",
                                "has_header": False, "ok_file": ".ok", "lastest_ready_time": "0030",
                                "ftp_polling_sec": 120, "compress_type": None, "compress_passwd": None,
                                "file_encode": None, "alarm_receivers": "jiangyuande", "max_waiting_hours": "1"},
             "sink_configs": {"table_name": "loan_tljr_ccpaccount_fund_loan_bill_status_trace", "target_sep": "special",
                              "table_type": "partition", "partition_key": None, "write_mode": "overwrite"}},
            {"name": "loan_tljr_ccpaccount_fund_period_account_trade", "task_type_name": "ftp2mthive",
             "source_configs": {"file_path": "/one-sftp-xy-bank/queqiao/loan/gd/$now.datekey",
                                "file_name": "account_fund.period_account_trade_${now.datekey}.txt",
                                "target_sep": "special",
                                "table_cols": "id,sys_ref_no,sys_ref_seq,trade_time,trade_sys_date,account_id,loan_id,period_id,trade_code,trade_description,trade_amount,currency,org_no,product_no,customer_no,business_ref_no,orig_sys_ref_no,product_trade_code,add_time,update_time,creditor_code",
                                "has_header": False, "ok_file": ".ok", "lastest_ready_time": "0030",
                                "ftp_polling_sec": 120, "compress_type": None, "compress_passwd": None,
                                "file_encode": None, "alarm_receivers": "jiangyuande", "max_waiting_hours": "1"},
             "sink_configs": {"table_name": "loan_tljr_ccpaccount_fund_period_account_trade", "target_sep": "special",
                              "table_type": "partition", "partition_key": None, "write_mode": "overwrite"}},
            {"name": "loan_tljr_ccpaccount_fund_period_bill_status_trace", "task_type_name": "ftp2mthive",
             "source_configs": {"file_path": "/one-sftp-xy-bank/queqiao/loan/gd/$now.datekey",
                                "file_name": "account_fund.period_bill_status_trace_${now.datekey}.txt",
                                "target_sep": "special",
                                "table_cols": "id,sys_ref_no,trade_time,trade_sys_date,customer_no,product_no,account_id,loan_id,period_id,event_code,before_status,after_status,business_ref_no,product_trade_code,creditor_code,add_time,update_time",
                                "has_header": False, "ok_file": ".ok", "lastest_ready_time": "0030",
                                "ftp_polling_sec": 120, "compress_type": None, "compress_passwd": None,
                                "file_encode": None, "alarm_receivers": "jiangyuande", "max_waiting_hours": "1"},
             "sink_configs": {"table_name": "loan_tljr_ccpaccount_fund_period_bill_status_trace",
                              "target_sep": "special", "table_type": "partition", "partition_key": None,
                              "write_mode": "overwrite"}},
            {"name": "loan_tljr_ccpaccount_fund_loan_bill", "task_type_name": "ftp2mthive",
             "source_configs": {"file_path": "/one-sftp-xy-bank/queqiao/loan/gd/$now.datekey",
                                "file_name": "account_fund.loan_bill_${now.datekey}.txt",
                                "target_sep": "special",
                                "table_cols": "id,union_loan_bill_id,account_id,customer_no,product_no,loan_no,ext_id,loan_type,rate_index_id,loan_total_principal,loan_periods,repay_type,interval_type,total_terms,start_interest_day,open_date,expire_date,status,write_off_status,interest_bearing_status,settle_date,loan_balance,planed_principal,non_planed_principal,principal_balance,interest_balance,interest_penalty_balance,fee_balance,deserved_principal_amount,deserved_interest_amount,deserved_interest_penalty_amount,deserved_fee_amount,paid_principal_amount,paid_interest_amount,paid_interest_penalty_amount,paid_fee_amount,freed_interest_amount,freed_fee_amount,overdue_level,overdue_days,version,add_time,update_time,creditor_code,creditor_proportion",
                                "has_header": False, "ok_file": ".ok", "lastest_ready_time": "0020",
                                "ftp_polling_sec": 120, "compress_type": None, "compress_passwd": None,
                                "file_encode": None, "alarm_receivers": "jiangyuande", "max_waiting_hours": "1"},
             "sink_configs": {"table_name": "loan_tljr_ccpaccount_fund_loan_bill", "target_sep": "special",
                              "table_type": "partition", "partition_key": None, "write_mode": "overwrite"}},
            {"name": "loan_tljr_ccpaccount_fund_loan_bill_after_batch", "task_type_name": "ftp2mthive",
             "source_configs": {"file_path": "/one-sftp-xy-bank/queqiao/loan/gd/$now.datekey",
                                "file_name": "account_fund.loan_bill_after_batch_${now.datekey}.txt",
                                "target_sep": "special",
                                "table_cols": "id,union_loan_bill_id,account_id,customer_no,product_no,loan_no,ext_id,loan_type,rate_index_id,loan_total_principal,loan_periods,repay_type,interval_type,total_terms,start_interest_day,open_date,expire_date,status,write_off_status,interest_bearing_status,settle_date,loan_balance,planed_principal,non_planed_principal,principal_balance,interest_balance,interest_penalty_balance,fee_balance,deserved_principal_amount,deserved_interest_amount,deserved_interest_penalty_amount,deserved_fee_amount,paid_principal_amount,paid_interest_amount,paid_interest_penalty_amount,paid_fee_amount,freed_interest_amount,freed_fee_amount,overdue_level,overdue_days,version,add_time,update_time,creditor_code,creditor_proportion",
                                "has_header": False, "ok_file": ".ok", "lastest_ready_time": "0330",
                                "ftp_polling_sec": 120, "compress_type": None, "compress_passwd": None,
                                "file_encode": None, "alarm_receivers": "jiangyuande", "max_waiting_hours": "1"},
             "sink_configs": {"table_name": "loan_tljr_ccpaccount_fund_loan_bill_after_batch", "target_sep": "special",
                              "table_type": "partition", "partition_key": None, "write_mode": "overwrite"}},
            {"name": "loan_tljr_ccpaccount_fund_period_bill", "task_type_name": "ftp2mthive",
             "source_configs": {"file_path": "/one-sftp-xy-bank/queqiao/loan/gd/$now.datekey",
                                "file_name": "account_fund.period_bill_${now.datekey}.txt",
                                "target_sep": "special",
                                "table_cols": "id,union_period_bill_id,account_id,customer_no,product_no,loan_id,union_loan_bill_id,type,generate_type,start_interest_day,end_interest_day,status,period_plan_date,plan_repay_date,settle_date,overdue_level,overdue_days,period_balance,principal_balance,interest_balance,interest_penalty_balance,fee_balance,plan_interest_amount,plan_fee_amount,deserved_principal_amount,deserved_interest_amount,deserved_interest_penalty_amount,deserved_fee_amount,paid_principal_amount,paid_interest_amount,paid_interest_penalty_amount,paid_fee_amount,coupon_can_free_interest_amount,coupon_can_free_fee_amount,freed_interest_amount,freed_fee_amount,creditor_interest_limit,creditor_fee_limit,add_time,update_time,version",
                                "has_header": False, "ok_file": ".ok", "lastest_ready_time": "0020",
                                "ftp_polling_sec": 120, "compress_type": None, "compress_passwd": None,
                                "file_encode": None, "alarm_receivers": "jiangyuande", "max_waiting_hours": "1"},
             "sink_configs": {"table_name": "loan_tljr_ccpaccount_fund_period_bill", "target_sep": "special",
                              "table_type": "partition", "partition_key": None, "write_mode": "overwrite"}},
            {"name": "loan_tljr_ccpaccount_fund_period_bill_after_batch", "task_type_name": "ftp2mthive",
             "source_configs": {"file_path": "/one-sftp-xy-bank/queqiao/loan/gd/$now.datekey",
                                "file_name": "account_fund.period_bill_after_batch_${now.datekey}.txt",
                                "target_sep": "special",
                                "table_cols": "id,union_period_bill_id,account_id,customer_no,product_no,loan_id,union_loan_bill_id,type,generate_type,start_interest_day,end_interest_day,status,period_plan_date,plan_repay_date,settle_date,overdue_level,overdue_days,period_balance,principal_balance,interest_balance,interest_penalty_balance,fee_balance,plan_interest_amount,plan_fee_amount,deserved_principal_amount,deserved_interest_amount,deserved_interest_penalty_amount,deserved_fee_amount,paid_principal_amount,paid_interest_amount,paid_interest_penalty_amount,paid_fee_amount,coupon_can_free_interest_amount,coupon_can_free_fee_amount,freed_interest_amount,freed_fee_amount,creditor_interest_limit,creditor_fee_limit,add_time,update_time,version",
                                "has_header": False, "ok_file": ".ok", "lastest_ready_time": "0330",
                                "ftp_polling_sec": 120, "compress_type": None, "compress_passwd": None,
                                "file_encode": None, "alarm_receivers": "jiangyuande", "max_waiting_hours": "1"},
             "sink_configs": {"table_name": "loan_tljr_ccpaccount_fund_period_bill_after_batch",
                              "target_sep": "special", "table_type": "partition", "partition_key": None,
                              "write_mode": "overwrite"}},
            {"name": "loan_tljr_ccpaccount_fund_account_trade", "task_type_name": "ftp2mthive",
             "source_configs": {"file_path": "/one-sftp-xy-bank/queqiao/loan/gd/$now.datekey",
                                "file_name": "account_fund.account_trade_${now.datekey}.txt",
                                "target_sep": "special",
                                "table_cols": "id,sys_ref_no,sys_ref_seq,trade_time,trade_sys_date,account_id,loan_id,period_id,trade_code,trade_description,trade_amount,currency,org_no,product_no,customer_no,business_ref_no,orig_sys_ref_no,add_time,update_time,creditor_code,product_trade_code",
                                "has_header": False, "ok_file": ".ok", "lastest_ready_time": "0030",
                                "ftp_polling_sec": 120, "compress_type": None, "compress_passwd": None,
                                "file_encode": None, "alarm_receivers": "jiangyuande", "max_waiting_hours": "1"},
             "sink_configs": {"table_name": "loan_tljr_ccpaccount_fund_account_trade", "target_sep": "special",
                              "table_type": "partition", "partition_key": None, "write_mode": "overwrite"}},
            {"name": "loan_tljr_ccpaccount_fund_account_trade_after_batch", "task_type_name": "ftp2mthive",
             "source_configs": {"file_path": "/one-sftp-xy-bank/queqiao/loan/gd/$now.datekey",
                                "file_name": "account_fund.account_trade_after_batch_${now.datekey}.txt",
                                "target_sep": "special",
                                "table_cols": "id,sys_ref_no,sys_ref_seq,trade_time,trade_sys_date,account_id,loan_id,period_id,trade_code,trade_description,trade_amount,currency,org_no,product_no,customer_no,business_ref_no,orig_sys_ref_no,add_time,update_time,creditor_code,product_trade_code",
                                "has_header": False, "ok_file": ".ok", "lastest_ready_time": "0330",
                                "ftp_polling_sec": 120, "compress_type": None, "compress_passwd": None,
                                "file_encode": None, "alarm_receivers": "jiangyuande", "max_waiting_hours": "1"},
             "sink_configs": {"table_name": "loan_tljr_ccpaccount_fund_account_trade_after_batch",
                              "target_sep": "special", "table_type": "partition", "partition_key": None,
                              "write_mode": "overwrite"}},

        ]
    },
    {
        'scenario': '助贷账务数据接入-银数江苏',
        'describe': '银数江苏小额账务数据',
        'project_id': 'loan',
        'source_dsn': 'loan_ylsj_jsyh',
        'sink_dsn': 'mart_fspinno_queqiao',
        'tasks': [
            {"name": "loan_ylsj_ccpaccount_account", "task_type_name": "ftp2mthive",
             "source_configs": {"file_path": "/one-sftp-xy-bank/queqiao/loan/ylsj_jsyh/$now.datekey",
                                "file_name": "account.account_${now.datekey}.txt", "target_sep": "special",
                                "table_cols": "id,account_no,customer_no,product_no,org_no,currency,open_date,bill_day,due_day,auth_product_index_id,auth_person_index_id,status,frozen_status,account_balance,principal_balance,interest_balance,interest_penalty_balance,fee_balance,deserved_principal_amount,deserved_interest_amount,deserved_interest_penalty_amount,deserved_fee_amount,paid_principal_amount,paid_interest_amount,paid_interest_penalty_amount,paid_fee_amount,version,add_time,update_time",
                                "has_header": False, "ok_file": ".ok", "lastest_ready_time": "0020",
                                "ftp_polling_sec": 120, "compress_type": None, "compress_passwd": None,
                                "file_encode": None, "alarm_receivers": "jiangyuande", "max_waiting_hours": "1"},
             "sink_configs": {"table_name": "loan_ylsj_ccpaccount_account", "target_sep": "special",
                              "table_type": "partition", "partition_key": None, "write_mode": "overwrite"}},
            {"name": "loan_ylsj_ccpaccount_loan_bill", "task_type_name": "ftp2mthive",
             "source_configs": {"file_path": "/one-sftp-xy-bank/queqiao/loan/ylsj_jsyh/$now.datekey",
                                "file_name": "account.loan_bill_${now.datekey}.txt", "target_sep": "special",
                                "table_cols": "id,union_loan_bill_id,is_union,account_id,customer_no,product_no,loan_no,ext_id,loan_type,rate_index_id,advance_repay_fee_rate,loan_total_principal,loan_periods,advance_repayable_period,repay_type,interval_type,total_terms,start_interest_day,open_date,expire_date,status,write_off_status,interest_bearing_status,settle_date,loan_balance,planed_principal,non_planed_principal,principal_balance,interest_balance,interest_penalty_balance,fee_balance,advance_repay_fee_balance,deserved_principal_amount,deserved_interest_amount,deserved_interest_penalty_amount,deserved_fee_amount,deserved_advance_repay_fee_amount,paid_principal_amount,paid_interest_amount,paid_interest_penalty_amount,paid_fee_amount,paid_advance_repay_fee_amount,freed_interest_amount,freed_fee_amount,overdue_level,overdue_days,version,add_time,update_time,creditor_code,creditor_proportion,creditors",
                                "has_header": False, "ok_file": ".ok", "lastest_ready_time": "0020",
                                "ftp_polling_sec": 120, "compress_type": None, "compress_passwd": None,
                                "file_encode": None, "alarm_receivers": "jiangyuande", "max_waiting_hours": "1"},
             "sink_configs": {"table_name": "loan_ylsj_ccpaccount_loan_bill", "target_sep": "special",
                              "table_type": "partition", "partition_key": None, "write_mode": "overwrite"}},
            {"name": "loan_ylsj_ccpaccount_loan_bill_after_batch", "task_type_name": "ftp2mthive",
             "source_configs": {"file_path": "/one-sftp-xy-bank/queqiao/loan/ylsj_jsyh/$now.datekey",
                                "file_name": "account.loan_bill_after_batch_${now.datekey}.txt",
                                "target_sep": "special",
                                "table_cols": "id,union_loan_bill_id,is_union,account_id,customer_no,product_no,loan_no,ext_id,loan_type,rate_index_id,advance_repay_fee_rate,loan_total_principal,loan_periods,advance_repayable_period,repay_type,interval_type,total_terms,start_interest_day,open_date,expire_date,status,write_off_status,interest_bearing_status,settle_date,loan_balance,planed_principal,non_planed_principal,principal_balance,interest_balance,interest_penalty_balance,fee_balance,advance_repay_fee_balance,deserved_principal_amount,deserved_interest_amount,deserved_interest_penalty_amount,deserved_fee_amount,deserved_advance_repay_fee_amount,paid_principal_amount,paid_interest_amount,paid_interest_penalty_amount,paid_fee_amount,paid_advance_repay_fee_amount,freed_interest_amount,freed_fee_amount,overdue_level,overdue_days,version,add_time,update_time,creditor_code,creditor_proportion,creditors",
                                "has_header": False, "ok_file": ".ok", "lastest_ready_time": "0330",
                                "ftp_polling_sec": 120, "compress_type": None, "compress_passwd": None,
                                "file_encode": None, "alarm_receivers": "jiangyuande", "max_waiting_hours": "1"},
             "sink_configs": {"table_name": "loan_ylsj_ccpaccount_loan_bill_after_batch", "target_sep": "special",
                              "table_type": "partition", "partition_key": None, "write_mode": "overwrite"}},
            {"name": "loan_ylsj_ccpaccount_period_bill", "task_type_name": "ftp2mthive",
             "source_configs": {"file_path": "/one-sftp-xy-bank/queqiao/loan/ylsj_jsyh/$now.datekey",
                                "file_name": "account.period_bill_${now.datekey}.txt", "target_sep": "special",
                                "table_cols": "id,union_period_bill_id,account_id,customer_no,product_no,loan_id,union_loan_bill_id,is_union,type,generate_type,start_interest_day,end_interest_day,status,period_plan_date,plan_repay_date,settle_date,overdue_level,overdue_days,period_balance,principal_balance,interest_balance,interest_penalty_balance,fee_balance,advance_repay_fee_balance,deserved_principal_amount,deserved_interest_amount,new_deserved_interest_amount,deserved_interest_penalty_amount,deserved_fee_amount,new_deserved_fee_amount,deserved_advance_repay_fee_amount,paid_principal_amount,paid_interest_amount,paid_interest_penalty_amount,paid_fee_amount,paid_advance_repay_fee_amount,freed_interest_amount,freed_fee_amount,plan_interest_amount,plan_fee_amount,coupon_can_free_interest_amount,coupon_can_free_fee_amount,add_time,update_time,version",
                                "has_header": False, "ok_file": ".ok", "lastest_ready_time": "0020",
                                "ftp_polling_sec": 120, "compress_type": None, "compress_passwd": None,
                                "file_encode": None, "alarm_receivers": "jiangyuande", "max_waiting_hours": "1"},
             "sink_configs": {"table_name": "loan_ylsj_ccpaccount_period_bill", "target_sep": "special",
                              "table_type": "partition", "partition_key": None, "write_mode": "overwrite"}},
            {"name": "loan_ylsj_ccpaccount_period_bill_after_batch", "task_type_name": "ftp2mthive",
             "source_configs": {"file_path": "/one-sftp-xy-bank/queqiao/loan/ylsj_jsyh/$now.datekey",
                                "file_name": "account.period_bill_after_batch_${now.datekey}.txt",
                                "target_sep": "special",
                                "table_cols": "id,union_period_bill_id,account_id,customer_no,product_no,loan_id,union_loan_bill_id,is_union,type,generate_type,start_interest_day,end_interest_day,status,period_plan_date,plan_repay_date,settle_date,overdue_level,overdue_days,period_balance,principal_balance,interest_balance,interest_penalty_balance,fee_balance,advance_repay_fee_balance,deserved_principal_amount,deserved_interest_amount,new_deserved_interest_amount,deserved_interest_penalty_amount,deserved_fee_amount,new_deserved_fee_amount,deserved_advance_repay_fee_amount,paid_principal_amount,paid_interest_amount,paid_interest_penalty_amount,paid_fee_amount,paid_advance_repay_fee_amount,freed_interest_amount,freed_fee_amount,plan_interest_amount,plan_fee_amount,coupon_can_free_interest_amount,coupon_can_free_fee_amount,add_time,update_time,version",
                                "has_header": False, "ok_file": ".ok", "lastest_ready_time": "0330",
                                "ftp_polling_sec": 120, "compress_type": None, "compress_passwd": None,
                                "file_encode": None, "alarm_receivers": "jiangyuande", "max_waiting_hours": "1"},
             "sink_configs": {"table_name": "loan_ylsj_ccpaccount_period_bill_after_batch", "target_sep": "special",
                              "table_type": "partition", "partition_key": None, "write_mode": "overwrite"}},
            {"name": "loan_ylsj_ccpaccount_period_bill_status_trace", "task_type_name": "ftp2mthive",
             "source_configs": {"file_path": "/one-sftp-xy-bank/queqiao/loan/ylsj_jsyh/$now.datekey",
                                "file_name": "account.period_bill_status_trace_${now.datekey}.txt",
                                "target_sep": "special",
                                "table_cols": "id,sys_ref_no,trade_time,trade_sys_date,customer_no,product_no,account_id,loan_id,period_id,event_code,before_status,after_status,business_ref_no,product_trade_code,creditor_code,add_time,update_time,is_union",
                                "has_header": False, "ok_file": ".ok", "lastest_ready_time": "0030",
                                "ftp_polling_sec": 120, "compress_type": None, "compress_passwd": None,
                                "file_encode": None, "alarm_receivers": "jiangyuande", "max_waiting_hours": "1"},
             "sink_configs": {"table_name": "loan_ylsj_ccpaccount_period_bill_status_trace", "target_sep": "special",
                              "table_type": "partition", "partition_key": None, "write_mode": "overwrite"}},
            {"name": "loan_ylsj_ccpaccount_account_trade", "task_type_name": "ftp2mthive",
             "source_configs": {"file_path": "/one-sftp-xy-bank/queqiao/loan/ylsj_jsyh/$now.datekey",
                                "file_name": "account.account_trade_${now.datekey}.txt",
                                "target_sep": "special",
                                "table_cols": "id,sys_ref_no,sys_ref_seq,trade_time,trade_sys_date,account_id,loan_id,period_id,trade_code,debit_credit_flag,trade_description,trade_amount,currency,abs_no,org_no,product_no,customer_no,business_ref_no,orig_sys_ref_no,add_time,update_time,creditor_code,product_trade_code,is_union",
                                "has_header": False, "ok_file": ".ok", "lastest_ready_time": "0030",
                                "ftp_polling_sec": 120, "compress_type": None, "compress_passwd": None,
                                "file_encode": None, "alarm_receivers": "jiangyuande", "max_waiting_hours": "1"},
             "sink_configs": {"table_name": "loan_ylsj_ccpaccount_account_trade", "target_sep": "special",
                              "table_type": "partition", "partition_key": None, "write_mode": "overwrite"}},
            {"name": "loan_ylsj_ccpaccount_account_trade_after_batch", "task_type_name": "ftp2mthive",
             "source_configs": {"file_path": "/one-sftp-xy-bank/queqiao/loan/ylsj_jsyh/$now.datekey",
                                "file_name": "account.account_trade_after_batch_${now.datekey}.txt",
                                "target_sep": "special",
                                "table_cols": "id,sys_ref_no,sys_ref_seq,trade_time,trade_sys_date,account_id,loan_id,period_id,trade_code,debit_credit_flag,trade_description,trade_amount,currency,abs_no,org_no,product_no,customer_no,business_ref_no,orig_sys_ref_no,add_time,update_time,creditor_code,product_trade_code,is_union",
                                "has_header": False, "ok_file": ".ok", "lastest_ready_time": "0330",
                                "ftp_polling_sec": 120, "compress_type": None, "compress_passwd": None,
                                "file_encode": None, "alarm_receivers": "jiangyuande", "max_waiting_hours": "1"},
             "sink_configs": {"table_name": "loan_ylsj_ccpaccount_account_trade_after_batch", "target_sep": "special",
                              "table_type": "partition", "partition_key": None, "write_mode": "overwrite"}},
            {"name": "loan_ylsj_ccpaccount_loan_bill_status_trace", "task_type_name": "ftp2mthive",
             "source_configs": {"file_path": "/one-sftp-xy-bank/queqiao/loan/ylsj_jsyh/$now.datekey",
                                "file_name": "account.loan_bill_status_trace_${now.datekey}.txt",
                                "target_sep": "special",
                                "table_cols": "id,sys_ref_no,trade_time,trade_sys_date,customer_no,product_no,account_id,loan_id,event_code,before_status,after_status,business_ref_no,product_trade_code,creditor_code,add_time,update_time,is_union",
                                "has_header": False, "ok_file": ".ok", "lastest_ready_time": "0030",
                                "ftp_polling_sec": 120, "compress_type": None, "compress_passwd": None,
                                "file_encode": None, "alarm_receivers": "jiangyuande", "max_waiting_hours": "1"},
             "sink_configs": {"table_name": "loan_ylsj_ccpaccount_loan_bill_status_trace", "target_sep": "special",
                              "table_type": "partition", "partition_key": None, "write_mode": "overwrite"}},
            {"name": "loan_ylsj_ccpaccount_period_account_trade", "task_type_name": "ftp2mthive",
             "source_configs": {"file_path": "/one-sftp-xy-bank/queqiao/loan/ylsj_jsyh/$now.datekey",
                                "file_name": "account.period_account_trade_${now.datekey}.txt",
                                "target_sep": "special",
                                "table_cols": "id,sys_ref_no,sys_ref_seq,trade_time,trade_sys_date,account_id,loan_id,period_id,trade_code,trade_description,trade_amount,currency,abs_no,org_no,product_no,customer_no,business_ref_no,orig_sys_ref_no,product_trade_code,add_time,update_time,is_union,creditor_code",
                                "has_header": False, "ok_file": ".ok", "lastest_ready_time": "0030",
                                "ftp_polling_sec": 120, "compress_type": None, "compress_passwd": None,
                                "file_encode": None, "alarm_receivers": "jiangyuande", "max_waiting_hours": "1"},
             "sink_configs": {"table_name": "loan_ylsj_ccpaccount_period_account_trade", "target_sep": "special",
                              "table_type": "partition", "partition_key": None, "write_mode": "overwrite"}},
            {"name": "loan_ylsj_ccpaccount_coupon", "task_type_name": "ftp2mthive",
             "source_configs": {"file_path": "/one-sftp-xy-bank/queqiao/loan/ylsj_jsyh/$now.datekey",
                                "file_name": "account.coupon_${now.datekey}.txt", "target_sep": "special",
                                "table_cols": "id,account_id,customer_no,product_no,coupon_type,coupon_value,invalidate_scene,invalid_recall_period,effective_periods,can_free_interest_days,loan_bill_id,period_bill_id,is_used,can_free_interest_amount,freed_interest_amount,recall_freed_amount,freed_interest_days,discount_rate,period_discount_rates,max_amount_of_coupon_available,min_amount_of_coupon_available,top_write_off_principal,add_time,update_time",
                                "has_header": False, "ok_file": ".ok", "lastest_ready_time": "0020",
                                "ftp_polling_sec": 120, "compress_type": None, "compress_passwd": None,
                                "file_encode": None, "alarm_receivers": "jiangyuande", "max_waiting_hours": "1"},
             "sink_configs": {"table_name": "loan_ylsj_ccpaccount_coupon", "target_sep": "special",
                              "table_type": "partition", "partition_key": None, "write_mode": "overwrite"}},
            {"name": "loan_ylsj_ccpaccount_refund_record", "task_type_name": "ftp2mthive",
             "source_configs": {"file_path": "/one-sftp-xy-bank/queqiao/loan/ylsj_jsyh/$now.datekey",
                                "file_name": "account.refund_record_${now.datekey}.txt",
                                "target_sep": "special",
                                "table_cols": "id,account_id,customer_no,product_no,business_ref_no,refund_amount,retreating_amount,add_time,update_time",
                                "has_header": False, "ok_file": ".ok", "lastest_ready_time": "0020",
                                "ftp_polling_sec": 120, "compress_type": None, "compress_passwd": None,
                                "file_encode": None, "alarm_receivers": "jiangyuande", "max_waiting_hours": "1"},
             "sink_configs": {"table_name": "loan_ylsj_ccpaccount_refund_record", "target_sep": "special",
                              "table_type": "partition", "partition_key": None, "write_mode": "overwrite"}},
            {"name": "loan_ylsj_ccpaccount_interest_control", "task_type_name": "ftp2mthive",
             "source_configs": {"file_path": "/one-sftp-xy-bank/queqiao/loan/ylsj_jsyh/$now.datekey",
                                "file_name": "account.interest_control_${now.datekey}.txt",
                                "target_sep": "special",
                                "table_cols": "id,product_no,currency,control_index,org_no,compute_method,principal_compute_flag,interest_compute_flag,penalty_compute_flag,fee_compute_flag,rate,penalty_rate,add_time,update_time",
                                "has_header": False, "ok_file": ".ok", "lastest_ready_time": "0020",
                                "ftp_polling_sec": 120, "compress_type": None, "compress_passwd": None,
                                "file_encode": None, "alarm_receivers": "jiangyuande", "max_waiting_hours": "1"},
             "sink_configs": {"table_name": "loan_ylsj_ccpaccount_interest_control", "target_sep": "special",
                              "table_type": "partition", "partition_key": None, "write_mode": "overwrite"}},
            {"name": "loan_ylsj_ccpaccount_fund_loan_bill_status_trace", "task_type_name": "ftp2mthive",
             "source_configs": {"file_path": "/one-sftp-xy-bank/queqiao/loan/ylsj_jsyh/$now.datekey",
                                "file_name": "account_fund.loan_bill_status_trace_${now.datekey}.txt",
                                "target_sep": "special",
                                "table_cols": "id,sys_ref_no,trade_time,trade_sys_date,customer_no,product_no,account_id,loan_id,event_code,before_status,after_status,business_ref_no,product_trade_code,creditor_code,add_time,update_time",
                                "has_header": False, "ok_file": ".ok", "lastest_ready_time": "0030",
                                "ftp_polling_sec": 120, "compress_type": None, "compress_passwd": None,
                                "file_encode": None, "alarm_receivers": "jiangyuande", "max_waiting_hours": "1"},
             "sink_configs": {"table_name": "loan_ylsj_ccpaccount_fund_loan_bill_status_trace", "target_sep": "special",
                              "table_type": "partition", "partition_key": None, "write_mode": "overwrite"}},
            {"name": "loan_ylsj_ccpaccount_fund_period_account_trade", "task_type_name": "ftp2mthive",
             "source_configs": {"file_path": "/one-sftp-xy-bank/queqiao/loan/ylsj_jsyh/$now.datekey",
                                "file_name": "account_fund.period_account_trade_${now.datekey}.txt",
                                "target_sep": "special",
                                "table_cols": "id,sys_ref_no,sys_ref_seq,trade_time,trade_sys_date,account_id,loan_id,period_id,trade_code,trade_description,trade_amount,currency,org_no,product_no,customer_no,business_ref_no,orig_sys_ref_no,product_trade_code,add_time,update_time,creditor_code",
                                "has_header": False, "ok_file": ".ok", "lastest_ready_time": "0030",
                                "ftp_polling_sec": 120, "compress_type": None, "compress_passwd": None,
                                "file_encode": None, "alarm_receivers": "jiangyuande", "max_waiting_hours": "1"},
             "sink_configs": {"table_name": "loan_ylsj_ccpaccount_fund_period_account_trade", "target_sep": "special",
                              "table_type": "partition", "partition_key": None, "write_mode": "overwrite"}},
            {"name": "loan_ylsj_ccpaccount_fund_period_bill_status_trace", "task_type_name": "ftp2mthive",
             "source_configs": {"file_path": "/one-sftp-xy-bank/queqiao/loan/ylsj_jsyh/$now.datekey",
                                "file_name": "account_fund.period_bill_status_trace_${now.datekey}.txt",
                                "target_sep": "special",
                                "table_cols": "id,sys_ref_no,trade_time,trade_sys_date,customer_no,product_no,account_id,loan_id,period_id,event_code,before_status,after_status,business_ref_no,product_trade_code,creditor_code,add_time,update_time",
                                "has_header": False, "ok_file": ".ok", "lastest_ready_time": "0030",
                                "ftp_polling_sec": 120, "compress_type": None, "compress_passwd": None,
                                "file_encode": None, "alarm_receivers": "jiangyuande", "max_waiting_hours": "1"},
             "sink_configs": {"table_name": "loan_ylsj_ccpaccount_fund_period_bill_status_trace",
                              "target_sep": "special", "table_type": "partition", "partition_key": None,
                              "write_mode": "overwrite"}},
            {"name": "loan_ylsj_ccpaccount_fund_loan_bill", "task_type_name": "ftp2mthive",
             "source_configs": {"file_path": "/one-sftp-xy-bank/queqiao/loan/ylsj_jsyh/$now.datekey",
                                "file_name": "account_fund.loan_bill_${now.datekey}.txt",
                                "target_sep": "special",
                                "table_cols": "id,union_loan_bill_id,account_id,customer_no,product_no,loan_no,ext_id,loan_type,rate_index_id,loan_total_principal,loan_periods,repay_type,interval_type,total_terms,start_interest_day,open_date,expire_date,status,write_off_status,interest_bearing_status,settle_date,loan_balance,planed_principal,non_planed_principal,principal_balance,interest_balance,interest_penalty_balance,fee_balance,deserved_principal_amount,deserved_interest_amount,deserved_interest_penalty_amount,deserved_fee_amount,paid_principal_amount,paid_interest_amount,paid_interest_penalty_amount,paid_fee_amount,freed_interest_amount,freed_fee_amount,overdue_level,overdue_days,version,add_time,update_time,creditor_code,creditor_proportion",
                                "has_header": False, "ok_file": ".ok", "lastest_ready_time": "0020",
                                "ftp_polling_sec": 120, "compress_type": None, "compress_passwd": None,
                                "file_encode": None, "alarm_receivers": "jiangyuande", "max_waiting_hours": "1"},
             "sink_configs": {"table_name": "loan_ylsj_ccpaccount_fund_loan_bill", "target_sep": "special",
                              "table_type": "partition", "partition_key": None, "write_mode": "overwrite"}},
            {"name": "loan_ylsj_ccpaccount_fund_loan_bill_after_batch", "task_type_name": "ftp2mthive",
             "source_configs": {"file_path": "/one-sftp-xy-bank/queqiao/loan/ylsj_jsyh/$now.datekey",
                                "file_name": "account_fund.loan_bill_after_batch_${now.datekey}.txt",
                                "target_sep": "special",
                                "table_cols": "id,union_loan_bill_id,account_id,customer_no,product_no,loan_no,ext_id,loan_type,rate_index_id,loan_total_principal,loan_periods,repay_type,interval_type,total_terms,start_interest_day,open_date,expire_date,status,write_off_status,interest_bearing_status,settle_date,loan_balance,planed_principal,non_planed_principal,principal_balance,interest_balance,interest_penalty_balance,fee_balance,deserved_principal_amount,deserved_interest_amount,deserved_interest_penalty_amount,deserved_fee_amount,paid_principal_amount,paid_interest_amount,paid_interest_penalty_amount,paid_fee_amount,freed_interest_amount,freed_fee_amount,overdue_level,overdue_days,version,add_time,update_time,creditor_code,creditor_proportion",
                                "has_header": False, "ok_file": ".ok", "lastest_ready_time": "0330",
                                "ftp_polling_sec": 120, "compress_type": None, "compress_passwd": None,
                                "file_encode": None, "alarm_receivers": "jiangyuande", "max_waiting_hours": "1"},
             "sink_configs": {"table_name": "loan_ylsj_ccpaccount_fund_loan_bill_after_batch", "target_sep": "special",
                              "table_type": "partition", "partition_key": None, "write_mode": "overwrite"}},
            {"name": "loan_ylsj_ccpaccount_fund_period_bill", "task_type_name": "ftp2mthive",
             "source_configs": {"file_path": "/one-sftp-xy-bank/queqiao/loan/ylsj_jsyh/$now.datekey",
                                "file_name": "account_fund.period_bill_${now.datekey}.txt",
                                "target_sep": "special",
                                "table_cols": "id,union_period_bill_id,account_id,customer_no,product_no,loan_id,union_loan_bill_id,type,generate_type,start_interest_day,end_interest_day,status,period_plan_date,plan_repay_date,settle_date,overdue_level,overdue_days,period_balance,principal_balance,interest_balance,interest_penalty_balance,fee_balance,plan_interest_amount,plan_fee_amount,deserved_principal_amount,deserved_interest_amount,deserved_interest_penalty_amount,deserved_fee_amount,paid_principal_amount,paid_interest_amount,paid_interest_penalty_amount,paid_fee_amount,coupon_can_free_interest_amount,coupon_can_free_fee_amount,freed_interest_amount,freed_fee_amount,creditor_interest_limit,creditor_fee_limit,add_time,update_time,version",
                                "has_header": False, "ok_file": ".ok", "lastest_ready_time": "0020",
                                "ftp_polling_sec": 120, "compress_type": None, "compress_passwd": None,
                                "file_encode": None, "alarm_receivers": "jiangyuande", "max_waiting_hours": "1"},
             "sink_configs": {"table_name": "loan_ylsj_ccpaccount_fund_period_bill", "target_sep": "special",
                              "table_type": "partition", "partition_key": None, "write_mode": "overwrite"}},
            {"name": "loan_ylsj_ccpaccount_fund_period_bill_after_batch", "task_type_name": "ftp2mthive",
             "source_configs": {"file_path": "/one-sftp-xy-bank/queqiao/loan/ylsj_jsyh/$now.datekey",
                                "file_name": "account_fund.period_bill_after_batch_${now.datekey}.txt",
                                "target_sep": "special",
                                "table_cols": "id,union_period_bill_id,account_id,customer_no,product_no,loan_id,union_loan_bill_id,type,generate_type,start_interest_day,end_interest_day,status,period_plan_date,plan_repay_date,settle_date,overdue_level,overdue_days,period_balance,principal_balance,interest_balance,interest_penalty_balance,fee_balance,plan_interest_amount,plan_fee_amount,deserved_principal_amount,deserved_interest_amount,deserved_interest_penalty_amount,deserved_fee_amount,paid_principal_amount,paid_interest_amount,paid_interest_penalty_amount,paid_fee_amount,coupon_can_free_interest_amount,coupon_can_free_fee_amount,freed_interest_amount,freed_fee_amount,creditor_interest_limit,creditor_fee_limit,add_time,update_time,version",
                                "has_header": False, "ok_file": ".ok", "lastest_ready_time": "0330",
                                "ftp_polling_sec": 120, "compress_type": None, "compress_passwd": None,
                                "file_encode": None, "alarm_receivers": "jiangyuande", "max_waiting_hours": "1"},
             "sink_configs": {"table_name": "loan_ylsj_ccpaccount_fund_period_bill_after_batch",
                              "target_sep": "special", "table_type": "partition", "partition_key": None,
                              "write_mode": "overwrite"}},
            {"name": "loan_ylsj_ccpaccount_fund_account_trade", "task_type_name": "ftp2mthive",
             "source_configs": {"file_path": "/one-sftp-xy-bank/queqiao/loan/ylsj_jsyh/$now.datekey",
                                "file_name": "account_fund.account_trade_${now.datekey}.txt",
                                "target_sep": "special",
                                "table_cols": "id,sys_ref_no,sys_ref_seq,trade_time,trade_sys_date,account_id,loan_id,period_id,trade_code,trade_description,trade_amount,currency,org_no,product_no,customer_no,business_ref_no,orig_sys_ref_no,add_time,update_time,creditor_code,product_trade_code",
                                "has_header": False, "ok_file": ".ok", "lastest_ready_time": "0030",
                                "ftp_polling_sec": 120, "compress_type": None, "compress_passwd": None,
                                "file_encode": None, "alarm_receivers": "jiangyuande", "max_waiting_hours": "1"},
             "sink_configs": {"table_name": "loan_ylsj_ccpaccount_fund_account_trade", "target_sep": "special",
                              "table_type": "partition", "partition_key": None, "write_mode": "overwrite"}},
            {"name": "loan_ylsj_ccpaccount_fund_account_trade_after_batch", "task_type_name": "ftp2mthive",
             "source_configs": {"file_path": "/one-sftp-xy-bank/queqiao/loan/ylsj_jsyh/$now.datekey",
                                "file_name": "account_fund.account_trade_after_batch_${now.datekey}.txt",
                                "target_sep": "special",
                                "table_cols": "id,sys_ref_no,sys_ref_seq,trade_time,trade_sys_date,account_id,loan_id,period_id,trade_code,trade_description,trade_amount,currency,org_no,product_no,customer_no,business_ref_no,orig_sys_ref_no,add_time,update_time,creditor_code,product_trade_code",
                                "has_header": False, "ok_file": ".ok", "lastest_ready_time": "0330",
                                "ftp_polling_sec": 120, "compress_type": None, "compress_passwd": None,
                                "file_encode": None, "alarm_receivers": "jiangyuande", "max_waiting_hours": "1"},
             "sink_configs": {"table_name": "loan_ylsj_ccpaccount_fund_account_trade_after_batch",
                              "target_sep": "special", "table_type": "partition", "partition_key": None,
                              "write_mode": "overwrite"}}
        ]
    },
    {
        'scenario': '助贷账务数据接入-银数江苏(最小化)',
        'describe': '银数江苏小额账务数据最小化回传',
        'project_id': 'loan',
        'source_dsn': 'loan_ylsj_jsyh',
        'sink_dsn': 'mart_fspinno_queqiao',
        'tasks': [
            {"name": "loan_ylsj_min_ccpaccount_loan_bill", "task_type_name": "ftp2mthive",
             "source_configs": {"file_path": "/one-sftp-xy-bank/queqiao/loan/ylsj_jsyh/min/$now.datekey",
                                "file_name": "account.loan_bill_${now.datekey}.txt", "target_sep": "special",
                                "table_cols": "id,union_loan_bill_id,loan_no,product_no,add_time,start_interest_day,expire_date,Interest_bearing_status,loan_total_principal,non_planed_principal,deserved_principal_amount,deserved_interest_amount,deserved_interest_penalty_amount,paid_principal_amount,paid_interest_amount,freed_interest_amount,paid_interest_penalty_amount,settle_date,loan_periods,loan_type,write_off_status,update_time",
                                "has_header": False, "ok_file": ".ok", "lastest_ready_time": "0020",
                                "ftp_polling_sec": 120, "compress_type": None, "compress_passwd": None,
                                "file_encode": None, "alarm_receivers": "jiangyuande", "max_waiting_hours": "1"},
             "sink_configs": {"table_name": "loan_ylsj_min_ccpaccount_loan_bill", "target_sep": "special",
                              "table_type": "partition", "partition_key": None, "write_mode": "overwrite"}},
            {"name": "loan_ylsj_min_ccpaccount_period_bill", "task_type_name": "ftp2mthive",
             "source_configs": {"file_path": "/one-sftp-xy-bank/queqiao/loan/ylsj_jsyh/min/$now.datekey",
                                "file_name": "account.period_bill_${now.datekey}.txt", "target_sep": "special",
                                "table_cols": "id,loan_id,union_loan_bill_id,product_no,type,generate_type,status,settle_date,start_interest_day,overdue_days,overdue_level,plan_repay_date,deserved_principal_amount,deserved_interest_amount,deserved_interest_penalty_amount,paid_principal_amount,paid_interest_amount,paid_interest_penalty_amount,freed_interest_amount,add_time,update_time",
                                "has_header": False, "ok_file": ".ok", "lastest_ready_time": "0020",
                                "ftp_polling_sec": 120, "compress_type": None, "compress_passwd": None,
                                "file_encode": None, "alarm_receivers": "jiangyuande", "max_waiting_hours": "1"},
             "sink_configs": {"table_name": "loan_ylsj_min_ccpaccount_period_bill", "target_sep": "special",
                              "table_type": "partition", "partition_key": None, "write_mode": "overwrite"}},
            {"name": "loan_ylsj_min_ccpaccount_period_bill_after_batch", "task_type_name": "ftp2mthive",
             "source_configs": {"file_path": "/one-sftp-xy-bank/queqiao/loan/ylsj_jsyh/min/$now.datekey",
                                "file_name": "account.period_bill_after_batch_${now.datekey}.txt",
                                "target_sep": "special",
                                "table_cols": "id,loan_id,union_loan_bill_id,product_no,type,generate_type,status,settle_date,start_interest_day,overdue_days,overdue_level,plan_repay_date,deserved_principal_amount,deserved_interest_amount,deserved_interest_penalty_amount,paid_principal_amount,paid_interest_amount,paid_interest_penalty_amount,freed_interest_amount,add_time,update_time",
                                "has_header": False, "ok_file": ".ok", "lastest_ready_time": "0330",
                                "ftp_polling_sec": 120, "compress_type": None, "compress_passwd": None,
                                "file_encode": None, "alarm_receivers": "jiangyuande", "max_waiting_hours": "1"},
             "sink_configs": {"table_name": "loan_ylsj_min_ccpaccount_period_bill_after_batch", "target_sep": "special",
                              "table_type": "partition", "partition_key": None, "write_mode": "overwrite"}},
            {"name": "loan_ylsj_min_ccpaccount_period_account_trade", "task_type_name": "ftp2mthive",
             "source_configs": {"file_path": "/one-sftp-xy-bank/queqiao/loan/ylsj_jsyh/min/$now.datekey",
                                "file_name": "account.period_account_trade_${now.datekey}.txt",
                                "target_sep": "special",
                                "table_cols": "id,loan_id,period_id,product_no,business_ref_no,customer_no,product_trade_code,trade_code,trade_amount,trade_sys_date,trade_time",
                                "has_header": False, "ok_file": ".ok", "lastest_ready_time": "0030",
                                "ftp_polling_sec": 120, "compress_type": None, "compress_passwd": None,
                                "file_encode": None, "alarm_receivers": "jiangyuande", "max_waiting_hours": "1"},
             "sink_configs": {"table_name": "loan_ylsj_min_ccpaccount_period_account_trade", "target_sep": "special",
                              "table_type": "partition", "partition_key": None, "write_mode": "overwrite"}}
        ]
    },
    {
        'scenario': '助贷账务数据接入-银数天津',
        'describe': '银数天津小额账务数据',
        'project_id': 'loan',
        'source_dsn': 'loan_ylsj_tjyh',
        'sink_dsn': 'mart_fspinno_queqiao',
        'tasks': [
            {"name": "loan_ylsj_tjyh_ccpaccount_account", "task_type_name": "ftp2mthive",
             "source_configs": {"file_path": "/one-sftp-xy-bank/queqiao/loan/ylsj_tjyh/$now.datekey",
                                "file_name": "account.account_${now.datekey}.txt", "target_sep": "special",
                                "table_cols": "id,account_no,customer_no,product_no,org_no,currency,open_date,bill_day,due_day,auth_product_index_id,auth_person_index_id,status,frozen_status,account_balance,principal_balance,interest_balance,interest_penalty_balance,fee_balance,deserved_principal_amount,deserved_interest_amount,deserved_interest_penalty_amount,deserved_fee_amount,paid_principal_amount,paid_interest_amount,paid_interest_penalty_amount,paid_fee_amount,version,add_time,update_time",
                                "has_header": False, "ok_file": ".ok", "lastest_ready_time": "0020",
                                "ftp_polling_sec": 120, "compress_type": None, "compress_passwd": None,
                                "file_encode": None, "alarm_receivers": "jiangyuande", "max_waiting_hours": "1"},
             "sink_configs": {"table_name": "loan_ylsj_tjyh_ccpaccount_account", "target_sep": "special",
                              "table_type": "partition", "partition_key": None, "write_mode": "overwrite"}},
            {"name": "loan_ylsj_tjyh_ccpaccount_loan_bill", "task_type_name": "ftp2mthive",
             "source_configs": {"file_path": "/one-sftp-xy-bank/queqiao/loan/ylsj_tjyh/$now.datekey",
                                "file_name": "account.loan_bill_${now.datekey}.txt", "target_sep": "special",
                                "table_cols": "id,union_loan_bill_id,is_union,account_id,customer_no,product_no,loan_no,ext_id,loan_type,rate_index_id,advance_repay_fee_rate,loan_total_principal,loan_periods,advance_repayable_period,repay_type,interval_type,total_terms,start_interest_day,open_date,expire_date,status,write_off_status,interest_bearing_status,settle_date,loan_balance,planed_principal,non_planed_principal,principal_balance,interest_balance,interest_penalty_balance,fee_balance,advance_repay_fee_balance,deserved_principal_amount,deserved_interest_amount,deserved_interest_penalty_amount,deserved_fee_amount,deserved_advance_repay_fee_amount,paid_principal_amount,paid_interest_amount,paid_interest_penalty_amount,paid_fee_amount,paid_advance_repay_fee_amount,freed_interest_amount,freed_fee_amount,overdue_level,overdue_days,version,add_time,update_time,creditor_code,creditor_proportion,creditors",
                                "has_header": False, "ok_file": ".ok", "lastest_ready_time": "0020",
                                "ftp_polling_sec": 120, "compress_type": None, "compress_passwd": None,
                                "file_encode": None, "alarm_receivers": "jiangyuande", "max_waiting_hours": "1"},
             "sink_configs": {"table_name": "loan_ylsj_tjyh_ccpaccount_loan_bill", "target_sep": "special",
                              "table_type": "partition", "partition_key": None, "write_mode": "overwrite"}},
            {"name": "loan_ylsj_tjyh_ccpaccount_loan_bill_after_batch", "task_type_name": "ftp2mthive",
             "source_configs": {"file_path": "/one-sftp-xy-bank/queqiao/loan/ylsj_tjyh/$now.datekey",
                                "file_name": "account.loan_bill_after_batch_${now.datekey}.txt",
                                "target_sep": "special",
                                "table_cols": "id,union_loan_bill_id,is_union,account_id,customer_no,product_no,loan_no,ext_id,loan_type,rate_index_id,advance_repay_fee_rate,loan_total_principal,loan_periods,advance_repayable_period,repay_type,interval_type,total_terms,start_interest_day,open_date,expire_date,status,write_off_status,interest_bearing_status,settle_date,loan_balance,planed_principal,non_planed_principal,principal_balance,interest_balance,interest_penalty_balance,fee_balance,advance_repay_fee_balance,deserved_principal_amount,deserved_interest_amount,deserved_interest_penalty_amount,deserved_fee_amount,deserved_advance_repay_fee_amount,paid_principal_amount,paid_interest_amount,paid_interest_penalty_amount,paid_fee_amount,paid_advance_repay_fee_amount,freed_interest_amount,freed_fee_amount,overdue_level,overdue_days,version,add_time,update_time,creditor_code,creditor_proportion,creditors",
                                "has_header": False, "ok_file": ".ok", "lastest_ready_time": "0330",
                                "ftp_polling_sec": 120, "compress_type": None, "compress_passwd": None,
                                "file_encode": None, "alarm_receivers": "jiangyuande", "max_waiting_hours": "1"},
             "sink_configs": {"table_name": "loan_ylsj_tjyh_ccpaccount_loan_bill_after_batch", "target_sep": "special",
                              "table_type": "partition", "partition_key": None, "write_mode": "overwrite"}},
            {"name": "loan_ylsj_tjyh_ccpaccount_period_bill", "task_type_name": "ftp2mthive",
             "source_configs": {"file_path": "/one-sftp-xy-bank/queqiao/loan/ylsj_tjyh/$now.datekey",
                                "file_name": "account.period_bill_${now.datekey}.txt", "target_sep": "special",
                                "table_cols": "id,union_period_bill_id,account_id,customer_no,product_no,loan_id,union_loan_bill_id,is_union,type,generate_type,start_interest_day,end_interest_day,status,period_plan_date,plan_repay_date,settle_date,overdue_level,overdue_days,period_balance,principal_balance,interest_balance,interest_penalty_balance,fee_balance,advance_repay_fee_balance,deserved_principal_amount,deserved_interest_amount,new_deserved_interest_amount,deserved_interest_penalty_amount,deserved_fee_amount,new_deserved_fee_amount,deserved_advance_repay_fee_amount,paid_principal_amount,paid_interest_amount,paid_interest_penalty_amount,paid_fee_amount,paid_advance_repay_fee_amount,freed_interest_amount,freed_fee_amount,plan_interest_amount,plan_fee_amount,coupon_can_free_interest_amount,coupon_can_free_fee_amount,add_time,update_time,version",
                                "has_header": False, "ok_file": ".ok", "lastest_ready_time": "0020",
                                "ftp_polling_sec": 120, "compress_type": None, "compress_passwd": None,
                                "file_encode": None, "alarm_receivers": "jiangyuande", "max_waiting_hours": "1"},
             "sink_configs": {"table_name": "loan_ylsj_tjyh_ccpaccount_period_bill", "target_sep": "special",
                              "table_type": "partition", "partition_key": None, "write_mode": "overwrite"}},
            {"name": "loan_ylsj_tjyh_ccpaccount_period_bill_after_batch", "task_type_name": "ftp2mthive",
             "source_configs": {"file_path": "/one-sftp-xy-bank/queqiao/loan/ylsj_tjyh/$now.datekey",
                                "file_name": "account.period_bill_after_batch_${now.datekey}.txt",
                                "target_sep": "special",
                                "table_cols": "id,union_period_bill_id,account_id,customer_no,product_no,loan_id,union_loan_bill_id,is_union,type,generate_type,start_interest_day,end_interest_day,status,period_plan_date,plan_repay_date,settle_date,overdue_level,overdue_days,period_balance,principal_balance,interest_balance,interest_penalty_balance,fee_balance,advance_repay_fee_balance,deserved_principal_amount,deserved_interest_amount,new_deserved_interest_amount,deserved_interest_penalty_amount,deserved_fee_amount,new_deserved_fee_amount,deserved_advance_repay_fee_amount,paid_principal_amount,paid_interest_amount,paid_interest_penalty_amount,paid_fee_amount,paid_advance_repay_fee_amount,freed_interest_amount,freed_fee_amount,plan_interest_amount,plan_fee_amount,coupon_can_free_interest_amount,coupon_can_free_fee_amount,add_time,update_time,version",
                                "has_header": False, "ok_file": ".ok", "lastest_ready_time": "0330",
                                "ftp_polling_sec": 120, "compress_type": None, "compress_passwd": None,
                                "file_encode": None, "alarm_receivers": "jiangyuande", "max_waiting_hours": "1"},
             "sink_configs": {"table_name": "loan_ylsj_tjyh_ccpaccount_period_bill_after_batch",
                              "target_sep": "special", "table_type": "partition", "partition_key": None,
                              "write_mode": "overwrite"}},
            {"name": "loan_ylsj_tjyh_ccpaccount_period_bill_status_trace", "task_type_name": "ftp2mthive",
             "source_configs": {"file_path": "/one-sftp-xy-bank/queqiao/loan/ylsj_tjyh/$now.datekey",
                                "file_name": "account.period_bill_status_trace_${now.datekey}.txt",
                                "target_sep": "special",
                                "table_cols": "id,sys_ref_no,trade_time,trade_sys_date,customer_no,product_no,account_id,loan_id,period_id,event_code,before_status,after_status,business_ref_no,product_trade_code,creditor_code,add_time,update_time,is_union",
                                "has_header": False, "ok_file": ".ok", "lastest_ready_time": "0030",
                                "ftp_polling_sec": 120, "compress_type": None, "compress_passwd": None,
                                "file_encode": None, "alarm_receivers": "jiangyuande", "max_waiting_hours": "1"},
             "sink_configs": {"table_name": "loan_ylsj_tjyh_ccpaccount_period_bill_status_trace",
                              "target_sep": "special", "table_type": "partition", "partition_key": None,
                              "write_mode": "overwrite"}},
            {"name": "loan_ylsj_tjyh_ccpaccount_account_trade", "task_type_name": "ftp2mthive",
             "source_configs": {"file_path": "/one-sftp-xy-bank/queqiao/loan/ylsj_tjyh/$now.datekey",
                                "file_name": "account.account_trade_${now.datekey}.txt",
                                "target_sep": "special",
                                "table_cols": "id,sys_ref_no,sys_ref_seq,trade_time,trade_sys_date,account_id,loan_id,period_id,trade_code,debit_credit_flag,trade_description,trade_amount,currency,abs_no,org_no,product_no,customer_no,business_ref_no,orig_sys_ref_no,add_time,update_time,creditor_code,product_trade_code,is_union",
                                "has_header": False, "ok_file": ".ok", "lastest_ready_time": "0030",
                                "ftp_polling_sec": 120, "compress_type": None, "compress_passwd": None,
                                "file_encode": None, "alarm_receivers": "jiangyuande", "max_waiting_hours": "1"},
             "sink_configs": {"table_name": "loan_ylsj_tjyh_ccpaccount_account_trade", "target_sep": "special",
                              "table_type": "partition", "partition_key": None, "write_mode": "overwrite"}},
            {"name": "loan_ylsj_tjyh_ccpaccount_account_trade_after_batch", "task_type_name": "ftp2mthive",
             "source_configs": {"file_path": "/one-sftp-xy-bank/queqiao/loan/ylsj_tjyh/$now.datekey",
                                "file_name": "account.account_trade_after_batch_${now.datekey}.txt",
                                "target_sep": "special",
                                "table_cols": "id,sys_ref_no,sys_ref_seq,trade_time,trade_sys_date,account_id,loan_id,period_id,trade_code,debit_credit_flag,trade_description,trade_amount,currency,abs_no,org_no,product_no,customer_no,business_ref_no,orig_sys_ref_no,add_time,update_time,creditor_code,product_trade_code,is_union",
                                "has_header": False, "ok_file": ".ok", "lastest_ready_time": "0330",
                                "ftp_polling_sec": 120, "compress_type": None, "compress_passwd": None,
                                "file_encode": None, "alarm_receivers": "jiangyuande", "max_waiting_hours": "1"},
             "sink_configs": {"table_name": "loan_ylsj_tjyh_ccpaccount_account_trade_after_batch",
                              "target_sep": "special", "table_type": "partition", "partition_key": None,
                              "write_mode": "overwrite"}},
            {"name": "loan_ylsj_tjyh_ccpaccount_loan_bill_status_trace", "task_type_name": "ftp2mthive",
             "source_configs": {"file_path": "/one-sftp-xy-bank/queqiao/loan/ylsj_tjyh/$now.datekey",
                                "file_name": "account.loan_bill_status_trace_${now.datekey}.txt",
                                "target_sep": "special",
                                "table_cols": "id,sys_ref_no,trade_time,trade_sys_date,customer_no,product_no,account_id,loan_id,event_code,before_status,after_status,business_ref_no,product_trade_code,creditor_code,add_time,update_time,is_union",
                                "has_header": False, "ok_file": ".ok", "lastest_ready_time": "0030",
                                "ftp_polling_sec": 120, "compress_type": None, "compress_passwd": None,
                                "file_encode": None, "alarm_receivers": "jiangyuande", "max_waiting_hours": "1"},
             "sink_configs": {"table_name": "loan_ylsj_tjyh_ccpaccount_loan_bill_status_trace", "target_sep": "special",
                              "table_type": "partition", "partition_key": None, "write_mode": "overwrite"}},
            {"name": "loan_ylsj_tjyh_ccpaccount_period_account_trade", "task_type_name": "ftp2mthive",
             "source_configs": {"file_path": "/one-sftp-xy-bank/queqiao/loan/ylsj_tjyh/$now.datekey",
                                "file_name": "account.period_account_trade_${now.datekey}.txt",
                                "target_sep": "special",
                                "table_cols": "id,sys_ref_no,sys_ref_seq,trade_time,trade_sys_date,account_id,loan_id,period_id,trade_code,trade_description,trade_amount,currency,abs_no,org_no,product_no,customer_no,business_ref_no,orig_sys_ref_no,product_trade_code,add_time,update_time,is_union,creditor_code",
                                "has_header": False, "ok_file": ".ok", "lastest_ready_time": "0030",
                                "ftp_polling_sec": 120, "compress_type": None, "compress_passwd": None,
                                "file_encode": None, "alarm_receivers": "jiangyuande", "max_waiting_hours": "1"},
             "sink_configs": {"table_name": "loan_ylsj_tjyh_ccpaccount_period_account_trade", "target_sep": "special",
                              "table_type": "partition", "partition_key": None, "write_mode": "overwrite"}},
            {"name": "loan_ylsj_tjyh_ccpaccount_coupon", "task_type_name": "ftp2mthive",
             "source_configs": {"file_path": "/one-sftp-xy-bank/queqiao/loan/ylsj_tjyh/$now.datekey",
                                "file_name": "account.coupon_${now.datekey}.txt", "target_sep": "special",
                                "table_cols": "id,account_id,customer_no,product_no,coupon_type,coupon_value,invalidate_scene,invalid_recall_period,effective_periods,can_free_interest_days,loan_bill_id,period_bill_id,is_used,can_free_interest_amount,freed_interest_amount,recall_freed_amount,freed_interest_days,discount_rate,period_discount_rates,max_amount_of_coupon_available,min_amount_of_coupon_available,top_write_off_principal,add_time,update_time",
                                "has_header": False, "ok_file": ".ok", "lastest_ready_time": "0020",
                                "ftp_polling_sec": 120, "compress_type": None, "compress_passwd": None,
                                "file_encode": None, "alarm_receivers": "jiangyuande", "max_waiting_hours": "1"},
             "sink_configs": {"table_name": "loan_ylsj_tjyh_ccpaccount_coupon", "target_sep": "special",
                              "table_type": "partition", "partition_key": None, "write_mode": "overwrite"}},
            {"name": "loan_ylsj_tjyh_ccpaccount_refund_record", "task_type_name": "ftp2mthive",
             "source_configs": {"file_path": "/one-sftp-xy-bank/queqiao/loan/ylsj_tjyh/$now.datekey",
                                "file_name": "account.refund_record_${now.datekey}.txt",
                                "target_sep": "special",
                                "table_cols": "id,account_id,customer_no,product_no,business_ref_no,refund_amount,retreating_amount,add_time,update_time",
                                "has_header": False, "ok_file": ".ok", "lastest_ready_time": "0020",
                                "ftp_polling_sec": 120, "compress_type": None, "compress_passwd": None,
                                "file_encode": None, "alarm_receivers": "jiangyuande", "max_waiting_hours": "1"},
             "sink_configs": {"table_name": "loan_ylsj_tjyh_ccpaccount_refund_record", "target_sep": "special",
                              "table_type": "partition", "partition_key": None, "write_mode": "overwrite"}},
            {"name": "loan_ylsj_tjyh_ccpaccount_interest_control", "task_type_name": "ftp2mthive",
             "source_configs": {"file_path": "/one-sftp-xy-bank/queqiao/loan/ylsj_tjyh/$now.datekey",
                                "file_name": "account.interest_control_${now.datekey}.txt",
                                "target_sep": "special",
                                "table_cols": "id,product_no,currency,control_index,org_no,compute_method,principal_compute_flag,interest_compute_flag,penalty_compute_flag,fee_compute_flag,rate,penalty_rate,add_time,update_time",
                                "has_header": False, "ok_file": ".ok", "lastest_ready_time": "0020",
                                "ftp_polling_sec": 120, "compress_type": None, "compress_passwd": None,
                                "file_encode": None, "alarm_receivers": "jiangyuande", "max_waiting_hours": "1"},
             "sink_configs": {"table_name": "loan_ylsj_tjyh_ccpaccount_interest_control", "target_sep": "special",
                              "table_type": "partition", "partition_key": None, "write_mode": "overwrite"}},
            {"name": "loan_ylsj_tjyh_ccpaccount_fund_loan_bill_status_trace", "task_type_name": "ftp2mthive",
             "source_configs": {"file_path": "/one-sftp-xy-bank/queqiao/loan/ylsj_tjyh/$now.datekey",
                                "file_name": "account_fund.loan_bill_status_trace_${now.datekey}.txt",
                                "target_sep": "special",
                                "table_cols": "id,sys_ref_no,trade_time,trade_sys_date,customer_no,product_no,account_id,loan_id,event_code,before_status,after_status,business_ref_no,product_trade_code,creditor_code,add_time,update_time",
                                "has_header": False, "ok_file": ".ok", "lastest_ready_time": "0030",
                                "ftp_polling_sec": 120, "compress_type": None, "compress_passwd": None,
                                "file_encode": None, "alarm_receivers": "jiangyuande", "max_waiting_hours": "1"},
             "sink_configs": {"table_name": "loan_ylsj_tjyh_ccpaccount_fund_loan_bill_status_trace",
                              "target_sep": "special", "table_type": "partition", "partition_key": None,
                              "write_mode": "overwrite"}},
            {"name": "loan_ylsj_tjyh_ccpaccount_fund_period_account_trade", "task_type_name": "ftp2mthive",
             "source_configs": {"file_path": "/one-sftp-xy-bank/queqiao/loan/ylsj_tjyh/$now.datekey",
                                "file_name": "account_fund.period_account_trade_${now.datekey}.txt",
                                "target_sep": "special",
                                "table_cols": "id,sys_ref_no,sys_ref_seq,trade_time,trade_sys_date,account_id,loan_id,period_id,trade_code,trade_description,trade_amount,currency,org_no,product_no,customer_no,business_ref_no,orig_sys_ref_no,product_trade_code,add_time,update_time,creditor_code",
                                "has_header": False, "ok_file": ".ok", "lastest_ready_time": "0030",
                                "ftp_polling_sec": 120, "compress_type": None, "compress_passwd": None,
                                "file_encode": None, "alarm_receivers": "jiangyuande", "max_waiting_hours": "1"},
             "sink_configs": {"table_name": "loan_ylsj_tjyh_ccpaccount_fund_period_account_trade",
                              "target_sep": "special", "table_type": "partition", "partition_key": None,
                              "write_mode": "overwrite"}},
            {"name": "loan_ylsj_tjyh_ccpaccount_fund_period_bill_status_trace", "task_type_name": "ftp2mthive",
             "source_configs": {"file_path": "/one-sftp-xy-bank/queqiao/loan/ylsj_tjyh/$now.datekey",
                                "file_name": "account_fund.period_bill_status_trace_${now.datekey}.txt",
                                "target_sep": "special",
                                "table_cols": "id,sys_ref_no,trade_time,trade_sys_date,customer_no,product_no,account_id,loan_id,period_id,event_code,before_status,after_status,business_ref_no,product_trade_code,creditor_code,add_time,update_time",
                                "has_header": False, "ok_file": ".ok", "lastest_ready_time": "0030",
                                "ftp_polling_sec": 120, "compress_type": None, "compress_passwd": None,
                                "file_encode": None, "alarm_receivers": "jiangyuande", "max_waiting_hours": "1"},
             "sink_configs": {"table_name": "loan_ylsj_tjyh_ccpaccount_fund_period_bill_status_trace",
                              "target_sep": "special", "table_type": "partition", "partition_key": None,
                              "write_mode": "overwrite"}},
            {"name": "loan_ylsj_tjyh_ccpaccount_fund_loan_bill", "task_type_name": "ftp2mthive",
             "source_configs": {"file_path": "/one-sftp-xy-bank/queqiao/loan/ylsj_tjyh/$now.datekey",
                                "file_name": "account_fund.loan_bill_${now.datekey}.txt",
                                "target_sep": "special",
                                "table_cols": "id,union_loan_bill_id,account_id,customer_no,product_no,loan_no,ext_id,loan_type,rate_index_id,loan_total_principal,loan_periods,repay_type,interval_type,total_terms,start_interest_day,open_date,expire_date,status,write_off_status,interest_bearing_status,settle_date,loan_balance,planed_principal,non_planed_principal,principal_balance,interest_balance,interest_penalty_balance,fee_balance,deserved_principal_amount,deserved_interest_amount,deserved_interest_penalty_amount,deserved_fee_amount,paid_principal_amount,paid_interest_amount,paid_interest_penalty_amount,paid_fee_amount,freed_interest_amount,freed_fee_amount,overdue_level,overdue_days,version,add_time,update_time,creditor_code,creditor_proportion",
                                "has_header": False, "ok_file": ".ok", "lastest_ready_time": "0020",
                                "ftp_polling_sec": 120, "compress_type": None, "compress_passwd": None,
                                "file_encode": None, "alarm_receivers": "jiangyuande", "max_waiting_hours": "1"},
             "sink_configs": {"table_name": "loan_ylsj_tjyh_ccpaccount_fund_loan_bill", "target_sep": "special",
                              "table_type": "partition", "partition_key": None, "write_mode": "overwrite"}},
            {"name": "loan_ylsj_tjyh_ccpaccount_fund_loan_bill_after_batch", "task_type_name": "ftp2mthive",
             "source_configs": {"file_path": "/one-sftp-xy-bank/queqiao/loan/ylsj_tjyh/$now.datekey",
                                "file_name": "account_fund.loan_bill_after_batch_${now.datekey}.txt",
                                "target_sep": "special",
                                "table_cols": "id,union_loan_bill_id,account_id,customer_no,product_no,loan_no,ext_id,loan_type,rate_index_id,loan_total_principal,loan_periods,repay_type,interval_type,total_terms,start_interest_day,open_date,expire_date,status,write_off_status,interest_bearing_status,settle_date,loan_balance,planed_principal,non_planed_principal,principal_balance,interest_balance,interest_penalty_balance,fee_balance,deserved_principal_amount,deserved_interest_amount,deserved_interest_penalty_amount,deserved_fee_amount,paid_principal_amount,paid_interest_amount,paid_interest_penalty_amount,paid_fee_amount,freed_interest_amount,freed_fee_amount,overdue_level,overdue_days,version,add_time,update_time,creditor_code,creditor_proportion",
                                "has_header": False, "ok_file": ".ok", "lastest_ready_time": "0330",
                                "ftp_polling_sec": 120, "compress_type": None, "compress_passwd": None,
                                "file_encode": None, "alarm_receivers": "jiangyuande", "max_waiting_hours": "1"},
             "sink_configs": {"table_name": "loan_ylsj_tjyh_ccpaccount_fund_loan_bill_after_batch",
                              "target_sep": "special", "table_type": "partition", "partition_key": None,
                              "write_mode": "overwrite"}},
            {"name": "loan_ylsj_tjyh_ccpaccount_fund_period_bill", "task_type_name": "ftp2mthive",
             "source_configs": {"file_path": "/one-sftp-xy-bank/queqiao/loan/ylsj_tjyh/$now.datekey",
                                "file_name": "account_fund.period_bill_${now.datekey}.txt",
                                "target_sep": "special",
                                "table_cols": "id,union_period_bill_id,account_id,customer_no,product_no,loan_id,union_loan_bill_id,type,generate_type,start_interest_day,end_interest_day,status,period_plan_date,plan_repay_date,settle_date,overdue_level,overdue_days,period_balance,principal_balance,interest_balance,interest_penalty_balance,fee_balance,plan_interest_amount,plan_fee_amount,deserved_principal_amount,deserved_interest_amount,deserved_interest_penalty_amount,deserved_fee_amount,paid_principal_amount,paid_interest_amount,paid_interest_penalty_amount,paid_fee_amount,coupon_can_free_interest_amount,coupon_can_free_fee_amount,freed_interest_amount,freed_fee_amount,creditor_interest_limit,creditor_fee_limit,add_time,update_time,version",
                                "has_header": False, "ok_file": ".ok", "lastest_ready_time": "0020",
                                "ftp_polling_sec": 120, "compress_type": None, "compress_passwd": None,
                                "file_encode": None, "alarm_receivers": "jiangyuande", "max_waiting_hours": "1"},
             "sink_configs": {"table_name": "loan_ylsj_tjyh_ccpaccount_fund_period_bill", "target_sep": "special",
                              "table_type": "partition", "partition_key": None, "write_mode": "overwrite"}},
            {"name": "loan_ylsj_tjyh_ccpaccount_fund_period_bill_after_batch", "task_type_name": "ftp2mthive",
             "source_configs": {"file_path": "/one-sftp-xy-bank/queqiao/loan/ylsj_tjyh/$now.datekey",
                                "file_name": "account_fund.period_bill_after_batch_${now.datekey}.txt",
                                "target_sep": "special",
                                "table_cols": "id,union_period_bill_id,account_id,customer_no,product_no,loan_id,union_loan_bill_id,type,generate_type,start_interest_day,end_interest_day,status,period_plan_date,plan_repay_date,settle_date,overdue_level,overdue_days,period_balance,principal_balance,interest_balance,interest_penalty_balance,fee_balance,plan_interest_amount,plan_fee_amount,deserved_principal_amount,deserved_interest_amount,deserved_interest_penalty_amount,deserved_fee_amount,paid_principal_amount,paid_interest_amount,paid_interest_penalty_amount,paid_fee_amount,coupon_can_free_interest_amount,coupon_can_free_fee_amount,freed_interest_amount,freed_fee_amount,creditor_interest_limit,creditor_fee_limit,add_time,update_time,version",
                                "has_header": False, "ok_file": ".ok", "lastest_ready_time": "0330",
                                "ftp_polling_sec": 120, "compress_type": None, "compress_passwd": None,
                                "file_encode": None, "alarm_receivers": "jiangyuande", "max_waiting_hours": "1"},
             "sink_configs": {"table_name": "loan_ylsj_tjyh_ccpaccount_fund_period_bill_after_batch",
                              "target_sep": "special", "table_type": "partition", "partition_key": None,
                              "write_mode": "overwrite"}},
            {"name": "loan_ylsj_tjyh_ccpaccount_fund_account_trade", "task_type_name": "ftp2mthive",
             "source_configs": {"file_path": "/one-sftp-xy-bank/queqiao/loan/ylsj_tjyh/$now.datekey",
                                "file_name": "account_fund.account_trade_${now.datekey}.txt",
                                "target_sep": "special",
                                "table_cols": "id,sys_ref_no,sys_ref_seq,trade_time,trade_sys_date,account_id,loan_id,period_id,trade_code,trade_description,trade_amount,currency,org_no,product_no,customer_no,business_ref_no,orig_sys_ref_no,add_time,update_time,creditor_code,product_trade_code",
                                "has_header": False, "ok_file": ".ok", "lastest_ready_time": "0030",
                                "ftp_polling_sec": 120, "compress_type": None, "compress_passwd": None,
                                "file_encode": None, "alarm_receivers": "jiangyuande", "max_waiting_hours": "1"},
             "sink_configs": {"table_name": "loan_ylsj_tjyh_ccpaccount_fund_account_trade", "target_sep": "special",
                              "table_type": "partition", "partition_key": None, "write_mode": "overwrite"}},
            {"name": "loan_ylsj_tjyh_ccpaccount_fund_account_trade_after_batch", "task_type_name": "ftp2mthive",
             "source_configs": {"file_path": "/one-sftp-xy-bank/queqiao/loan/ylsj_tjyh/$now.datekey",
                                "file_name": "account_fund.account_trade_after_batch_${now.datekey}.txt",
                                "target_sep": "special",
                                "table_cols": "id,sys_ref_no,sys_ref_seq,trade_time,trade_sys_date,account_id,loan_id,period_id,trade_code,trade_description,trade_amount,currency,org_no,product_no,customer_no,business_ref_no,orig_sys_ref_no,add_time,update_time,creditor_code,product_trade_code",
                                "has_header": False, "ok_file": ".ok", "lastest_ready_time": "0330",
                                "ftp_polling_sec": 120, "compress_type": None, "compress_passwd": None,
                                "file_encode": None, "alarm_receivers": "jiangyuande", "max_waiting_hours": "1"},
             "sink_configs": {"table_name": "loan_ylsj_tjyh_ccpaccount_fund_account_trade_after_batch",
                              "target_sep": "special", "table_type": "partition", "partition_key": None,
                              "write_mode": "overwrite"}}
        ]
    },
    {
        'scenario': '助贷账务数据接入-银数天津(最小化)',
        'describe': '银数天津小额账务数据最小化回传',
        'project_id': 'loan',
        'source_dsn': 'loan_ylsj_tjyh',
        'sink_dsn': 'mart_fspinno_queqiao',
        'tasks': [
            {"name": "loan_ylsj_tjyh_min_ccpaccount_loan_bill", "task_type_name": "ftp2mthive",
             "source_configs": {"file_path": "/one-sftp-xy-bank/queqiao/loan/ylsj_tjyh/min/$now.datekey",
                                "file_name": "account.loan_bill_${now.datekey}.txt", "target_sep": "special",
                                "table_cols": "id,union_loan_bill_id,loan_no,product_no,add_time,start_interest_day,expire_date,Interest_bearing_status,loan_total_principal,non_planed_principal,deserved_principal_amount,deserved_interest_amount,deserved_interest_penalty_amount,paid_principal_amount,paid_interest_amount,freed_interest_amount,paid_interest_penalty_amount,settle_date,loan_periods,loan_type,write_off_status,update_time",
                                "has_header": False, "ok_file": ".ok", "lastest_ready_time": "0020",
                                "ftp_polling_sec": 120, "compress_type": None, "compress_passwd": None,
                                "file_encode": None, "alarm_receivers": "jiangyuande", "max_waiting_hours": "1"},
             "sink_configs": {"table_name": "loan_ylsj_tjyh_min_ccpaccount_loan_bill", "target_sep": "special",
                              "table_type": "partition", "partition_key": None, "write_mode": "overwrite"}},
            {"name": "loan_ylsj_tjyh_min_ccpaccount_period_bill", "task_type_name": "ftp2mthive",
             "source_configs": {"file_path": "/one-sftp-xy-bank/queqiao/loan/ylsj_tjyh/min/$now.datekey",
                                "file_name": "account.period_bill_${now.datekey}.txt", "target_sep": "special",
                                "table_cols": "id,loan_id,union_loan_bill_id,product_no,type,generate_type,status,settle_date,start_interest_day,overdue_days,overdue_level,plan_repay_date,deserved_principal_amount,deserved_interest_amount,deserved_interest_penalty_amount,paid_principal_amount,paid_interest_amount,paid_interest_penalty_amount,freed_interest_amount,add_time,update_time",
                                "has_header": False, "ok_file": ".ok", "lastest_ready_time": "0020",
                                "ftp_polling_sec": 120, "compress_type": None, "compress_passwd": None,
                                "file_encode": None, "alarm_receivers": "jiangyuande", "max_waiting_hours": "1"},
             "sink_configs": {"table_name": "loan_ylsj_tjyh_min_ccpaccount_period_bill", "target_sep": "special",
                              "table_type": "partition", "partition_key": None, "write_mode": "overwrite"}},
            {"name": "loan_ylsj_tjyh_min_ccpaccount_period_bill_after_batch", "task_type_name": "ftp2mthive",
             "source_configs": {"file_path": "/one-sftp-xy-bank/queqiao/loan/ylsj_tjyh/min/$now.datekey",
                                "file_name": "account.period_bill_after_batch_${now.datekey}.txt",
                                "target_sep": "special",
                                "table_cols": "id,loan_id,union_loan_bill_id,product_no,type,generate_type,status,settle_date,start_interest_day,overdue_days,overdue_level,plan_repay_date,deserved_principal_amount,deserved_interest_amount,deserved_interest_penalty_amount,paid_principal_amount,paid_interest_amount,paid_interest_penalty_amount,freed_interest_amount,add_time,update_time",
                                "has_header": False, "ok_file": ".ok", "lastest_ready_time": "0330",
                                "ftp_polling_sec": 120, "compress_type": None, "compress_passwd": None,
                                "file_encode": None, "alarm_receivers": "jiangyuande", "max_waiting_hours": "1"},
             "sink_configs": {"table_name": "loan_ylsj_tjyh_min_ccpaccount_period_bill_after_batch",
                              "target_sep": "special",
                              "table_type": "partition", "partition_key": None, "write_mode": "overwrite"}},
            {"name": "loan_ylsj_tjyh_min_ccpaccount_period_account_trade", "task_type_name": "ftp2mthive",
             "source_configs": {"file_path": "/one-sftp-xy-bank/queqiao/loan/ylsj_tjyh/min/$now.datekey",
                                "file_name": "account.period_account_trade_${now.datekey}.txt",
                                "target_sep": "special",
                                "table_cols": "id,loan_id,period_id,product_no,business_ref_no,customer_no,product_trade_code,trade_code,trade_amount,trade_sys_date,trade_time",
                                "has_header": False, "ok_file": ".ok", "lastest_ready_time": "0030",
                                "ftp_polling_sec": 120, "compress_type": None, "compress_passwd": None,
                                "file_encode": None, "alarm_receivers": "jiangyuande", "max_waiting_hours": "1"},
             "sink_configs": {"table_name": "loan_ylsj_tjyh_min_ccpaccount_period_account_trade",
                              "target_sep": "special",
                              "table_type": "partition", "partition_key": None, "write_mode": "overwrite"}}
        ]
    },
    {
        'scenario': '助贷账务数据接入-浦发',
        'describe': '浦发账务数据',
        'project_id': 'loan',
        'source_dsn': 'spdb_loan',
        'sink_dsn': 'mart_fspinno_queqiao',
        'tasks': [
            {"name": "ods_pudong_development_credit_ss", "task_type_name": "wtp2mthive",
             "source_configs": {"file_path": None, "file_name": "tmsxxx_1072_${now.datekey}.dat", "target_sep": "^?",
                                "table_cols": "data_date,credit_limit_no,cooperation_platform,product_no,credit_start_date,credit_expire_date,credit_amount,contract_status,third_party_id,business_platform_no",
                                "has_header": False, "ok_file": "tmsxxx_1072_${now.datekey}.flg",
                                "lastest_ready_time": "1100", "ftp_polling_sec": 300, "compress_type": None,
                                "compress_passwd": None, "file_encode": "gbk", "alarm_receivers": "jiangyuande",
                                "max_waiting_hours": "1"},
             "sink_configs": {"table_name": "ods_pudong_development_credit_ss", "target_sep": "^?",
                              "table_type": "partition", "partition_key": None, "write_mode": "overwrite"}},
            {"name": "ods_pudong_development_loan_bill_ss", "task_type_name": "wtp2mthive",
             "source_configs": {"file_path": None, "file_name": "tmdkxx_1072_${now.datekey}.dat", "target_sep": "^?",
                                "table_cols": "data_date,loan_no,cooperation_platform,product_no,start_interest_day,expire_date,loan_total_principal,principal_balance,repay_type,year_rate,pay_bankcard_code,pay_bankcard_id,pay_bankcard_no,repay_bankcard_code,repay_bankcard_id,repay_bankcard_no,contract_status,settle_date,credit_contract_no,third_party_id,business_platform_no,order_no,order_biz_serial_no,order_time",
                                "has_header": False, "ok_file": "tmdkxx_1072_${now.datekey}.flg",
                                "lastest_ready_time": "1100", "ftp_polling_sec": 300, "compress_type": None,
                                "compress_passwd": None, "file_encode": "gbk", "alarm_receivers": "jiangyuande",
                                "max_waiting_hours": "1"},
             "sink_configs": {"table_name": "ods_pudong_development_loan_bill_ss", "target_sep": "^?",
                              "table_type": "partition", "partition_key": None, "write_mode": "overwrite"}},
            {"name": "ods_pudong_development_loan_overdue_ss", "task_type_name": "wtp2mthive",
             "source_configs": {"file_path": None, "file_name": "hxyqxx_1072_${now.datekey}.dat", "target_sep": "^?",
                                "table_cols": "data_date,loan_no,cooperation_platform,product_no,first_overdue_date,overdue_times,overdue_principal_amount,overdue_interest_amount",
                                "has_header": False, "ok_file": "hxyqxx_1072_${now.datekey}.flg",
                                "lastest_ready_time": "1100", "ftp_polling_sec": 300, "compress_type": None,
                                "compress_passwd": None, "file_encode": "gbk", "alarm_receivers": "jiangyuande",
                                "max_waiting_hours": "1"},
             "sink_configs": {"table_name": "ods_pudong_development_loan_overdue_ss", "target_sep": "^?",
                              "table_type": "partition", "partition_key": None, "write_mode": "overwrite"}},
            {"name": "ods_pudong_development_loan_payment_d", "task_type_name": "wtp2mthive",
             "source_configs": {"file_path": None, "file_name": "hxdkff_1072_${now.datekey}.dat", "target_sep": "^?",
                                "table_cols": "data_date,trade_date,loan_no,payment_principal,cooperation_platform,product_no",
                                "has_header": False, "ok_file": "hxdkff_1072_${now.datekey}.flg",
                                "lastest_ready_time": "1100", "ftp_polling_sec": 300, "compress_type": None,
                                "compress_passwd": None, "file_encode": "gbk", "alarm_receivers": "jiangyuande",
                                "max_waiting_hours": "1"},
             "sink_configs": {"table_name": "ods_pudong_development_loan_payment_d", "target_sep": "^?",
                              "table_type": "partition", "partition_key": None, "write_mode": "overwrite"}},
            {"name": "ods_pudong_development_loan_repayment_d", "task_type_name": "wtp2mthive",
             "source_configs": {"file_path": None, "file_name": "hxhk_1072_${now.datekey}.dat", "target_sep": "^?",
                                "table_cols": "data_date,trade_date,loan_no,biz_serial_no,cooperation_platform,product_no,repay_type,trade_code,normal_repay_tag,overdue_repay_tag,actual_repay_amount,principal_balance,repay_normal_principal_amount,repay_normal_interest_amount,repay_overdue_principal_amount,repay_overdue_interest_amount,request_interest_amount",
                                "has_header": False, "ok_file": "hxhk_1072_${now.datekey}.flg",
                                "lastest_ready_time": "1100", "ftp_polling_sec": 300, "compress_type": None,
                                "compress_passwd": None, "file_encode": "gbk", "alarm_receivers": "jiangyuande",
                                "max_waiting_hours": "1"},
             "sink_configs": {"table_name": "ods_pudong_development_loan_repayment_d", "target_sep": "^?",
                              "table_type": "partition", "partition_key": None, "write_mode": "overwrite"}},
            {"name": "ods_pudong_development_period_overdue_ss", "task_type_name": "wtp2mthive",
             "source_configs": {"file_path": None, "file_name": "tqqgje_1072_${now.datekey}.dat", "target_sep": "^?",
                                "table_cols": "trade_date,loan_no,cooperation_platform,product_no,status,period_number,plan_repay_date,overdue_principal_amount,overdue_interest_amount,principal_penalty_amount,interest_penalty_amount",
                                "has_header": False, "ok_file": "tqqgje_1072_${now.datekey}.flg",
                                "lastest_ready_time": "1100", "ftp_polling_sec": 300, "compress_type": None,
                                "compress_passwd": None, "file_encode": "gbk", "alarm_receivers": "jiangyuande",
                                "max_waiting_hours": "1"},
             "sink_configs": {"table_name": "ods_pudong_development_period_overdue_ss", "target_sep": "^?",
                              "table_type": "partition", "partition_key": None, "write_mode": "overwrite"}},
            {"name": "ods_pudong_development_period_repayment_plan_d", "task_type_name": "wtp2mthive",
             "source_configs": {"file_path": None, "file_name": "wdqqgbjylx_1072_${now.datekey}.dat",
                                "target_sep": "^?",
                                "table_cols": "trade_date,loan_no,cooperation_platform,product_no,status,period_number,plan_repay_date,deserved_principal_amount,deserved_interest_amount",
                                "has_header": False, "ok_file": "wdqqgbjylx_1072_${now.datekey}.flg",
                                "lastest_ready_time": "1100", "ftp_polling_sec": 300, "compress_type": None,
                                "compress_passwd": None, "file_encode": "gbk", "alarm_receivers": "jiangyuande",
                                "max_waiting_hours": "1"},
             "sink_configs": {"table_name": "ods_pudong_development_period_repayment_plan_d", "target_sep": "^?",
                              "table_type": "partition", "partition_key": None, "write_mode": "overwrite"}}
        ]
    },
    {
        'scenario': '光大标卡导流数据接入',
        'describe': '联名卡光大标卡导流数据',
        'project_id': 'cbc',
        'source_dsn': 'cbc_gdyh',
        'sink_dsn': 'mart_fspinno_queqiao',
        'tasks': [
            {"name": "meituan_firpos_custlist", "task_type_name": "ftp2mthive",
             "source_configs": {"file_path": "/one-sftp-xy-bank/queqiao/card/gd/$now.datekey",
                                "file_name": "meituan_firpos_custlist_${now.datekey}.txt", "target_sep": "|+|",
                                "table_cols": "phone_num,active_time", "has_header": False,
                                "ok_file": "meituan_firpos_custlist_${now.datekey}.ok",
                                "lastest_ready_time": "0530", "ftp_polling_sec": 300, "compress_type": None,
                                "compress_passwd": None, "file_encode": None, "alarm_receivers": "jiangyuande",
                                "max_waiting_hours": "1"},
             "sink_configs": {"table_name": "meituan_firpos_custlist", "target_sep": "|+|",
                              "table_type": "partition", "partition_key": None, "write_mode": "overwrite"}}
        ]
    },
    {
        'scenario': '房价数据接入-房价网',
        'describe': '房价网数据接入',
        'project_id': 'risk',
        'source_dsn': 'fjw_risk',
        'sink_dsn': 'mart_fspinno_queqiao',
        'tasks': [
            {"name": "ods_fjw_comm_import", "task_type_name": "ftp2mthive",
             "source_configs": {"file_path": '/output', "file_name": "FJW_COMM_${now.datekey}.csv", "target_sep": "@",
                                "table_cols": "id,city,region,name,alias,address,addr_alias,avg_price,rent_price,lng,lat,block,loop_line_info,business_quarter,households,property_fee,car_site_count,property_life,house_property,all_area,build_area,plot_ratio,landscaping_ratio,property_type,build_type,build_structure,finish_building_date,developer,house_type,house_area,deal_num,district_facilitys,school,market,subway,bank,hospital,status",
                                "has_header": True, "ok_file": "FJW_COMM_${now.datekey}.csv.sha512",
                                "lastest_ready_time": "1500", "ftp_polling_sec": 600, "compress_type": None,
                                "compress_passwd": None, "file_encode": None, "alarm_receivers": "jiangyuande",
                                "max_waiting_hours": "1"},
             "sink_configs": {"table_name": "ods_fjw_comm_import", "target_sep": "@", "table_type": "partition",
                              "partition_key": None, "write_mode": "overwrite"}}
        ]
    },
    {
        'scenario': '房价数据接入-58同城',
        'describe': '58同城房价数据接入',
        'project_id': 'risk',
        'source_dsn': '58tc_risk',
        'sink_dsn': 'mart_fspinno_queqiao',
        'tasks': [
            {"name": "ods_58_community_base_info", "task_type_name": "ftp2mthive",
             "source_configs": {"file_path": '/upload',
                                "file_name": "01\u5c0f\u533a\u57fa\u7840\u4fe1\u606f_${now.datekey}.zip",
                                "target_sep": ",",
                                "table_cols": "id,community_id,city,region,business_quarter,name,address,extend_name,aggre_name,avg_price,rent_price,area,data_time",
                                "has_header": True,
                                "ok_file": None,
                                "lastest_ready_time": 1200,
                                "ftp_polling_sec": 600,
                                "compress_type": "7z",
                                "compress_passwd": "meituan",
                                "file_encode": "gbk",
                                "alarm_receivers": "jiangyuande",
                                "max_waiting_hours": "1",
                                "conflict_sep": True
                                },
             "sink_configs": {"table_name": "ods_58_community_base_info", "target_sep": ",",
                              "table_type": "partition", "partition_key": None, "write_mode": "overwrite"}}
        ]
    },
    {
        'scenario': '助贷账务数据测试环境接入',
        'describe': '金融云测试环境测试数据',
        'project_id': 'loan',
        'source_dsn': 'loan_fincloud_test',
        'sink_dsn': 'mart_fspinno_queqiao',
        'tasks': [
            {"name": "queqiaotest_testtable_after_batch", "task_type_name": "ftp2mthive",
             "source_configs": {"file_path": '/one-sftp-xy-bank/queqiao/test/loan/$now.datekey',
                                "file_name": "queqiaotest.testtable_after_batch_${now.datekey}.txt",
                                "target_sep": "special",
                                "table_cols": "id,dept,age,name,create_time,last_login_time",
                                "has_header": False,
                                "ok_file": '.ok',
                                "lastest_ready_time": 1200,
                                "ftp_polling_sec": 60,
                                "compress_type": None,
                                "compress_passwd": None,
                                "file_encode": None,
                                "alarm_receivers": "jiangyuande",
                                "max_waiting_hours": "1"},
             "sink_configs": {"table_name": "queqiaotest_testtable_after_batch", "target_sep": "special",
                              "table_type": "partition", "partition_key": None, "write_mode": "overwrite"}}
        ]
    },
    {
        'scenario': '机构测算中心传输链路测试',
        'describe': '从美团集群同步数据到测算中心集群',
        'project_id': 'risk',
        'source_dsn': 'mart_fspinno_queqiao_test',
        'sink_dsn': 'tgdw',
        'tasks': [
            {"name": "fate_psi_userlist", "task_type_name": "talos2hive",
             "source_configs": {"sql": 'select * from mart_fspinno_queqiao_test.fate_psi_userlist',
                                "uname": "talos_algo_ftplink",
                                "passwd": "VDsbbd#877",
                                "engine": "onesql"},
             "sink_configs": {"table_name": "fate_psi_userlist_with_queqiao", "target_sep": "special",
                              "table_type": "partition", "partition_key": None, "write_mode": "overwrite"}}
        ]
    },
    {
        'scenario': '机构测算中心平安银行数据导入',
        'describe': '从美团集群同步数据到测算中心集群',
        'project_id': 'risk',
        'source_dsn': 'meituan-talos',
        'sink_dsn': 'm03070010',
        'tasks': [
            {"name": "pingan_sample_20220929_mt_11", "task_type_name": "talos2hive",
             "source_configs": {"sql": 'select * from mart_finrisk_bl.pingan_sample_20220929_mt_11',
                                "uname": "talos_algo_ftplink",
                                "passwd": "VDsbbd#877",
                                "engine": "onesql"},
             "sink_configs": {"table_name": "pingan_sample_20220929_mt_11", "target_sep": "special",
                              "null_format": "NULL",
                              "table_type": "full", "partition_key": None, "write_mode": "overwrite"}},
        ]
    },
    {
        'scenario': '机构测算中心平安银行数据导入1',
        'describe': '从美团集群同步数据到测算中心集群',
        'project_id': 'risk',
        'source_dsn': 'meituan-talos',
        'sink_dsn': 'm03070010',
        'tasks': [
            {"name": "pingan_sample_20221108_mt_04", "task_type_name": "talos2hive",
             "source_configs": {"sql": 'select * from mart_finrisk_bl.pingan_sample_20221108_mt_04',
                                "uname": "talos_algo_ftplink",
                                "passwd": "VDsbbd#877",
                                "engine": "onesql"},
             "sink_configs": {"table_name": "pingan_sample_20221108_mt_04", "target_sep": "special",
                              "null_format": "NULL",
                              "table_type": "full", "partition_key": None, "write_mode": "overwrite"}}
        ]
    },
    {
        'scenario': '机构测算中心廊坊银行数据导入',
        'describe': '从美团集群同步数据到测算中心集群',
        'project_id': 'risk',
        'source_dsn': 'meituan-talos',
        'sink_dsn': 'a04721460',
        'tasks': [
            {"name": "cliu_langfang_bank_reject_sample_perf_2022", "task_type_name": "talos2hive",
             "source_configs": {
                 "sql": 'select * from mart_fsp_security_safetmp.cliu_langfang_bank_reject_sample_perf_2022',
                 "uname": "talos_algo_ftplink",
                 "passwd": "VDsbbd#877",
                 "engine": "onesql"},
             "sink_configs": {"table_name": "cliu_langfang_bank_reject_sample_perf_2022", "target_sep": "special",
                              "null_format": "NULL",
                              "table_type": "full", "partition_key": None, "write_mode": "overwrite"}}
        ]
    },
    {
        'scenario': '机构测算中心九江银行数据导入',
        'describe': '从美团集群同步数据到测算中心集群',
        'project_id': 'risk',
        'source_dsn': 'meituan-talos',
        'sink_dsn': 'm04544240',
        'tasks': [
            {"name": "jiujiang_sample_20221121_mt_shf", "task_type_name": "talos2hive",
             "source_configs": {
                 "sql": 'select * from mart_finrisk_bl.jiujiang_sample_20221121_mt_shf',
                 "uname": "talos_algo_ftplink",
                 "passwd": "VDsbbd#877",
                 "engine": "onesql"},
             "sink_configs": {"table_name": "jiujiang_sample_20221121_mt_shf", "target_sep": "special",
                              "null_format": "NULL",
                              "table_type": "full", "partition_key": None, "write_mode": "overwrite"}}
        ]
    },
    {
        'scenario': '机构测算中心九江银行数据导入1',
        'describe': '从美团集群同步数据到测算中心集群1',
        'project_id': 'risk',
        'source_dsn': 'meituan-talos',
        'sink_dsn': 'm04544240',
        'tasks': [
            {"name": "jiujiang_sample_20221123_mt_xfze", "task_type_name": "talos2hive",
             "source_configs": {
                 "sql": 'select * from mart_finrisk_bl.jiujiang_sample_20221123_mt_xfze',
                 "uname": "talos_algo_ftplink",
                 "passwd": "VDsbbd#877",
                 "engine": "onesql"},
             "sink_configs": {"table_name": "jiujiang_sample_20221123_mt_xfze", "target_sep": "special",
                              "null_format": "NULL",
                              "table_type": "full", "partition_key": None, "write_mode": "overwrite"}}
        ]
    },
    {
        'scenario': '联通联合建模PSI&特征数据',
        'describe': '同步PSI与标签数据至fate集群',
        'project_id': 'loan',
        'source_dsn': 'meituan-talos',
        'sink_dsn': 'fate-shuke',
        'tasks': [
            # 26163:7.2675h
            {"name": "fate_psi_userlist", "task_type_name": "talos2fatetask",
             # 13点就绪
             "source_configs": {"sql": 'select hash_id from mart_fspinno.tmp_shuanglin_userlist_20220902',
                                "uname": "talos_algo_ftplink",
                                "passwd": "VDsbbd#877",
                                "engine": "onesql"},
             "sink_configs": {
                 'role': 'guest', 'party_id': 20003, 'task_bucket_cnt': 33,
                 'bucket_cnt': 0, 'null_format': None, 'partition': 16, 'work_mode': 1,
                 'namespace': 'mt_platform', 'tablename': 'queqiao_de_model_psi_sample_${now.datekey}',
                 'cols': 'hash_id', 'target_sep': ',',
                 'job_conf': json.dumps(psi_job_conf), 'job_dsl': json.dumps(psi_job_dsl),
                 'validate_rate': 0.03
             }
             },
            # 4790:1.33055556
            {"name": "fate_feature_userlist", "task_type_name": "talos2fate",
             # 13点就绪
             "source_configs": {
                 "sql": 'select hash_id,label,f16,f294,f17,f290,f15,f12,f310,f303,f316,f26,f27,f40,f18,f1,f300,f253,f313,f289,f48,f261,f6,f311,f52,f44,f327,f283,f11,f234,f254,f305,f250,f187,f14,f315,f301,f2,f304,f293,f5,f53,f306,f286,f63,f302,f13,f181,f291,f102,f101,f279,f287,f36,f33,f209,f211,f68,f56,f297,f29,f98 from mart_fspinno.tmp_shuanglin_userlist_feature_20220907 where partition_date="$now.delta(1).date"',
                 "uname": "talos_algo_ftplink",
                 "passwd": "VDsbbd#877",
                 "engine": "onesql"},
             "sink_configs": {
                 'role': 'guest', 'party_id': 20003,
                 'bucket_cnt': 5, 'null_format': '', 'partition': 16, 'work_mode': 1,
                 'namespace': 'mt_platform', 'tablename': 'queqiao_mt_de_pred_bucket_${now.datekey}',
                 'cols': 'hash_id,label,f16,f294,f17,f290,f15,f12,f310,f303,f316,f26,f27,f40,f18,f1,f300,f253,f313,f289,f48,f261,f6,f311,f52,f44,f327,f283,f11,f234,f254,f305,f250,f187,f14,f315,f301,f2,f304,f293,f5,f53,f306,f286,f63,f302,f13,f181,f291,f102,f101,f279,f287,f36,f33,f209,f211,f68,f56,f297,f29,f98',
                 'target_sep': 'special', 'key_idx': '0'
             }
             }
        ]
    },
    {
        'scenario': '联通联合建模预测数据回传',
        'describe': '同步fate集群的预测结果数据至hive集群',
        'project_id': 'loan',
        'source_dsn': 'fate-shuke',
        'sink_dsn': 'mart_fspinno_queqiao',
        'tasks': [
            # 6h
            {"name": "fate_predict_userlist", "task_type_name": "fatetask2mthive",
             "source_configs": {
                 'role': 'guest', 'party_id': 20003,
                 'cols': 'hash_id,predict_score',
                 'component_name': 'hetero_secure_boost_0', 'data_name': 'train',
                 'bucket_cnt': 5, 'job_conf': json.dumps(predict_job_conf), 'job_dsl': json.dumps(predict_job_dsl),
             },
             "sink_configs": {"table_name": "fate_de_predict_result", "target_sep": "special",
                              "table_type": "partition", "partition_key": None, "write_mode": "overwrite"}
             }
        ]
    },
    {
        'scenario': '工行人群撞库结果入库',
        'describe': '同步fate集群的撞库结果数据至hive集群',
        'project_id': 'cbc',
        'source_dsn': 'tianwen',
        'sink_dsn': 'mart_fspinno_queqiao',
        'tasks': [
            {"name": "tianwen_icbc_psi_mobile_group_0", "task_type_name": "fate2mthive",
             "source_configs": {
                 'role': 'guest', 'party_id': 9999,
                 'cols': 'mobile_sm3',
                 'component_name': 'intersection_0', 'data_name': 'data_16699014932348714',
                 'job_id': '202212011331334945340'
             },
             "sink_configs": {"table_name": "tianwen_icbc_psi_mobile_group_0", "target_sep": "special",
                              "table_type": "partition", "partition_key": None, "write_mode": "overwrite"}
             },
            {"name": "tianwen_icbc_psi_mobile_group_1", "task_type_name": "fate2mthive",
             "source_configs": {
                 'role': 'guest', 'party_id': 9999,
                 'cols': 'mobile_sm3',
                 'component_name': 'intersection_0', 'data_name': 'data_16699105991851810',
                 'job_id': '202212011603195452890'
             },
             "sink_configs": {"table_name": "tianwen_icbc_psi_mobile_group_1", "target_sep": "special",
                              "table_type": "partition", "partition_key": None, "write_mode": "overwrite"}
             },
            {"name": "tianwen_icbc_psi_mobile_group_2", "task_type_name": "fate2mthive",
             "source_configs": {
                 'role': 'guest', 'party_id': 9999,
                 'cols': 'mobile_sm3',
                 'component_name': 'intersection_0', 'data_name': 'data_16699106427114292',
                 'job_id': '202212011604030566490'
             },
             "sink_configs": {"table_name": "tianwen_icbc_psi_mobile_group_2", "target_sep": "special",
                              "table_type": "partition", "partition_key": None, "write_mode": "overwrite"}
             },
            {"name": "tianwen_icbc_psi_mobile_group_3", "task_type_name": "fate2mthive",
             "source_configs": {
                 'role': 'guest', 'party_id': 9999,
                 'cols': 'mobile_sm3',
                 'component_name': 'intersection_0', 'data_name': 'data_16699106785114739',
                 'job_id': '202212011604387939410'
             },
             "sink_configs": {"table_name": "tianwen_icbc_psi_mobile_group_3", "target_sep": "special",
                              "table_type": "partition", "partition_key": None, "write_mode": "overwrite"}
             },
            {"name": "tianwen_icbc_psi_mobile_group_4", "task_type_name": "fate2mthive",
             "source_configs": {
                 'role': 'guest', 'party_id': 9999,
                 'cols': 'mobile_sm3',
                 'component_name': 'intersection_0', 'data_name': 'data_16699107019341081',
                 'job_id': '202212011605022345080'
             },
             "sink_configs": {"table_name": "tianwen_icbc_psi_mobile_group_4", "target_sep": "special",
                              "table_type": "partition", "partition_key": None, "write_mode": "overwrite"}
             },
            {"name": "tianwen_icbc_psi_id_group_0", "task_type_name": "fate2mthive",
             "source_configs": {
                 'role': 'guest', 'party_id': 9999,
                 'cols': 'identify_id_sm3',
                 'component_name': 'intersection_0', 'data_name': 'data_16699107351691848',
                 'job_id': '202212011605355621040'
             },
             "sink_configs": {"table_name": "tianwen_icbc_psi_id_group_0", "target_sep": "special",
                              "table_type": "partition", "partition_key": None, "write_mode": "overwrite"}
             },
            {"name": "tianwen_icbc_psi_id_group_1", "task_type_name": "fate2mthive",
             "source_configs": {
                 'role': 'guest', 'party_id': 9999,
                 'cols': 'identify_id_sm3',
                 'component_name': 'intersection_0', 'data_name': 'data_16699113052132588',
                 'job_id': '202212011615055411490'
             },
             "sink_configs": {"table_name": "tianwen_icbc_psi_id_group_1", "target_sep": "special",
                              "table_type": "partition", "partition_key": None, "write_mode": "overwrite"}
             },
        ]
    },
    {
        'scenario': '浦发隐私计算标签数据回传',
        'describe': 'psi后人群结果关联风险标签回传',
        'project_id': 'cbc',
        'source_dsn': 'meituan-talos',
        'sink_dsn': 'meituan-queqiao-sftp',
        'tasks': [
            {"name": "spdb_psi_feature_data", "task_type_name": "talos2ftp",
             "source_configs": {"sql": 'select * from xxx.xxx'},
             "sink_configs": {
                 "file_name": "xxx_${now.delta(1).datekey}.txt",
                 "file_path": "queqiao/card/spdb_test/${now.delta(1).datekey}",
                 "ok_file": ".ok"
             }
             }
        ]
    },
]


def create_task(clean=True):
    if clean:
        print('clean table apply, task and task_config')
        TaskConfig.delete_by()
        Apply.delete_by()
        Task.delete_by()
    for a in applys:
        source_dsn_name = a.pop('source_dsn')
        source_dsn = Dsn.get_one(name=source_dsn_name)
        sink_dsn_name = a.pop('sink_dsn')
        sink_dsn = Dsn.get_one(name=sink_dsn_name)
        tasks = a.pop('tasks')
        project = Project.get_one(name=a['project_id'])
        engine = Engine.get_one(name='Ftplink')
        a['project_id'] = project.id
        kwargs = {
            **a,
            'security_level': 3,
            'status': ApplyStatus.AGREE.value,
            'create_user': 'jiangyuande',
            'update_user': 'jiangyuande',
        }
        print(f'new apply with kwargs: {kwargs}')
        apply = Apply.new(**kwargs)
        apply.save()
        for t in tasks:
            task_type = TaskType.get_one(name=t.pop('task_type_name'))
            kwargs = {
                'name': f'{t.pop("name")}',
                'apply_id': apply.id,
                'task_type_id': task_type.id,
                'engine_id': engine.id,
                'project_id': project.id,
                'status': TaskStatus.SUCCESS.value,
                'trans_type': TransType.SCHEDULE.value,
                'alarm_receivers': 'jiangyuande',
                'create_user': 'jiangyuande',
                'update_user': 'jiangyuande',
            }
            print(f'new task with kwargs: {kwargs}')
            task = Task.new(**kwargs)
            task.save()

            source_configs = t.pop('source_configs')
            sink_configs = t.pop('sink_configs')

            def create_task_config(cid, did, configs):
                for c in configs.keys():
                    key = c
                    value = configs[c]
                    kwargs = {
                        'key': key,
                        'value': value if value else None,
                        'task_id': task.id,
                        'cid': cid,
                        'create_user': 'jiangyuande',
                        'update_user': 'jiangyuande',
                    }
                    print(f'new task_config with kwargs: {kwargs}')
                    task_config = TaskConfig.new(**kwargs)
                    task_config.save()
                dsn_config = TaskConfig.new(key='dsn', value=did, task_id=task.id, cid=cid,
                                            create_user='jiangyuande', update_user='jiangyuande')
                dsn_config.save()

            create_task_config(task_type.source_id, source_dsn.id, source_configs)
            create_task_config(task_type.sink_id, sink_dsn.id, sink_configs)


table_funcs = [
    create_component,
    create_engine,
    create_task_type,
    create_dsn,
    create_project,
    create_task
]


@click.group()
def dbcli():
    print('execute db command...')


@dbcli.command('create-db')
@click.confirmation_option(prompt='是否删除当前数据库并重新创建？')
def db_create():
    db.drop_all()
    db.create_all()


@click.group()
def tablecli():
    print('execute table command...')


@tablecli.command('create-table')
@click.option('--table', '-t', multiple=True, default=[],
              type=click.Choice([f.__name__.replace('create_', '') for f in table_funcs]))
@click.option('--no-clean', default=True, is_flag=True)
def table_create(table, no_clean):
    clean = no_clean
    print(f'=======table_list: {table}, clean: {clean}=======')
    if not table:
        total_steps = len(table_funcs)
        curr_step = 1
        for func in table_funcs:
            func(clean)
            print(f'#####init progress[{curr_step}/{total_steps}]##### {func}')
            curr_step += 1
    else:
        for table_name in table:
            eval(f'create_{table_name}')(clean)


@click.group()
def confcli():
    print(f'execute config command...')


@confcli.command('init-system-config')
def system_config():
    def parse_config_content(config_file):
        print(f'start read {config_file} and parse comment content')
        contents = osutil.calls(f'cat {config_file} | grep -v "^#" | grep " = " | grep " # "')
        if not isinstance(contents, list):
            contents = [contents]
        print(f'read {len(contents)} config')
        content_map = {}
        for content in contents:
            var = content.split(' = ')[0]
            content = content.split(' # ')[-1]
            content_map[var] = content
            print(f'var: {var}, comment: {content}')
        return content_map

    # todo: 批量保存
    def save_configs(config_map, content_map):
        for key in config_map.keys():
            if key.islower() or key not in content_map:
                continue
            value = config_map[key]
            name = content_map[key]
            # if SystemConfig.exists(key=key, value=value, name=name):
            #     continue
            print(f'save [{key}] with value [{value}], key name: {name}')
            SystemConfig.dump(key=key, value=value, name=name, opt_user='system')

    default_config_content = parse_config_content(f'{CONF_PATH}/default.py')
    env_config_content = parse_config_content(f'{CONF_PATH}/env/{system_env}.py')
    default_config_content.update(env_config_content)
    save_configs(app.config, default_config_content)


cli = click.CommandCollection(sources=[dbcli, tablecli, confcli])
if __name__ == '__main__':
    cli()
