#!/bin/bash
# nohup bin/monitor.sh api,exe > logs/monitor.log 2>&1 &

curr_path=$(cd "$(dirname $0)";pwd)
source $curr_path/.envrc

if [ $# -lt 1 ]
then
    echo "please enter $all_apps"
    exit 1
fi

apps=$1
sys_admin=`cat ${conf_path}/default.py | grep -v "#" | grep "SYS_ADMIN = " | awk -F ' = ' '{print $NF}' | sed 's/"//g'`
port=`cat ${conf_path}/default.py | grep -v "#" | grep "API_PORT = " | awk -F ' = ' '{print $NF}' | sed 's/"//g'`
echo "sys_admin: $sys_admin, port: $port"

user=`whoami`

logger() {
    time=`date +"%Y-%m-%d %H:%M:%S"`
    msg=$1
    echo "[$time] $msg"
}

alarm() {
    msg=$1
    curl -H "Content-Type:application/json" -X POST --data "{\"msg\":\"$msg\",\"receivers\":\"${sys_admin}\"}" http://localhost:$port/api/v1/health/alert
}

check_process_status() {
    for app in `echo $apps | awk -F , '{for(i=1;i<=NF;i++){printf "%s\n",$i}}'`
    do
        me=${app}.${user}
        if [[ ! "$all_apps" =~ "$app" ]]
        then
            continue
        fi

        pid=`cat $project_path/${me}.pid`
        kill -0 $pid
        alive=$?
	    if [ $alive -lt 0 ]
	    then
	        msg="$app has been gone away! restarting ..."
		    logger "$msg"
		    sh ${curr_path}/start.sh $app
	        # 等待启动
	        sleep 3
            alarm "$msg"
        else
            logger "$app still alive"
        fi
    done
}


while true
do
	logger "start check"
	check_process_status
	sleep 300
done