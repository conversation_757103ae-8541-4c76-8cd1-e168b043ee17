curr_path=$(cd "$(dirname $0)";pwd)
project_path=`echo ${curr_path} | sed 's/\/bin//g'`
service_path="${project_path}/queqiao"
conf_path="${project_path}/instance"
log_path="${project_path}/logs"
tmp_path="/tmp/queqiao"
file_path="${project_path}/local"
all_apps=`ls ${service_path} | grep "\.py" | grep -v "__" | sed 's/.py//g'`
# echo "curr_path: ${curr_path}, project_path: ${project_path}, service_path: ${service_path}, conf_path: ${conf_path}"