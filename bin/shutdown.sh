curr_path=$(cd "$(dirname $0)";pwd)
source $curr_path/.envrc

if [ $# -lt 1 ]
then
    echo "please enter $all_apps"
    exit 1
fi

apps=$1
user=`whoami`

shutdown() {
    for app in `echo $apps | awk -F , '{for(i=1;i<=NF;i++){printf "%s\n",$i}}'`
    do
        me=${app}.${user}
        if [[ ! "$all_apps" =~ "$app" ]]
        then
            echo "$app not in bootable app list: ${all_apps}"
            continue
        fi

        echo "[stopping ${app}]..."
        pid=`cat $project_path/$me.pid`
        echo "kiling ${app} with pid: $pid"
        cnt=`ps  -ef | grep $pid | grep -v "grep" | wc -l`
        if [ $cnt -gt 0 ]
        then
            echo "get $cnt $app process"
            ps  -ef | grep $pid | grep -v "grep" | awk '{print $2}' | xargs kill -9
            echo "${app} has been stopped"
        else
            echo "nothing found for $app"
        fi
    done
}

echo "=====shutdown====="
shutdown