"""
Author: xiaohei
Date: 2022/12/1
Email: <EMAIL>
Host: xiaohei.info
"""
import multiprocessing
import os
import sys

sys.path.append(os.path.dirname(sys.path[0]))
from queqiao.util.mt.talos import TalosClient
from queqiao.api import app

sql_list = []
for i in range(0, 2):
    sql = f'''
    select mobile_sha256 from (
select mobile_sha256,
       mobile_group
  from mart_fspinno_creditcard_test.testzyt
 where mobile_sha256 is not null
 group by mobile_sha256,
          mobile_group
          )a where mobile_group={i}
    '''
    sql_list.append(sql)

client = TalosClient(uname='talos_algo_ftplink', passwd='VDsbbd#877', engine='onesql', download_max=5000000,
                     fetch_buffer=1000000)
client.open()
client.set_default_col_sep(',')
client.set_default_save_path('/tmp')

from pytalos.client import AsyncTalosClient

username = 'talos_algo_ftplink'
password = 'VDsbbd#877'
engine = 'onesql'
client = AsyncTalosClient(username=username, password=password)
client.open_session()
sql = '''
select mobile_group,count(1) from (select mobile_md5,
      substr(mobile_md5, 1, 1) as mobile_group
 from mart_fspinno.umeng_first_loan_score_psi_sample where partition_date='2023-11-20'
)a group by mobile_group order by mobile_group;
'''
qid = client.submit(dsn='hdim', statement=sql, engine=engine)
client.get_query_info(qid)['status']
res = client.download(qid)
for i in res:
    print(i)

# i = 0
# for sql in sql_list:
#     query_result = client.query(sql)
#     print(f'==={i}===')
#     print(query_result)
#     i += 1


def download(sql):
    query_result = client.query(sql)
    print(query_result)


pool = multiprocessing.Pool(processes=5)
pool_outputs = pool.map(download, sql_list)
pool.close()
pool.join()
print('Pool: ', pool_outputs)
