#!/bin/bash
curr_path=$(cd "$(dirname $0)";pwd)
source $curr_path/.envrc

kinit_time=`date +"%Y-%m-%d %H:%M:%S"`
export KRB5_CONFIG=/opt/meituan/hadoop/etc/hadoop/krb5.conf

base_path=$(cd "$(dirname "$0")";pwd)
hostnameis=`hostname`
mt_project_group=`cat ${project_path}/instance/env/mtprod.py | grep 'MT_PROJECT_GROUP =' | awk -F '=' '{print $NF}' | awk -F "'" '{print $2}'`
echo "hostname:${hostnameis},mt_project_group:${mt_project_group}"

kinit_cmd="kinit -kt /etc/hadoop/keytabs/${mt_project_group}.keytab ${mt_project_group}/${hostnameis}@SANKUAI.COM"
echo "$kinit_cmd"
eval $kinit_cmd
rcode=$?
echo "[${kinit_time}] kinit return ${rcode}"