# coding=utf-8
from instance.default import *

# 系统环境设置（sftp、mysql信息等）
ENV = "prod"

# ---------------- mysql ---------------
# todo: 主从高可用配置
# zebra参考: https://km.sankuai.com/page/139799849
SQLALCHEMY_DATABASE_URI = 'mysql+pymysql://{username}:{password}@{host}:{port}/{db}?charset=utf8'.format(
    username='rds_queqiao', password='oEuhSnvjZb!Y0u', host='findataplat.vip.sankuai.com', port='5002', db='queqiao')
SQLALCHEMY_DATABASE_URI_SLAVE = 'mysql+pymysql://{username}:{password}@{host}:{port}/{db}?charset=utf8'.format(
    username='rds_queqiao', password='oEuhSnvjZb!Y0u', host='findataplat.vip.sankuai.com', port='5002', db='queqiao')

SQLALCHEMY_BINDS = dict(
    master=SQLALCHEMY_DATABASE_URI
)
# 性能监控插件
DEBUG = False

MT_PROJECT_GROUP = 'hadoop-fspinno-queqiao'  # 美团项目组名

REDIS_HOST = 'vm-yg-ftplink-app02'
# broker(消息中间件来接收和发送任务消息)
CELERY_BROKER_URL = f'redis://:{REDIS_PWD}@{REDIS_HOST}:{REDIS_PORT}/3'
# backend(存储worker执行的结果)
CELERY_RESULT_BACKEND = f'redis://:{REDIS_PWD}@{REDIS_HOST}:{REDIS_PORT}/4'
CELERY_WORKERS = 1000  # celery默认worker数量
CELERY_TASK_QUEUES = {  # celery任务队列配置
    # vm-yg-ftplink-app02
    '***********': ['normal'],
    # '***********': ['normal', 'bigtask', 'timing'],
    # vm-mt-ftplink-app02
    '************': ['normal', 'bigtask', 'timing'],
    # vm-mt-ftplink-app03
    '*************': ['normal', 'test']
}

# sparkcmd
SPARK_DEPLOY_MODE = 'cluster'  # spark部署方式
SPARK_DEFAULT_DRIVER_MEMORY = '512M'  # spark driver内存
SPARK_DEFAULT_NUM_EXECUTORS = '10'  # spark executor数量
SPARK_DEFAULT_EXECUTOR_MEMORY = '1G'  # spark executor内存
SPARK_DEFAULT_EXECUTOR_CORES = '1'  # spark executor核心数
SPARK_YARN_QUEUE = 'root.zw06.hadoop-fincom.etl'  # spark yarn提交队列
SPARK_SUBMIT_CONFIGS = '--files $SPARK_HOME/conf/hive-site.xml,$SPARK_HOME/conf/.hiverc --conf spark.local.hiverc=true'  # spark 任务提交附加参数
KERBEROS_KEYTAB = f'/etc/hadoop/keytabs/{MT_PROJECT_GROUP}.keytab'  # keytab文件路径
KERBEROS_USER = f'{MT_PROJECT_GROUP}/<EMAIL>'  # keytab认证用户

# api
API_HOST = '***********'  # api后端服务地址
CHECK_IMPORT_RESULT = True  # 是否启用入库结果检查（耗时增加）
