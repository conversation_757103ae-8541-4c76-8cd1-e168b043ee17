# coding=utf-8
from instance.default import *

PYTHON_ENV = '/app/mtdp/env/ftplink/bin'

# 系统环境设置（sftp、mysql信息等）
ENV = "tgprod"

# ---------------- mysql ---------------

SQLALCHEMY_DATABASE_URI = 'mysql+pymysql://{username}:{password}@{host}:{port}/{db}?charset=utf8'.format(
    username='ftplink', password='password', host='wg-cdp-prod-vip', port='3306', db='queqiao')
SQLALCHEMY_DATABASE_URI_SLAVE = 'mysql+pymysql://{username}:{password}@{host}:{port}/{db}?charset=utf8'.format(
    username='ftplink', password='password', host='wg-cdp-prod-vip', port='3306', db='queqiao')

SQLALCHEMY_BINDS = dict(
    master=SQLALCHEMY_DATABASE_URI
)
# 性能监控插件
DEBUG = False
REDIS_HOST = '**************'
# broker(消息中间件来接收和发送任务消息)
CELERY_BROKER_URL = f'redis://:{REDIS_PWD}@{REDIS_HOST}:{REDIS_PORT}/1'
# backend(存储worker执行的结果)
CELERY_RESULT_BACKEND = f'redis://:{REDIS_PWD}@{REDIS_HOST}:{REDIS_PORT}/2'
CELERY_WORKERS = 1000  # celery默认worker数量
# CELERY_TASK_QUEUES = {
# celery任务队列配置
# gh-ftplink-app01
# '************': ['normal'],
# '************': ['normal', 'bigtask', 'timing'],
# gh-ftplink-app02
# '*************': ['normal', 'bigtask', 'timing']
# }

# pyhive
KERBEROS_KEYTAB = '/app/mtdp/env/hive.keytab'  # keytab文件路径
KERBEROS_USER = 'hive'  # keytab认证用户
KERBEROS_SERVICE_NAME = 'hive'  # kerberos服务名
PYHIVE_HOST = 'wg-cdp-prod-vip.prod.aif.com'  # hs地址
PYHIVE_PORT = '10000'  # hs端口
BEELINE_U = '***************************************************************/<EMAIL>'  # beeline连接字符串
DEFAULT_PUSHER = 'File'
ALARM_FILE_MSG_TEMP = '{ctime}#aicappprodqq_鹊桥传输系统#核心服务#QUEQIAO_ERROR#{msg}#{receivers}'  # 文件告警模板
ALARM_FILE_PATH = '/tmp/queqiao_filepusher.log'  # 告警记录文件
DEFAULT_SCHEDULER = 'Azkaban'
AZKABAN_BIN_DEPLOY_PATH = '/app/mtdp/functions/common/bin'  # Azkaban脚本部署路径
DEFAULT_APPROVER = 'Inner'

LDAP_REQUIRED_CONFIGS = {  # LDAP连接配置
    "LDAP_SERVER_POOL": ['wg-cdp-prod-kdc01.prod.aif.com', 'wg-cdp-prod-kdc02.prod.aif.com'],
    "LDAP_SERVER_PORT": "389",
    "LDAP_SEARCH_DN": "uid=ldapbind,cn=users,cn=accounts,dc=prod,dc=aif,dc=com",
    "LDAP_SEARCH_PASSWORD": "Ab123456",
    "LDAP_SEARCH_BASE": "cn=accounts,dc=prod,dc=aif,dc=com",
    "LDAP_GROUP_SEARCH_BASE": "cn=groups,cn=accounts,dc=prod,dc=aif,dc=com",
    "LDAP_ALLOW_GROUPS": None
}
TG_CBC_TL_ONLINE_BANKDW = {'qddw': '************',  # 天宫银行数据分发映射
                           'gldw': '************', 'jsdw': '************', 'hzdw': '************',
                           'shdw': '************', 'zjkdw': '************',
                           'tjdw': '************', 'dydw': '************', 'gzdw': '************',
                           'hkdw': '************', 'jjdw': '************', 'xmdw': '************',
                           'harbindw': '************', 'whrcdw': '************',
                           }
HIVE_TMP_NULL_FORMAT = 'NULL'
