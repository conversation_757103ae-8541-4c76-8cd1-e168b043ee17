# coding=utf-8
from instance.default import *

# 系统环境设置（sftp、mysql信息等）
ENV = "local"

# ---------------- mysql ---------------

SQLALCHEMY_DATABASE_URI = 'mysql+pymysql://{username}:{password}@{host}:{port}/{db}?charset=utf8'.format(
    username='root', password='root', host='127.0.0.1', port='3306', db='queqiao')
SQLALCHEMY_DATABASE_URI_SLAVE = 'mysql+pymysql://{username}:{password}@{host}:{port}/{db}?charset=utf8'.format(
    username='root', password='root', host='127.0.0.1', port='3306', db='queqiao')

SQLALCHEMY_BINDS = dict(
    master=SQLALCHEMY_DATABASE_URI
)
# 性能监控插件
DEBUG = True

REDIS_HOST = '*************'
# broker(消息中间件来接收和发送任务消息)
CELERY_BROKER_URL = f'redis://:{REDIS_PWD}@{REDIS_HOST}:{REDIS_PORT}/1'
# backend(存储worker执行的结果)
CELERY_RESULT_BACKEND = f'redis://:{REDIS_PWD}@{REDIS_HOST}:{REDIS_PORT}/2'

# sparkcmd
SPARK_DEPLOY_MODE = 'client'  # spark部署方式
SPARK_DEFAULT_DRIVER_MEMORY = '512M'  # spark driver内存
SPARK_DEFAULT_NUM_EXECUTORS = '1'  # spark executor数量
SPARK_DEFAULT_EXECUTOR_MEMORY = '1G'  # spark executor内存
SPARK_DEFAULT_EXECUTOR_CORES = '1'  # spark executor核心数
SPARK_YARN_QUEUE = 'default'  # spark yarn提交队列

# pyhive
KERBEROS_KEYTAB = '/opt/hive.keytab'  # keytab文件路径
KERBEROS_USER = 'hive'  # keytab认证用户
KERBEROS_SERVICE_NAME = 'hive'  # kerberos服务名
PYHIVE_HOST = 'vm-hh-ftplink-app-test02'  # hs地址
PYHIVE_PORT = '10000'  # hs端口
BEELINE_U = '***********************************************************/<EMAIL>'  # beeline连接字符串
# for test
ALARM_FILE_MSG_TEMP = '{ctime}#aicappprodqq_鹊桥传输系统#核心服务#QUEQIAO_ERROR#{msg}#{receivers}'  # 文件告警模板
ALARM_FILE_PATH = '/tmp/queqiao_filepusher.log'  # 告警记录文件
AZKABAN_BIN_DEPLOY_PATH = '/app/mtdp/functions/common/bin'  # Azkaban脚本部署路径

LDAP_REQUIRED_CONFIGS = {  # LDAP连接配置
    "LDAP_SERVER_POOL": ['yp-ftplink-app-test12.mt'],
    "LDAP_SERVER_PORT": "389",
    "LDAP_SEARCH_DN": "uid=ldapbind,cn=users,cn=accounts,dc=mt,dc=com",
    "LDAP_SEARCH_PASSWORD": "password",
    "LDAP_SEARCH_BASE": "cn=accounts,dc=mt,dc=com",
    "LDAP_GROUP_SEARCH_BASE": "cn=groups,cn=accounts,dc=mt,dc=com",
    "LDAP_ALLOW_GROUPS": None
}
