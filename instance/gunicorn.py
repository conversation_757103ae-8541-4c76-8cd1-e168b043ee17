"""
Author: xiaohei
Date: 2021/5/8
Email: <EMAIL>
Host: xiaohei.info
"""
import multiprocessing

#
from instance.default import API_PORT, LOG_PATH

# workers = multiprocessing.cpu_count() * 2 + 1
workers = 4
bind = f'0.0.0.0:{API_PORT}'
loglevel = 'debug'

timeout = 3 * 60  # 3 minutes
keepalive = 24 * 60 * 60  # 1 day

max_requests = 0
worker_class = 'gevent'

# LOG_FILE = '../logs/gunicorn/'
LOG_FILE = LOG_PATH
accesslog = f'{LOG_PATH}/gunicorn_access.log'
errorlog = f'{LOG_PATH}/gunicorn_error.log'

access_log_format = '%(h)s %({X-Forwarded-For}i)s %(l)s %(t)s "%(r)s" %(s)s %(b)s %(p)s %(T)s.%(D)-6ss "%(f)s" "%(a)s"'
