# coding=utf-8
import getpass
import os
import socket

# 配置项条件：
# 1）存放程序启动时必须加载的信息
# 2）永远不会变的信息
# 3）因业务需求改变配置而需要重启时，移动到配置中心
# 注释格式：
# var = value # 这里是注释
# 不可跨行
# # ------基础配置------

HOSTNAME = socket.gethostname()
IPADDR = socket.gethostbyname(HOSTNAME)
APPKEY = 'com.sankuai.ftplink.app'
CONF_PATH = os.path.split(os.path.realpath(__file__))[0]
PROJECT_PATH = os.path.abspath(f'{CONF_PATH}/..')
SERVICE_PATH = f'{PROJECT_PATH}/queqiao'
BIN_PATH = f'{PROJECT_PATH}/bin'
LOG_PATH = f'{PROJECT_PATH}/logs'
EXE_LOG_PATH = f'{LOG_PATH}/execution'
TMP_FOLDER = '/opt/meituan/tmp/queqiao'
WINDOWS_LINE_ENDING = '\r\n'
UNIX_LINE_ENDING = '\n'
SYS_ADMIN = "jiangyuande"  # 系统管理员

# ------通用变量-----
QUEQIAO_ENV = 'QUEQIAO_ENV'
system_env = os.environ.get(QUEQIAO_ENV, None)
if not system_env:
    raise Exception(f'{QUEQIAO_ENV} not set!')

API_HOST = IPADDR  # api后端服务地址
API_PORT = '8090'  # api后端服务端口
API_VERSION = 'v1'  # api后端接口版本
SYSTEM_USER = getpass.getuser()
DEFAULT_PUSHER = 'Daxiang'  # 默认消息推送渠道
DEFAULT_SCHEDULER = 'Cantor'  # 默认对接的调度系统
DEFAULT_APPROVER = 'Daxiang'  # 默认的审批流
LOCALFILE_DEFAULT_SEP = '\x01'  # 本地文件默认分隔符

# ------大象推送配置------
# 开发者接口文档：https://km.sankuai.com/page/30194536
# 各语言demo：https://km.sankuai.com/page/250341591
# 公众号后台配置：https://mp.neixin.cn/basicsetting
DXPS_PUB_ID = 137475618020  # 公众号ID
DXPS_CLIENT_ID = '024201S05703e241'  # 开发者信息的Key
DXPS_CLIENT_SECRET = 'effe0b74acfd39093eaf468de8d022bf'  # 开发者信息的Token
DXPS_HOST = 'https://xmapi.vip.sankuai.com'  # 线上服务基地址
DXPS_URL_PATH = '/api/pub/push'  # 向个人推送消息的API地址
# ------大象快审配置-------
AUDIT_SERVER_APPKEY = 'com.sankuai.xm.workbench.approval'  # 大象快审服务端appkey
AUDIT_UDB_APPKEY = 'com.sankuai.xm.udb'  # udb服务端appkey
AUDIT_UDB_TENANT = {'test': 'sankuai.info', 'prod': 'meituan.com'}  # 大象快审环境租户信息
AUDIT_IDL_FILE = f'{PROJECT_PATH}/ext/thrift/xm/xm-approval.thrift'
AUDIT_IDL_FILE_UDB = f'{PROJECT_PATH}/ext/thrift/udb/UdbService.thrift'
# -----persona人群画像配置-----
PERSONA_APPKEY = 'com.sankuai.nlpml.udm.utvsapi'
PERSONA_IDL_FILE = f'{PROJECT_PATH}/ext/thrift/utvsapi/persona_crowd_api.thrift'

# ------日志配置------
DEFAULT_LOGGER = 'api'
from queqiao.log.format import RequestFormatter
from queqiao.log import format as log_format

LOGGING_SETTINGS = dict(
    version=1,
    disable_existing_loggers=False,
    formatters={
        'request':
            {
                '()': f'{log_format.__name__}.{RequestFormatter.__name__}',
                'format':
                    '[%(asctime)s %(levelname)s/%(processName)s/%(process)s %(module)s:%(lineno)d] '
                    '[%(remote_addr)s|%(user)s|%(url)s]: %(message)s'
            },
        'backend':
            {
                '()': f'{log_format.__name__}.{RequestFormatter.__name__}',
                'format':
                    '[%(asctime)s %(levelname)s/%(processName)s/%(process)s %(module)s:%(lineno)d] '
                    '%(message)s'
            },
        'default':
            {
                '()': 'logging.Formatter',
                'format':
                    '[%(asctime)s %(levelname)s/%(processName)s/%(process)s %(module)s:%(lineno)d] '
                    '%(message)s',
            }
    },
    handlers={
        'stdout':
            {
                'class': 'logging.StreamHandler',
                'level': 'DEBUG',
                'formatter': 'default',
            }
    },
    loggers={
        'console': {
            'handlers': ['stdout'],
            'level': 'DEBUG'
        }
    },
    # 覆盖mtthrift设置的root logger SteamHandler
    root={
        'handlers': []
    }
)

log_level = 'INFO' if 'prod' in system_env else 'DEBUG'
# print(f'system_env: {system_env}, log_level: {log_level}')
apps = [f.replace('.py', '') for f in os.listdir(SERVICE_PATH) if f.endswith('.py') and not f.startswith('__')]
for app in apps:
    LOGGING_SETTINGS['handlers'][f'{app}_daily_file'] = {
        'class': 'queqiao.log.handler.DailyRotatingFileHandler',
        'filename': f'{LOG_PATH}/queqiao-{app}.{SYSTEM_USER}.log',
        'level': log_level,
        'formatter': 'request' if app == 'api' else 'backend'
    }
    LOGGING_SETTINGS['loggers'][app] = {
        'handlers': [f'{app}_daily_file'],
        'level': log_level,
    }

# ------mysql配置------
# http://flask-sqlalchemy.pocoo.org/2.3/config/
SQLALCHEMY_POOL_SIZE = 200
SQLALCHEMY_MAX_OVERFLOW = 500
SQLALCHEMY_POOL_RECYCLE = 600
SQLALCHEMY_ENGINE_OPTIONS = {
    'pool_pre_ping': True,
    "pool_recycle": 600,
}
# SQLALCHEMY_ECHO = True
SQLALCHEMY_TRACK_MODIFICATIONS = False
# SQLALCHEMY_COMMIT_ON_TEARDOWN = True

# -----redis配置------
REDIS_HOST = 'localhost'  # redis主机名
REDIS_PORT = 6379  # redis端口号
REDIS_PWD = 'password'  # redis连接密码
REDIS_DB = 0  # redis默认db
REDIS_KEY_EXPIRE = 7200  # redis配置项默认过期时间

# -------api-------
# SSO验证
SSO_SALT = '0d8c65ea4257a0701b398ba5627aab3ad747a7bd9ed043be54b5794e20595fc94661b6f9751c9c0c'
SSO_LOGIN_URL = 'https://ssosv.sankuai.com/sson/login'
SSO_VIP_HOST = 'https://sso.vip.sankuai.com'
SSO_LOGOUT_URL = SSO_VIP_HOST + '/sson/oauth2.0/logout'
SSO_ACCESS_TOKEN_URL = SSO_VIP_HOST + '/sson/oauth2.0/access-token'
SSO_USER_INFO_API = SSO_VIP_HOST + '/open/api/session/userinfo'
# 访问api-sso使用的token
SSO_API_TOKEN = {
    'client_id': 'bba64f11b1',
    'client_secret': 'd58ce1e3a06748c89a96cd81398eb06b',
}

# 外部系统连接信息
ORG_SERVICE_INFO = {
    'host': 'https://org2.vip.sankuai.com',
    'client_id': 'bba64f11b1',
    'client_secret': '7cad7d8af4714431b70ec9635c6c77ab',
}

USER_AUTH_TYPE = 'LDAP' if SYSTEM_USER != 'sankuai' else 'MTSSO'  # 默认认证组件

DXKS_SINGLE_TEMPLATE = [  # 大象快审-单条审批模板
    {"seq": 1, "num": "{apply_id}"},
    {"seq": 2, "text": "{create_time}"},
    {"seq": 3, "text": "{create_user}"},
    {"seq": 4, "text": "{leader}"},
    {"seq": 5, "text": "{org}"},
    {"seq": 6, "text": "{scenario}"},
    {"seq": 7, "text": "{describe}"},
    {"seq": 8, "selected": "{security_level}"},
    {"seq": 9, "text": "{information}"},
    {"seq": 10, "text": "{task_name}"},
    {"seq": 11, "text": "{where2where}"},
    {"seq": 12, "text": "{trans_type}"},
    {"seq": 13, "num": "{result_size}"},
    {"seq": 14, "text": "{engine}"},
    {"seq": 15, "text": "{execution_meta}"},
    {"seq": 16, "text": "{project}"}
]
DXKS_BATCH_TEMPLATE = [  # 大象快审-批量审批模板
    {"seq": 1, "num": "{apply_id}"},
    {"seq": 2, "text": "{create_time}"},
    {"seq": 3, "text": "{create_user}"},
    {"seq": 4, "text": "{leader}"},
    {"seq": 5, "text": "{org}"},
    {"seq": 6, "text": "{scenario}"},
    {"seq": 7, "text": "{describe}"},
    {"seq": 8, "selected": "{security_level}"},
    {"seq": 9, "text": "{information}"},
    {"seq": 10, "text": "{task_detail}"},
    {"seq": 11, "text": "{project}"}
]

DXKS_TEMPLATES = {  # 大象快审审批模板-单条/批量
    'single': DXKS_SINGLE_TEMPLATE,
    'batch': DXKS_BATCH_TEMPLATE
}

DXKS_TEMPLATE_IDS = {  # 大象快审审批模板-用户组与模板id映射(区分不同审批人)
    'prod': {'queqiao_user_bs': {'single': 5852, 'batch': 5851}, 'default': {'single': 5913, 'batch': 5914}},
    'test': {'queqiao_user_bs': {'single': 1379, 'batch': 1378}, 'default': {'single': 1381, 'batch': 1380}},
}

COOKIE_SALT = 'ARD15Xtf'  # 用户token加密salt
ADMIN_GROUP = 'queqiao_admin'  # LDAP管理员用户组
MT_DEPARTMENT_GROUPS = {'数据平台及工具': ['queqiao_user_sys'],  # 美团部门对应的鹊桥用户组
                        '联名卡数据': ['queqiao_user_rd'],
                        '数字微贷数据': ['queqiao_user_rd'],
                        '风险数据': ['queqiao_user_rd']}
# exe
# 本地文件存储路径
LOCAL_FILE_FOLDER = f'{PROJECT_PATH}/local'
LOCAL_DATA_FOLDER = [LOCAL_FILE_FOLDER]
WAC_SLEEP_SEC = 60  # wac后端程序默认休眠时间
DATAX_DEFAULT_PARALLELISM = 4  # datax默认并行度
DATAX_DEFAULT_ERROR_PERCENTAGE = 0  # datax默认错误容忍度

# -----celery-----
# http://docs.celeryproject.org/en/latest/userguide/configuration.html
# https://docs.celeryq.dev/en/stable/
# broker(消息中间件来接收和发送任务消息)
CELERY_BROKER_URL = f'redis://:{REDIS_PWD}@{REDIS_HOST}:{REDIS_PORT}/1'
# backend(存储worker执行的结果)
CELERY_RESULT_BACKEND = f'redis://:{REDIS_PWD}@{REDIS_HOST}:{REDIS_PORT}/2'
# 设置时间参照，不设置默认使用的UTC时间
CELERY_TIMEZONE = 'Asia/Shanghai'
# 指定任务的序列化
CELERY_TASK_SERIALIZER = 'json'
# 指定执行结果的序列化
CELERY_RESULT_SERIALIZER = 'json'
CELERY_RETRY_DEFAULT_TIMES = 0  # celery默认重试次数
CELERY_RETRY_DEFAULT_INTERVAL = 3 * 60  # celery默认重试间隔（秒）
CELERY_TASK_QUEUES = {  # celery任务队列配置
    '127.0.0.1': ['normal', 'bigtask', 'timing']
}
CELERY_WORKERS = 100  # celery默认worker数量

DATAX_HOME = f'{PROJECT_PATH}/ext/engine/datax'  # datax路径

TALOS_DEFAULT_DSN = 'hdim'  # talos默认dsn
TALOS_USERNAME = "talos_algo_ftplink"  # talos默认用户名
TALOS_PASSWORD = "VDsbbd#877"  # talos默认密码
# hive/presto/onesql
TALOS_ENGINE = 'onesql'  # talos默认引擎
TALOS_DOWNLOAD_MAX = 5000000  # talos默认最大下载量
TALOS_FETCH_BUFFER = 200000  # talos批量下载模式单次获取条数
TALOS_WARN_MAX = 20000000  # talos告警阈值
TALOS_WARN_MAX_GB = 6  # talos告警阈值（GB）

ALARM_HIVE_DOWNLOAD_MAX_NUM = 20000000  # Hive最大告警下载量
ALARM_HIVE_DOWNLOAD_MAX_GB = 6  # Hive最大告警下载量(GB)
DEFAULT_PARTITION_KEY = 'partition_date'  # Hive默认分区键
HIVE_TMP_NULL_FORMAT = '\\N'  # Hive临时表默认的空值存储配置
CHECK_IMPORT_RESULT = True  # 是否启用入库结果检查（耗时增加）
CHECK_META_MAX_BYTES = 1024 * 1024 * 1024 * 10  # 超出字节大小后不对文件元数据进行校验（耗时增加）

# wtp配置
WTP_HOME_DIR = '/opt/meituan/apps/wtp'  # wtp部署路径
WTP_JAR_PATH = f'{WTP_HOME_DIR}/wtp.jar'  # wtp可执行程序路径

# talos任务配置
TALOS_QUEUED_MAX = 40  # running任务超出此数值后talos任务将会等待
TALOS_QUEUED_WAIT_TIME = 60  # talos任务等待时间s
