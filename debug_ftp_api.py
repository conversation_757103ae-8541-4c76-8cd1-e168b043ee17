#!/usr/bin/env python
# coding=utf-8

"""
FTP API 调试工具
用于测试和排查 FTP API 接口问题
"""

import requests
import json
import sys
import time

def test_api_endpoint(base_url, endpoint, params=None, headers=None):
    """测试 API 端点"""
    url = f"{base_url}{endpoint}"
    
    print(f"\n{'='*60}")
    print(f"测试接口: {endpoint}")
    print(f"完整URL: {url}")
    print(f"参数: {params}")
    print(f"{'='*60}")
    
    try:
        response = requests.get(url, params=params, headers=headers, timeout=30)
        
        print(f"状态码: {response.status_code}")
        print(f"响应头: {dict(response.headers)}")
        
        if response.status_code == 200:
            try:
                data = response.json()
                print(f"响应数据: {json.dumps(data, indent=2, ensure_ascii=False)}")
            except:
                print(f"响应内容: {response.text}")
        else:
            print(f"错误响应: {response.text}")
            
        return response
        
    except requests.exceptions.Timeout:
        print("❌ 请求超时 - 可能是服务器无响应")
        return None
    except requests.exceptions.ConnectionError:
        print("❌ 连接错误 - 请检查服务是否启动")
        return None
    except Exception as e:
        print(f"❌ 请求异常: {str(e)}")
        return None

def check_service_status(base_url):
    """检查服务状态"""
    print("🔍 检查服务状态...")
    
    # 测试基础健康检查
    health_endpoints = [
        "/api/v1/health/",
        "/api/v1/doc/",
        "/"
    ]
    
    for endpoint in health_endpoints:
        try:
            response = requests.get(f"{base_url}{endpoint}", timeout=5)
            if response.status_code == 200:
                print(f"✅ 服务正常 - {endpoint} 返回 200")
                return True
        except:
            continue
    
    print("❌ 服务可能未启动或不可访问")
    return False

def test_ftp_apis(base_url="http://localhost:5000"):
    """测试 FTP API 接口"""
    
    print("🚀 FTP API 调试工具")
    print("=" * 60)
    
    # 检查服务状态
    if not check_service_status(base_url):
        print("\n💡 调试建议:")
        print("1. 检查服务是否启动: ./bin/status.sh")
        print("2. 启动服务: ./bin/start.sh")
        print("3. 检查端口是否被占用: lsof -i :5000")
        return
    
    # 准备测试数据
    test_cases = [
        {
            "name": "WC by ID (无认证测试)",
            "endpoint": "/api/v1/ftp/wc/id/1",
            "params": {"file_path": "/test/file.txt"}
        },
        {
            "name": "WC by Name (无认证测试)",
            "endpoint": "/api/v1/ftp/wc/test-dsn",
            "params": {"file_path": "/test/file.txt"}
        },
        {
            "name": "WC Direct (无认证测试)",
            "endpoint": "/api/v1/ftp/wc/direct",
            "params": {
                "protocol": "sftp",
                "ip": "127.0.0.1",
                "port": "22",
                "username": "test",
                "password": "test",
                "file_path": "/test/file.txt"
            }
        },
        {
            "name": "HEADN by ID (无认证测试)",
            "endpoint": "/api/v1/ftp/headn/id/1",
            "params": {"file_path": "/test/file.txt", "n": "10"}
        }
    ]
    
    # 执行测试
    for test_case in test_cases:
        response = test_api_endpoint(
            base_url, 
            test_case["endpoint"], 
            test_case["params"]
        )
        
        if response and response.status_code == 401:
            print("ℹ️  接口需要认证 - 这是正常的")
        elif response and response.status_code == 404:
            print("ℹ️  路由未找到 - 可能是路由注册问题")
        elif response is None:
            print("⚠️  无响应 - 可能是服务器内部错误")
        
        time.sleep(1)  # 避免请求过快
    
    print(f"\n📋 调试检查清单:")
    print("1. ✅ 服务状态检查完成")
    print("2. ✅ API 路由测试完成")
    print("3. ✅ 基础连通性测试完成")
    
    print(f"\n🔧 进一步调试步骤:")
    print("1. 查看服务日志:")
    print("   tail -f logs/queqiao.log")
    print("   tail -f logs/error.log")
    
    print("\n2. 检查路由注册:")
    print("   访问 http://localhost:5000/api/v1/doc/ 查看所有接口")
    
    print("\n3. 测试认证:")
    print("   先调用登录接口获取 token，然后在请求头中添加:")
    print("   Authorization: Bearer <your_token>")
    
    print("\n4. 检查数据库连接:")
    print("   确保 DSN 配置正确，数据库可访问")
    
    print("\n5. 逐步调试:")
    print("   a) 先测试不需要 FTP 连接的接口（如参数验证）")
    print("   b) 再测试需要 DSN 查询的接口")
    print("   c) 最后测试需要 FTP 连接的接口")

def show_log_monitoring_commands():
    """显示日志监控命令"""
    print(f"\n📊 实时日志监控命令:")
    print("=" * 60)
    
    commands = [
        {
            "desc": "监控所有 FTP API 日志",
            "cmd": "tail -f logs/queqiao.log | grep -E '\\[(WC_|HEADN_)'"
        },
        {
            "desc": "监控错误日志",
            "cmd": "tail -f logs/queqiao.log | grep ERROR"
        },
        {
            "desc": "监控特定接口日志 (WC)",
            "cmd": "tail -f logs/queqiao.log | grep '\\[WC_'"
        },
        {
            "desc": "监控特定接口日志 (HEADN)",
            "cmd": "tail -f logs/queqiao.log | grep '\\[HEADN_'"
        },
        {
            "desc": "监控连接相关日志",
            "cmd": "tail -f logs/queqiao.log | grep -E '(connection|connect|FTP)'"
        }
    ]
    
    for i, cmd_info in enumerate(commands, 1):
        print(f"{i}. {cmd_info['desc']}:")
        print(f"   {cmd_info['cmd']}")
        print()

if __name__ == "__main__":
    if len(sys.argv) > 1:
        base_url = sys.argv[1]
    else:
        base_url = "http://localhost:5000"
    
    test_ftp_apis(base_url)
    show_log_monitoring_commands()
    
    print(f"\n🎯 使用方法:")
    print(f"python debug_ftp_api.py [base_url]")
    print(f"例如: python debug_ftp_api.py http://localhost:8080")
